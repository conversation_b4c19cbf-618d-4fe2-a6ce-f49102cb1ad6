# Kafka 错误日志功能使用指南

## 概述

框架现已集成 Kafka 错误日志功能，所有使用 `kit/driver` 框架的服务都可以自动获得 Kafka 错误日志能力。该功能会自动将 `Error`、`Fatal`、`Panic` 级别的日志发送到 Kafka，便于集中监控和告警。

## 功能特性

- ✅ **自动集成**：使用 driver 框架的服务无需额外代码即可启用
- ✅ **选择性发送**：只发送 Error、Fatal、Panic 级别的日志
- ✅ **配置驱动**：通过配置文件控制是否启用和相关参数
- ✅ **结构化日志**：发送的日志包含完整的元数据和字段信息
- ✅ **故障隔离**：Kafka 发送失败不影响正常日志记录
- ✅ **优雅关闭**：服务停止时自动清理 Kafka 连接

## 快速开始

### 1. 配置文件设置

在你的服务配置文件中添加 `kafka-producer` 配置节：

```yaml
# 基础服务配置
rpc_server_name: "my-service"
rpc_port: 8080
log_level: "info"

# Kafka 错误日志配置
kafka-producer:
  brokers: ["localhost:9092"]
  topic: "my-service-error-logs"  # 可选，不配置会自动生成
  timeout: 10
```

### 2. 启动服务

使用标准的 driver 框架启动服务：

```go
package main

import (
    "git.keepfancy.xyz/back-end/frameworks/kit/driver"
    "github.com/sirupsen/logrus"
)

type MyService struct{}

func (s *MyService) Init() error {
    // 服务初始化逻辑
    return nil
}

func (s *MyService) Start() error {
    // 服务启动逻辑
    return nil
}

func (s *MyService) Stop() error {
    // 服务停止逻辑
    return nil
}

func main() {
    service := &MyService{}
    driver.Run(service)  // Kafka 日志功能会自动启用
}
```

### 3. 记录日志

正常使用 logrus 记录日志，Error 级别及以上的日志会自动发送到 Kafka：

```go
// 这些日志不会发送到 Kafka
logrus.Debug("调试信息")
logrus.Info("一般信息")
logrus.Warn("警告信息")

// 这些日志会发送到 Kafka
logrus.Error("发生错误")
logrus.WithFields(logrus.Fields{
    "user_id": 12345,
    "action":  "payment",
    "amount":  100.50,
}).Error("支付处理失败")

logrus.Fatal("致命错误")  // 注意：这会终止程序
```

## 配置详解

### 基础配置

```yaml
kafka-producer:
  brokers: ["kafka1:9092", "kafka2:9092"]  # Kafka 集群地址（必需）
  topic: "service-error-logs"              # 日志主题（可选）
  timeout: 10                              # 连接超时时间（秒）
```

### 安全配置

```yaml
kafka-producer:
  brokers: ["kafka1:9092"]
  
  # SASL 认证
  sasl_mechanism: "SCRAM"      # 支持 PLAIN, SCRAM
  sasl_username: "username"
  sasl_password: "password"
  sasl_algorithm: "sha-256"    # SCRAM 算法
  
  # SSL/TLS 加密
  use_ssl: true
  ssl_cert_file: "/path/to/client.crt"
  ssl_key_file: "/path/to/client.key"
  ssl_ca_location: "/path/to/ca.crt"
  ssl_insecure_skip_verify: false
```

### 主题命名规则

如果不配置 `topic`，系统会自动生成主题名称：

1. 如果配置了 `rpc_server_name`：使用 `{rpc_server_name}-error-logs`
2. 如果没有配置服务名称：使用 `service-error-logs`

建议的主题命名格式：
- `{service-name}-error-logs`
- `{service-name}-{environment}-error-logs`

## Kafka 消息格式

发送到 Kafka 的日志消息采用 JSON 格式：

```json
{
  "timestamp": "2025-08-05T20:06:46.597576+08:00",
  "level": "error",
  "message": "支付处理失败",
  "fields": {
    "user_id": 12345,
    "action": "payment",
    "amount": 100.50,
    "error_code": "PAY001"
  },
  "caller": "/path/to/service/payment.go:123",
  "source": "logdog"
}
```

字段说明：
- `timestamp`: 日志时间戳（RFC3339Nano 格式）
- `level`: 日志级别（error/fatal/panic）
- `message`: 日志消息内容
- `fields`: 通过 `logrus.WithFields()` 添加的字段
- `caller`: 调用者信息（文件路径和行号）
- `source`: 固定值 "logdog"

## 启动日志示例

服务启动时会显示 Kafka 日志功能的初始化信息：

```
INFO[2025-08-05T20:06:46+08:00] kafka sender topic my-service-error-logs initial word success
INFO[2025-08-05T20:06:46+08:00] Kafka log hook added successfully levels="[error fatal panic]" topic=my-service-error-logs
INFO[2025-08-05T20:06:46+08:00]  - [tcgo]Kafka error logging initialized successfully
INFO[2025-08-05T20:06:46+08:00]  - [tcgo]using kafka brokers: [localhost:9092]
INFO[2025-08-05T20:06:46+08:00]  - [tcgo]using kafka topic: my-service-error-logs
INFO[2025-08-05T20:06:46+08:00]  - [tcgo]Error, Fatal, and Panic logs will be sent to Kafka
```

如果没有配置 Kafka 或配置有误：

```
DEBUG[2025-08-05T20:06:46+08:00]  - [tcgo]kafka-producer not configured, skipping Kafka error logging
# 或
WARN[2025-08-05T20:06:46+08:00]  - [tcgo]kafka-producer.brokers not configured or empty, skipping Kafka error logging
# 或
WARN[2025-08-05T20:06:46+08:00]  - [tcgo]failed to initialize Kafka error logging: connection failed
```

## 最佳实践

### 1. 环境配置

**开发环境**：
```yaml
kafka-producer:
  brokers: ["localhost:9092"]
  # 使用默认主题即可
```

**生产环境**：
```yaml
kafka-producer:
  brokers: ["kafka-prod-1:9092", "kafka-prod-2:9092"]
  topic: "myservice-prod-error-logs"
  timeout: 30
  sasl_mechanism: "SCRAM"
  sasl_username: "${KAFKA_USERNAME}"
  sasl_password: "${KAFKA_PASSWORD}"
  use_ssl: true
```

### 2. 日志记录建议

```go
// ✅ 好的做法：使用结构化字段
logrus.WithFields(logrus.Fields{
    "user_id":    userID,
    "request_id": requestID,
    "error_code": "AUTH_FAILED",
    "ip_address": clientIP,
}).Error("用户认证失败")

// ❌ 避免：在消息中包含敏感信息
logrus.Error("用户认证失败，密码：" + password)  // 不要这样做

// ✅ 好的做法：使用错误码便于分类
logrus.WithField("error_code", "DB_CONNECTION_FAILED").Error("数据库连接失败")
```

### 3. 监控和告警

建议监控以下指标：
- Kafka 连接状态
- 错误日志发送成功率
- 特定错误码的出现频率
- 错误日志的时间分布

## 故障排除

### 常见问题

1. **Kafka 连接失败**
   ```
   Failed to fire hook: failed to send log to kafka: dial tcp: connection refused
   ```
   - 检查 Kafka 服务是否运行
   - 验证网络连接和防火墙设置
   - 确认 brokers 地址配置正确

2. **认证失败**
   ```
   Failed to fire hook: failed to send log to kafka: SASL authentication failed
   ```
   - 检查用户名和密码是否正确
   - 确认 SASL 机制配置正确
   - 验证用户是否有写入权限

3. **主题不存在**
   ```
   Failed to fire hook: failed to send log to kafka: topic does not exist
   ```
   - 确认主题已创建
   - 检查是否启用了自动创建主题
   - 验证用户是否有创建主题的权限

### 调试方法

1. **启用调试日志**：
   ```yaml
   log_level: "debug"
   ```

2. **检查配置**：
   ```go
   logrus.Debugf("Kafka config: %+v", viper.Get("kafka-producer"))
   ```

3. **测试连接**：
   ```bash
   # 使用 kafka 命令行工具测试
   kafka-console-producer.sh --broker-list localhost:9092 --topic test-topic
   ```

## 注意事项

1. **性能影响**：发送日志到 Kafka 会有一定的性能开销，但采用异步发送机制，影响较小
2. **存储成本**：错误日志会占用 Kafka 存储空间，建议设置合适的保留策略
3. **网络依赖**：需要确保服务与 Kafka 集群之间的网络连通性
4. **安全考虑**：生产环境必须启用认证和加密，避免敏感信息泄露

## 版本兼容性

- 要求 Go 1.16+
- 兼容 Kafka 2.0+
- 支持 SASL/PLAIN、SASL/SCRAM 认证
- 支持 SSL/TLS 加密连接
