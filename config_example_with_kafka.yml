# 服务配置示例 - 包含 Kafka 错误日志配置
# 这个文件展示了如何在服务中配置 Kafka 错误日志功能

# 基础服务配置
rpc_server_name: "my-service"
rpc_port: 8080
http_port: 8081
env_mode: "debug"

# 日志配置
log_level: "info"          # 日志级别：trace, debug, info, warn, error, fatal, panic
log_write: true            # 是否写入文件
log_dir: "./logs"          # 日志文件目录
log_json: false            # 是否使用 JSON 格式输出到控制台

# Kafka 生产者配置 - 用于错误日志
# 只有配置了这个节点，才会启用 Kafka 错误日志功能
kafka-producer:
  # Kafka 集群地址（必需）
  brokers: 
    - "localhost:9092"
    - "localhost:9093"
    - "localhost:9094"
  
  # 日志主题（可选）
  # 如果不配置，将使用 "{rpc_server_name}-error-logs" 作为默认主题
  # 如果 rpc_server_name 也没有配置，则使用 "service-error-logs"
  topic: "my-service-error-logs"
  
  # 连接超时时间（秒）
  timeout: 10
  
  # SASL 认证配置（可选）
  sasl_mechanism: ""        # 支持 "PLAIN", "SCRAM" 或留空
  sasl_username: ""
  sasl_password: ""
  sasl_algorithm: ""        # SCRAM 算法：sha-256 或 sha-512
  
  # SSL/TLS 配置（可选）
  use_ssl: false
  ssl_cert_file: ""         # 客户端证书文件路径
  ssl_key_file: ""          # 客户端私钥文件路径
  ssl_ca_location: ""       # CA 证书文件路径
  ssl_insecure_skip_verify: false  # 是否跳过 SSL 证书验证

# 数据库配置示例
mysql_list:
  default:
    user: "root"
    passwd: "password"
    addr: "localhost:3306"
    db: "my_database"
    charset: "utf8mb4"
    params: "parseTime=true&loc=Local"

# Redis 配置示例
redis_list:
  default:
    addr: "localhost:6379"
    password: ""
    db: 0

# 其他配置...
consul_addr: "localhost:8500"

---
# 生产环境配置示例
# 在生产环境中，通常需要更严格的安全配置

# 生产环境 Kafka 配置示例
kafka-producer-production:
  brokers:
    - "kafka-prod-1:9092"
    - "kafka-prod-2:9092"
    - "kafka-prod-3:9092"
  
  topic: "production-service-errors"
  timeout: 30
  
  # 生产环境通常需要 SASL 认证
  sasl_mechanism: "SCRAM"
  sasl_username: "service-user"
  sasl_password: "${KAFKA_PASSWORD}"  # 从环境变量读取
  sasl_algorithm: "sha-256"
  
  # 生产环境通常启用 SSL
  use_ssl: true
  ssl_cert_file: "/etc/ssl/certs/kafka-client.crt"
  ssl_key_file: "/etc/ssl/private/kafka-client.key"
  ssl_ca_location: "/etc/ssl/certs/kafka-ca.crt"
  ssl_insecure_skip_verify: false

---
# 开发环境简化配置示例
# 开发环境可以使用最简配置

# 最简 Kafka 配置
kafka-producer-dev:
  brokers: ["localhost:9092"]
  # topic 将自动使用服务名称生成

# 最简服务配置
rpc_server_name: "dev-service"
rpc_port: 8080
log_level: "debug"
log_write: false  # 开发环境可以只输出到控制台

---
# 配置说明和最佳实践

# 1. Kafka 错误日志功能说明：
#    - 只有 Error、Fatal、Panic 级别的日志会发送到 Kafka
#    - Debug、Info、Warn 级别的日志不会发送到 Kafka
#    - 发送到 Kafka 的日志包含完整的字段信息和调用者信息
#    - 如果 Kafka 发送失败，不会影响正常的日志记录

# 2. 主题命名建议：
#    - 使用服务名称作为前缀："{service-name}-error-logs"
#    - 区分环境："{service-name}-{env}-error-logs"
#    - 统一格式便于日志聚合和分析

# 3. 安全配置建议：
#    - 生产环境必须启用 SASL 认证和 SSL 加密
#    - 敏感信息（密码、证书）使用环境变量或密钥管理系统
#    - 定期轮换认证凭据

# 4. 性能配置建议：
#    - 根据网络环境调整 timeout 值
#    - 监控 Kafka 连接状态和发送延迟
#    - 考虑使用专用的错误日志主题，避免与业务消息混合

# 5. 监控和告警：
#    - 监控错误日志的发送成功率
#    - 设置关键错误的实时告警
#    - 定期检查日志主题的消费情况
