package dict

// 操作系统
const (
	OSLinux   = "linux"   // linux
	OSDarwin  = "darwin"  // darwin macos
	OSWindows = "windows" // windows
)

// BanStatus 账号状态
type BanStatus int

const (
	AccountStatusActive    BanStatus = iota // 未封停，正常号
	AccountStatusBanLogin                   // 禁止登录
	AccountStatusBanGame                    // 禁止游戏
	AccountStatusBanLogout                  // 已注销
)

// 灰度状态
const (
	GrayStatusNone   = 0 // none
	GrayStatusNormal = 1 // 正常
	GrayStatusGray   = 2 // 灰度
)

// 框架相关参数
const (
	SysProjectName         = "tcgo"                             // 框架英文名
	SysDefaultConfig       = "./config.yml"                     // 缺省config文件路径及名称,在执行路径下
	SysDefaultAuthKey      = "tcgo"                             // 认证密钥
	SysDefaultIpApi        = "http://myexternalip.com/raw"      // 获取公网IP地址API
	SysDefaultDirAccessLog = "/access_log/"                     // grpc日志文件夹名称
	SysDefaultDirGrpc      = "grpc_client"                      // grpc client日志文件名
	SysDefaultHttpPort     = "80"                               // 默认HTTP端口
	SysDefaultConsulAddr   = "127.0.0.1:8500"                   // 默认Consul地址
	SysDefaultRpcIP        = "127.0.0.1"                        // 默认RPC IP地址
	SysDefaultRpcPort      = "9090"                             // 默认RPC Port
	SysDefaultProductID    = 1                                  // 默认产品ID
	SysDefaultChannelID    = 1001                               // 默认Channel
	SysDefaultLang         = 1                                  // 默认语言 (英语)
	SysDefaultHttpRTimeout = 30                                 // 默认 http read timeout
	SysDefaultHttpWTimeout = 30                                 // 默认 http write timeout
	SysDefaultForbidPath   = "../bin/assets/forbid.txt"         // 屏蔽字配置路径
	SysDefaultGeoDbPath    = "../bin/assets/GeoLite2-City.mmdb" // 地理位置数据库配置路径
)
