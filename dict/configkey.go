// Package dict 配置文件大类 Word
package dict

// 日志配置Key Word
const (
	ConfigLogLevel  = "log_level"   // 日志级别
	ConfigLogWrite  = "log_write"   // 只有配置log_write生效，后续日志文件配置才有用
	ConfigLogDir    = "log_dir"     // 日志目录
	ConfigErrSqlDir = "err_sql_dir" // 日志SQL目录
	ConfigLogJson   = "log_json"    // json日志格式
	ConfigKafkaUrl  = "kafka_url"   // kafka地址，逗号分割
)

// RPC配置Key
const (
	ConfigRpcAddr          = "rpc_addr"        // rpc地址
	ConfigRpcPort          = "rpc_port"        // rpc端口
	ConfigRpcServerName    = "rpc_server_name" // rpc服务名称
	ConfigRpcServerTags    = "rpc_server_tags" // rpc服务TAG标签 Normal or Gray
	ConfigRpcIsDye         = "isDye"           // rpc isDye
	ConfigRpcDyeTag        = "dyeTag"          // rpc dyeTag
	ConfigRpcServerWeights = "weight"          // rpc 权重配置
)

// 服务类配置Key
const (
	ConfigHttpAddr        = "http_addr"             // HTTP_ADDR
	ConfigHttpPort        = "http_port"             // HTTP端口
	ConfigTcpPort         = "tcp_port"              // TCP端口
	ConfigReadmeHelp      = "help"                  // flag help
	ConfigPrometheusClose = "prometheus_close"      // 关闭Prometheus收集
	ConfigRpcPltHttpAddr  = "platform_http_address" // 平台地址
	ConfigRpcPhpSecret    = "php_secret"            // php平台secret
	ConfigGinEnvMode      = "env_mode"              // Gin env mode
)

// Mysql类配置Key
const (
	ConfigMysqlList    = "mysql_list" // MYSQL列表Key
	ConfigMysqlUser    = "user"       // mysql用户Key
	ConfigMysqlPwd     = "passwd"     // mysql密码Key
	ConfigMysqlAddr    = "addr"       // mysql地址Key
	ConfigMysqlDb      = "db"         // mysql数据库名称Key
	ConfigMysqlParams  = "params"     // mysql参数Key
	ConfigMysqlCharset = "charset"    // mysql字符集Key
)

// Redis类配置Key
const (
	ConfigRedisAddr   = "redis_addr"   // redis地址Key
	ConfigRedisPwd    = "redis_passwd" // redis密码Key
	ConfigRedisList   = "redis_list"   // redis列表Key
	ConfigRedisDbAddr = "addr"         // redis列表子元素 - 地址
	ConfigRedisDbPwd  = "passwd"       // redis列表子元素 - 密码
	ConfigRedisSETEX  = "SETEX"        // redis SETEX
	ConfigRedisGET    = "GET"          // redis GET
	ConfigRedisEXPIRE = "EXPIRE"       // redis EXPIRE
	ConfigRedisDEL    = "DEL"          // redis DEL
)

// 其它配置Key
const (
	ConfigConsulAddr        = "consul_addr"      // Consul地址Key
	ConfigNsqDAddr          = "nsqd_addr"        // NSQ地址Key
	ConfigNsqMaxInflight    = "nsq_max_inflight" // NSQ消息吞吐量
	ConfigNsqHttpAddr       = "nsqd_http_addr"   // NSQ http地址
	ConfigNsqLookUpdAddress = "nsqlookupd_addrs" // NSQLOOKUPD地址Key
	ConfigMessageRouter     = "message_router"   // 消息路由Key
	ConfigPassingWeight     = "PassingWeight"    // 负载均衡 Address.Attributes Key
)
