package timex

import (
	"fmt"
	"testing"
	"time"
)

func TestNextDay(t *testing.T) {
	wt := NewWorkTimeModel(TIME_MODEL_OPERATE)
	now := time.Now()
	nextDay := wt.NextDay(now, -1)
	fmt.Println(nextDay)
}

func TestNextWeek(t *testing.T) {
	wt := NewWorkTimeModel(TIME_MODEL_OPERATE)
	now := time.Date(2025, 6, 8, 5, 55, 0, 0, time.Local)
	fmt.Println(now)
	nextWeek, _ := wt.NextWeek(now, time.Saturday, 0)
	fmt.Println(nextWeek)
}

func TestNextMonth(t *testing.T) {
	wt := NewWorkTimeModel(TIME_MODEL_OPERATE)
	now := time.Now()
	nextMonth, err := wt.NextMonth(now, 1, 1)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(nextMonth)
}

func TestGetFlushTs(t *testing.T) {
	wt := NewWorkTimeModel(TIME_MODEL_OPERATE)
	now := time.Date(2025, 6, 6, 4, 55, 0, 0, time.Local)
	fmt.Println(now)
	start, end, err := wt.GetFlushTs(now, 3, 1)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(time.Unix(start, 0), time.Unix(end, 0))
}
