package timex

import (
	"fmt"
	"time"
)

type WorkTime struct {
	dayStartHour int // 当天开始时间
}

type TimeModel int

const (
	TIME_MODEL_REAL    TimeModel = 0 // 真实刷新时间 凌晨0点
	TIME_MODEL_OPERATE TimeModel = 5 // 运营刷新时间 凌晨5点
)

func NewWorkTimeModel(timeModel TimeModel) *WorkTime {
	return &WorkTime{
		dayStartHour: int(timeModel),
	}
}

// 设置运刷新时间
func NewWorkTime(dayStartHour int) *WorkTime {
	return &WorkTime{
		dayStartHour: dayStartHour,
	}
}

// 转换为目标工作时间 （unix 不可用，仅作用于时间判断)
func (w *WorkTime) GetWorkTime(t time.Time) time.Time {
	if t.Hour() < w.dayStartHour {
		return t.AddDate(0, 0, -1)
	}
	return t
}

// 是否同一天
func (w *WorkTime) IsSameDay(t1 time.Time, t2 time.Time) bool {
	w1 := w.GetWorkTime(t1)
	w2 := w.GetWorkTime(t2)
	return w1.Year() == w2.Year() && w1.Month() == w2.Month() && w1.Day() == w2.Day()
}

// 获取下一天的初始时间
func (w *WorkTime) NextDay(t time.Time, next int) time.Time {
	wt := w.GetWorkTime(t)
	wt = wt.AddDate(0, 0, next)
	// 重置为当天开始时间
	wt = time.Date(wt.Year(), wt.Month(), wt.Day(), w.dayStartHour, 0, 0, 0, wt.Location())
	return wt
}

// 获取下一周的初始时间
func (w *WorkTime) NextWeek(t time.Time, targetWeekday time.Weekday, next int) (time.Time, error) {
	if targetWeekday > 6 || targetWeekday < 0 {
		return time.Time{}, fmt.Errorf("targetWeekday must be between 0 and 6")
	}
	wt := w.GetWorkTime(t)
	weekday := wt.Weekday()
	daysUntilTarget := (int(targetWeekday) - int(weekday))
	if wt.Weekday() != 0 && wt.Weekday() < targetWeekday {
		next -= 1
	}
	wt = wt.AddDate(0, 0, (next)*7+daysUntilTarget)
	// 重置为当天开始时间
	wt = time.Date(wt.Year(), wt.Month(), wt.Day(), w.dayStartHour, 0, 0, 0, wt.Location())
	return wt, nil
}

// 获取下一个月的初始时间
func (w *WorkTime) NextMonth(t time.Time, day int, next int) (time.Time, error) {
	wt := w.GetWorkTime(t)
	if day < 1 || day > 31 {
		return time.Time{}, fmt.Errorf("day must be between 1 and 31")
	}
	if day > 28 {
		// 判断是否是2月
		if wt.Month() == 2 {
			if day > 29 {
				return time.Time{}, fmt.Errorf("%d month has no %d day", wt.Month(), day)
			}
		}
		if wt.Month() == 4 || wt.Month() == 6 || wt.Month() == 9 || wt.Month() == 11 {
			if day > 30 {
				return time.Time{}, fmt.Errorf("%d month has no %d day", wt.Month(), day)
			}
		}
	}
	if wt.Day() < day {
		next -= 1
	}
	wt = wt.AddDate(0, next, 0)
	wt = time.Date(wt.Year(), wt.Month(), day, w.dayStartHour, 0, 0, 0, wt.Location())
	return wt, nil
}

// 获取刷新时间段
func (w *WorkTime) GetFlushTs(now time.Time, flushTimeType int64, flushTime int64) (start int64, end int64, err error) {
	// 读不到proto，手动写
	// FTT_PERMANENT = 0; // 永久
	// FTT_MONTH     = 1; // 每月
	// FTT_WEEK      = 2; // 每周
	// FTT_DAY       = 3; // 每天
	switch flushTimeType {
	case 0:
		return 0, 0, nil
	case 1:
		startT, err := w.NextMonth(now, int(flushTime), 0)
		if err != nil {
			return 0, 0, err
		}
		endT, err := w.NextMonth(now, int(flushTime), 1)
		if err != nil {
			return 0, 0, err
		}
		return startT.Unix(), endT.Unix(), nil
	case 2:
		startT, err := w.NextWeek(now, time.Weekday(flushTime), 0)
		if err != nil {
			return 0, 0, err
		}
		endT, err := w.NextWeek(now, time.Weekday(flushTime), 1)
		if err != nil {
			return 0, 0, err
		}
		return startT.Unix(), endT.Unix(), nil
	case 3:
		return w.NextDay(now, 0).Unix(), w.NextDay(now, 1).Unix(), nil
	default:
		return 0, 0, fmt.Errorf("invalid flush time type: %d", flushTimeType)
	}
}
