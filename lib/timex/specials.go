package timex

import (
	"fmt"
	"github.com/araddon/dateparse"
	"time"
)

// DaysZeroTimeMs n天之后的0点时间(毫秒)
func DaysZeroTimeMs(n int) int64 {
	ms := Millisecond() + int64(n*secondsPerDay*msPerSecond)
	return DayZeroMillisecond(ms)
}

// LocalTimeString 获取当前时间
func LocalTimeString(layout string) string {
	return Now().Format(layout)
}

// LocalTimePreNDayString 获取前N天的日期列表
func LocalTimePreNDayString(dayNum int, layout string) []string {
	now := Now()
	list := make([]string, 0, dayNum)
	for i := 0; i < dayNum; i++ {
		preNum := i + 1
		tmpTime := now.AddDate(0, 0, -preNum)
		preDate := tmpTime.Format(layout)
		list = append(list, preDate)
	}
	return list
}

// TimeMSToString 毫秒转成时间格式
func TimeMSToString(ms int64) string {
	return TimeToString(ms/msPerSecond, time.DateTime)
}

func TimeMsToDateString(ms int64) string {
	return TimeToString(ms/msPerSecond, FormatStrDay)
}

// WeekDayZeroTimeMs 本周第某天零点时间（毫秒）,每周从周日开始算起
func WeekDayZeroTimeMs(day time.Weekday) int64 {
	if day > time.Saturday || day < time.Sunday {
		return -1
	}
	t := Now()
	if offsetDuration != 0 {
		t = t.Add(offsetDuration)
	}
	today := t.Weekday()
	delta := day - today
	return DaysZeroTimeMs(int(delta))
}

// NextPeriodWeekDayZeroTimeMs 当前WeekDay到目标星期几还要几天
func NextPeriodWeekDayZeroTimeMs(day time.Weekday) int64 {
	t := Now()
	if offsetDuration != 0 {
		t = t.Add(offsetDuration)
	}
	today := t.Weekday()
	if today == day {

		return DaysZeroTimeMs(7)
	} else {
		delta := (day - today + 7) % 7
		return DaysZeroTimeMs(int(delta))
	}
}

// MonthDayZeroTime 几个月之后的某一天零时
// month为第n个月之后（前）,0为本月，正数为之后，负数为之前
// date 第几天
func MonthDayZeroTime(month, date int) int64 {
	t := Now()
	if offsetDuration != 0 {
		t = t.Add(offsetDuration)
	}
	t = t.AddDate(0, month, 0)
	y, m, _ := t.Date()
	str := fmt.Sprintf("%d-%02d-%02d 00:00:00", y, m, date)
	return ParseToTimeUnixMS(str)
}

func WeekDay(ms int64) (weekDay time.Weekday) {
	weekDay = time.Unix(ms/msPerSecond, 0).In(loc).Weekday()
	return
}

// DayZeroInterval begin的零点到end的零点间隔几天
func DayZeroInterval(begin, end int64) int64 {
	begin = DayZeroMillisecond(begin*msPerSecond) / msPerSecond // 当天0点
	end = DayZeroMillisecond(end*msPerSecond) / msPerSecond     // 当天0点
	interval := end - begin
	return interval / secondsPerDay
}

// TransformLocal 时间字符串转换为服务器时间戳
// UTC ：也称ISO 8601时间格式。世界统一时间
// @return time.Time 转换后的Time
// @return string 转换后的时间戳格式化字符串
func TransformLocal(timeStr string) (time.Time, string, error) {
	tLocal, err := dateparse.ParseAny(timeStr)
	if err != nil {
		return time.Time{}, "", err
	}

	return tLocal, tLocal.Format(time.DateTime), nil
}

// GetNDayAgo N天前
func GetNDayAgo(n time.Duration) time.Time {
	d, _ := time.ParseDuration("-24h")
	d1 := Now().Add(d * n)
	return d1
}
