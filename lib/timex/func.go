package timex

import (
	"regexp"
	"strconv"

	"git.keepfancy.xyz/back-end/frameworks/kit/rds"
	"github.com/robfig/cron/v3"

	"time"
)

const (
	testTimeKey = "test:setnowtime"
)

func GetTestTimeKey() string {
	return testTimeKey
}

// DelTestTime 删除测试时间
func DelTestTime() {
	rds.Del(rds.DEFAULT, GetTestTimeKey())
}

// SetTestTime 设置测试时间
func SetTestTime(nowTime int64) {
	rds.Set(rds.DEFAULT, GetTestTimeKey(), nowTime, 24*time.Hour)
}

// ListernTestTime 监听测试时间
func ListernTestTime() {
	c := cron.New()
	// 每5秒监听变化
	c.AddFunc("*/5 * * * * *", func() {
		flushTestTime()
	})
	c.Start()
	// 立刻刷新一次
	flushTestTime()
}

func flushTestTime() {
	newOffsetStr := rds.Get(rds.DEFAULT, GetTestTimeKey())
	if newOffsetStr != "" {
		newOffset, _ := strconv.ParseInt(newOffsetStr, 10, 64)
		SetOffset(newOffset)
	} else {
		// 过期/删除 重置偏移量
		SetOffset(0)
	}
}

// TimeSub t1-t2间隔几天
func TimeSub(t1, t2 time.Time) int {
	t1 = time.Date(t1.Year(), t1.Month(), t1.Day(), 0, 0, 0, 0, time.Local)
	t2 = time.Date(t2.Year(), t2.Month(), t2.Day(), 0, 0, 0, 0, time.Local)

	return int(t1.Sub(t2).Hours() / 24)
}

// IsSameDay 是否同一天
func IsSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

// IsSameWeek 是否是同一周
func IsSameWeek(t1, t2 time.Time) bool {
	return IsSameDay(t1, t2) && t1.Weekday() == t2.Weekday()
}

// IsSameMonth 是否是同一个月
func IsSameMonth(t1, t2 time.Time) bool {
	y1, m1, _ := t1.Date()
	y2, m2, _ := t2.Date()
	return y1 == y2 && m1 == m2
}

// IsSameYear 是否是同一年
func IsSameYear(t1, t2 time.Time) bool {
	y1, _, _ := t1.Date()
	y2, _, _ := t2.Date()
	return y1 == y2
}

func FromUnixTime(t int64) string {
	tm := time.Unix(t, 0)
	return FormatTime(tm)
}

func FromUnixNanoTime(t int64) string {
	return time.Unix(t/1000000000, t%1000000000).Format(time.RFC3339Nano)
}

var (
	offset         int64
	offsetDuration time.Duration
	reg            = regexp.MustCompile(`[-: /]+`)
	loc            = time.Local
)

const (
	msPerSecond    = 1000
	secondsPerDay  = 86400
	NaturalDayMS   = msPerSecond * secondsPerDay
	NaturalDay     = secondsPerDay
	NaturalWeekDay = NaturalDay * 7
)

// SetOffset 设置偏移量
func SetOffset(ts int64) {
	offset = ts
}

func GetOffset() int64 {
	return offset
}

func Now() time.Time {
	return time.Now().In(loc).Add(time.Duration(offset) * time.Second)
}

// Millisecond 获取当前时间戳（毫秒）
func Millisecond() int64 {
	return Now().UnixNano() / 1e6
}

// Unix 获取当前时间戳（秒）
func Unix() int64 {
	return Now().Unix()
}

// DayZeroMillisecond 给定的时间戳，计算当天零点的时间戳
func DayZeroMillisecond(ms int64) int64 {
	ft := FormatStrDay
	t := time.Unix(ms/msPerSecond, 0).In(loc)
	val := t.Format(ft)
	t, _ = time.ParseInLocation(ft, val, loc)
	return t.UnixNano() / 1e6
}

// TimeToString 时间戳转换成时间格式
func TimeToString(sec int64, layout string) string {
	return time.Unix(sec, 0).In(loc).Format(layout)
}

// ParseToTimeUnixMS 字符串时间转时间戳毫秒  格式： yyyy-mm-dd hh:MM:ss| yyyy-mm-dd
func ParseToTimeUnixMS(tf string) int64 {
	t, err := parseTime(tf)
	if err != nil {
		return -1
	}
	return t.UnixNano() / 1e6
}

// ParseToTimeUnix 时间戳转换成时间格式,秒
func ParseToTimeUnix(tf string) int64 {
	t, err := parseTime(tf)
	if err != nil {
		return -1
	}
	return t.Unix()
}

