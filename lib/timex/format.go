package timex

import (
	"time"
)

const FormatStrDay = "20060102"     // 格式化Day
const FormatStrMonth = "200601"     // 格式化Month
const FormatStrDay2 = time.DateOnly // 格式化Day 2
const FormatStrYear = "2006"        // 格式化year
const layoutTimeWithoutSeparator = "20060102150405"

func FormatTime(t time.Time) string {
	return t.Format(time.DateTime)
}

func parseTime(tf string) (t time.Time, err error) {
	str := reg.ReplaceAllString(tf, "")
	if len(str) == len(FormatStrDay) {
		str += "000000"
	}
	t, err = time.ParseInLocation(layoutTimeWithoutSeparator, str, loc)
	return
}

func FormatTime2Day(t time.Time) string {
	return t.Format(FormatStrDay)
}