package ddm

import (
	"encoding/json"
	"testing"
)

type message struct {
	Name      IDName   `json:"name"`
	Mobile    Mobile   `json:"mobile"`
	IDCard    IDCard   `json:"id_card"`
	PassWord  PassWord `json:"password"`
	Email     Email    `json:"email"`
	BankCard1 BankCard `json:"bank_card_1"`
	BankCard2 BankCard `json:"bank_card_2"`
	BankCard3 BankCard `json:"bank_card_3"`
}

func TestMarshalJSON(t *testing.T) {
	msg := new(message)
	msg.Name = IDName("尹艳龙")
	msg.Mobile = Mobile("***********")
	msg.IDCard = IDCard("123456785252525252")
	msg.PassWord = PassWord("123456")
	msg.Email = Email("<EMAIL>")
	msg.BankCard1 = BankCard("****************")
	msg.BankCard2 = BankCard("*****************")
	msg.BankCard3 = BankCard("5555666687456985298")

	marshal, _ := json.Marshal(msg)
	t.Log(string(marshal))
}
