package lru

import (
	"fmt"
	"testing"

	"github.com/sirupsen/logrus"
)

type String string

func (d String) GetKey() string {
	// 模拟从Redis或Mysql获取
	return "get from redis"
}

func (d String) Update(val Value) {
	// 更换新缓存
}

func TestGet(t *testing.T) {
	c := NewCache(int64(0))
	// c.Add("key1", String("1234"), func(value Value) {})

	if v, ok := c.Get("key1"); !ok || string(v.(String)) != "1234" {
		t.Fatalf("cache hit key1=1234 failed")
	}
}

type RoomCache struct {
	PlayerID uint64
	Address  String
}

func (r RoomCache) GetKey() string {
	return fmt.Sprintf("desk:addr:%v", r.Address)
}

func (r RoomCache) Update(val Value) {
	logrus.Info("To update cache : " + r.Address)
}

func TestCacheObj(t *testing.T) {
	c := NewCache(int64(0))
	roomCache := &RoomCache{}
	roomCache.PlayerID = 10001
	roomCache.Address = "************"

	val, ok := c.Get(roomCache.GetKey())
	if !ok {
		c.Add(roomCache.GetKey(), roomCache, func(value Value) {
			t.Log("缓存淘汰")
		})
	}

	val.Update(roomCache)
}
