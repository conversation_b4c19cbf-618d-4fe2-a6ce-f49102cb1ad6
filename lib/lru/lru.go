package lru

/*
   绿色的是字典(map)，存储键和值的映射。键(key)查找值(value)的复杂是O(1)，字典插入一条记录复杂度也是O(1)。
   红色的是双向链表(double linked list)实现队列。值放到双向链表，访问到值时，移动到队尾复杂度是O(1)，队尾新增删除记录的复杂度均为O(1)。
*/
import "container/list"

type CacheLRU struct {
	maxLen    int64 // 最大值
	nLen      int64
	ll        *list.List               // 链表，表示其LRU值。队尾的LRU值高
	cache     map[string]*list.Element // map，存储节点，方便取值
	onEvicted func(value Value)        // 被淘汰时的回调函数
}

type entry struct {
	key   string
	value Value
}

type Value interface {
	GetKey() string
	Update(val Value)
}

func New(maxLen int64, onEvited func(Value)) *CacheLRU {
	return &CacheLRU{
		maxLen:    maxLen,
		ll:        list.New(),
		cache:     make(map[string]*list.Element),
		onEvicted: onEvited,
	}
}

// Get 查找主要有 2 个步骤，第一步是从字典中找到对应的双向链表的节点，第二步，将该节点移动到队尾。
func (c *CacheLRU) Get(key string) (val Value, ok bool) {
	if ele, ok := c.cache[key]; ok { // 如果键对应的链表节点存在，返回查找到的值。
		kv := ele.Value.(*entry)
		return kv.value, true
	}
	return val, false
}

// RemoveOldSet 这里的删除，实际上是缓存淘汰。即移除最近最少访问的节点（队首）
func (c *CacheLRU) RemoveOldSet() {
	ele := c.ll.Back() // c.ll.Back() 取到队首节点，从链表中删除。

	if ele != nil {
		c.ll.Remove(ele)
		kv := ele.Value.(*entry)
		delete(c.cache, kv.key) // 从字典中 c.cache 删除该节点的映射关系。
		c.nLen--
		if c.onEvicted != nil {
			c.onEvicted(kv.value) // 如果回调函数 OnEvicted 不为 nil，则调用回调函数。
		}
	}
}

// Remove 从Cache里删除
func (c *CacheLRU) Remove(key string) {
	ele, ok := c.cache[key]
	if ok {
		c.ll.Remove(ele)
		delete(c.cache, key)
		c.nLen--
		if c.onEvicted != nil {
			v := ele.Value.(*entry)
			c.onEvicted(v.value)
		}
	}
}

func (c *CacheLRU) Add(key string, value Value) {
	if ele, ok := c.cache[key]; ok {
		c.ll.MoveToFront(ele) // 如果键存在，则更新对应节点的值，并将该节点移到队尾。
		kv := ele.Value.(*entry)
		kv.value.Update(value)
		// 不存在则是新增场景，首先队尾添加新节点 &entry{key, value}, 并字典中添加 key 和节点的映射关系。
	} else {
		ele := c.ll.PushFront(&entry{key, value})
		c.cache[key] = ele
		c.nLen++
	}
	// 更新 c.nBytes，如果超过了设定的最大值 c.maxBytes，则移除最少访问的节点。
	for c.maxLen != 0 && c.maxLen < c.nLen {
		c.RemoveOldSet()
	}
}

func (c *CacheLRU) Len() int {
	if c.Empty() {
		return 0
	}
	return c.ll.Len()
}
func (c *CacheLRU) Empty() bool {
	return c.ll == nil || c.nLen == 0
}

func (c *CacheLRU) IsFull() bool {
	if c == nil {
		return false
	}
	return int64(c.Len()) > c.maxLen
}

func (c *CacheLRU) Contain(key string) bool {
	if c.Empty() {
		return false
	}
	if _, ok := c.cache[key]; ok {
		return true
	}
	return false
}
