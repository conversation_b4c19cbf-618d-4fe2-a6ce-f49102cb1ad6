package codec

import (
	"bytes"
	"compress/gzip"
	"io"
)

const unzipLimit = 100 * 1024 * 1024 // 100MB

// Gzip compresses bs.
func Gzip(bs []byte) []byte {
	var b bytes.Buffer

	w := gzip.NewWriter(&b)
	w.Write(bs)
	w.Close()

	return b.Bytes()
}

// Gunzip uncompresses bs.
func Gunzip(bs []byte) ([]byte, error) {
	r, err := gzip.NewReader(bytes.NewBuffer(bs))
	if err != nil {
		return nil, err
	}
	defer r.Close()

	var c bytes.Buffer
	if _, err = io.Copy(&c, io.LimitReader(r, unzipLimit)); err != nil {
		return nil, err
	}

	return c.Bytes(), nil
}
