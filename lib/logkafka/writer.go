package logkafka

import (
	"fmt"
	"time"
)

type LoggerWriter interface {
	Write(entry *logEntry) error
	Sync() error
	Close() error
}

type console struct{}

func newConsoleWrite() *console {
	return &console{}
}

func (i *console) Write(entry *logEntry) error {
	_, err := fmt.Printf("time=%s level=%s module=%s target=%s file=%s line=%d fields=%v \n",
		entry.TimeStamp.Format(time.RFC3339), entry.Level, entry.Module, entry.Target, entry.File, entry.Line, entry.Fields)
	return err
}

func (i *console) Sync() error {
	return nil
}

func (i *console) Close() error {
	return nil
}
