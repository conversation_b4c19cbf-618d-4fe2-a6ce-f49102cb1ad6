package logkafka

import "encoding/json"

type LogLevel byte

func (l LogLevel) MarshalJSON() ([]byte, error) {
	var lName = levelNames[l]
	return []byte(`"` + lName + `"`), nil
}

func (l LogLevel) String() string {
	return levelNames[l]
}

type jsonFormatter struct {
}

func NewJsonFormatter() LoggerFormatter {
	return &jsonFormatter{}
}

func (f *jsonFormatter) Format(entry *logEntry, buf *[]byte) (err error) {
	entry.File = toShort(entry.File)
	jsonBuf, err := json.Marshal(entry)
	*buf = append(*buf, jsonBuf...)
	return
}
