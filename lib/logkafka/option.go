package logkafka

type Option func(logger *tLogger)

func Level(level LogLevel) Option {
	return func(logger *tLogger) {
		logger.level = level
	}
}

func Module(module string) Option {
	return func(logger *tLogger) {
		logger.module = module
	}
}

func Target(target string) Option {
	return func(logger *tLogger) {
		logger.target = target
	}
}

func ErrorPath(path string) Option {
	return func(logger *tLogger) {
		logger.errorPath = path
	}
}

func Writer(writer LoggerWriter) Option {
	return func(logger *tLogger) {
		logger.writer = writer
	}
}

func CallDepth(callDepth int) Option {
	return func(logger *tLogger) {
		logger.callDepth = callDepth
	}
}
