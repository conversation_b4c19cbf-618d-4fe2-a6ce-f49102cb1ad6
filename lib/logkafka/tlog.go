package logkafka

import (
	"os"
	"runtime"
	"sync"
	"time"
)

const (
	LevelTrace LogLevel = iota
	LevelDebug
	LevelInfo
	LevelWarn
	LevelError
	LevelFatal
	LevelPanic
)

var levelNames = []string{
	LevelTrace: "Trace",
	LevelDebug: "Debug",
	LevelInfo:  "Info",
	LevelWarn:  "Warn",
	LevelError: "Error",
	LevelFatal: "Fatal",
	LevelPanic: "Panic",
}

type tLogger struct {
	level     LogLevel
	module    string
	target    string
	errorPath string
	kAddr     []string
	kTopic    string
	writer    LoggerWriter
	pipe      chan *logEntry
	errFile   *os.File
	initOnce  sync.Once
	callDepth int
}

var logger = &tLogger{
	callDepth: 2,
	writer:    newConsoleWrite(),
}

func GetLogger() *tLogger {
	return logger
}

func (l *tLogger) Start(opts ...Option) {
	for _, opt := range opts {
		opt(logger)
	}
}

func (l *tLogger) Close() error {
	return l.writer.Close()
}

func (l *tLogger) Sync() error {
	return l.writer.Sync()
}

// SetLevel Set the log level current instance.
func (l *tLogger) SetLevel(level LogLevel) {
	l.level = level
}

func (l *tLogger) GetLevel() LogLevel {
	return l.level
}

func (l *tLogger) CanTrace() bool {
	return l.level <= LevelTrace
}

func (l *tLogger) CanDebug() bool {
	return l.level <= LevelDebug
}

func (l *tLogger) CanInfo() bool {
	return l.level <= LevelInfo
}

func (l *tLogger) CanWarn() bool {
	return l.level <= LevelWarn
}

func (l *tLogger) CanError() bool {
	return l.level <= LevelError
}

func (l *tLogger) CanFatal() bool {
	return l.level <= LevelFatal
}

func (l *tLogger) CanPanic() bool {
	return l.level <= LevelPanic
}

func (l *tLogger) Trace(detail *Detail) {
	if l.CanTrace() {
		l.send(l.callDepth, LevelTrace, detail)
	}
}

func (l *tLogger) Debug(detail *Detail) {
	if l.CanDebug() {
		l.send(l.callDepth, LevelDebug, detail)
	}
}

func (l *tLogger) Info(detail *Detail) {
	if l.CanInfo() {
		l.send(l.callDepth, LevelInfo, detail)
	}
}

func (l *tLogger) Warn(detail *Detail) {
	if l.CanWarn() {
		l.send(l.callDepth, LevelWarn, detail)
	}
}

func (l *tLogger) Error(detail *Detail) {
	if l.CanError() {
		l.send(l.callDepth, LevelError, detail)
	}
}

func (l *tLogger) Fatal(detail *Detail) {
	if l.CanFatal() {
		l.send(l.callDepth, LevelFatal, detail)
	}
}

func (l *tLogger) Panic(detail *Detail) {
	if l.CanPanic() {
		l.send(l.callDepth, LevelPanic, detail)
	}
}

func (l *tLogger) send(callDepth int, lev LogLevel, detail *Detail) {
	_, file, line, ok := runtime.Caller(callDepth)
	if !ok {
		file = "???"
		line = 0
	}

	entry := &logEntry{
		Module:    l.module,
		TimeStamp: time.Now(),
		Level:     lev,
		File:      file,
		Target:    l.target,
		Line:      line,
		Fields:    *detail,
	}
	err := l.writer.Write(entry)
	if err != nil {
		l.writeError(err)
	}
}

func (l *tLogger) writeError(err error) {
	if l.errFile == nil {
		timeStr := time.Now().Format("20060102150405")
		if l.errorPath != "" {
			l.errorPath = l.errorPath + "/tLogError_" + timeStr + ".log"
		} else {
			l.errorPath = "../log/tLogError.log"
		}
		l.errFile, _ = os.OpenFile(l.errorPath, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	}

	var buf []byte
	formatTime(&buf, time.Now())
	buf = append(buf, ' ')
	buf = append(buf, "Error"...)
	buf = append(buf, ' ')
	buf = append(buf, err.Error()...)
	buf = append(buf, '\n')

	l.errFile.Write(buf)
}
