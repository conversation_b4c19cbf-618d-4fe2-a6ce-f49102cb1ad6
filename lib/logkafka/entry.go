package logkafka

import "time"

// 日志结构体
type logEntry struct {
	Module    string                 `json:"module"`
	TimeStamp time.Time              `json:"timestamp"`
	Level     LogLevel               `json:"level"`
	File      string                 `json:"file"`
	Target    string                 `json:"target"`
	Line      int                    `json:"line"`
	Fields    map[string]interface{} `json:"fields"`
}
