package logkafka

import (
	"sync"
)

type Detail map[string]interface{}

var pool *sync.Pool

type Fields struct {
	detail Detail
}

func init() {
	pool = &sync.Pool{
		New: func() interface{} {
			return new(Fields)
		},
	}
}

func NewFields() *Fields {
	field := pool.Get().(*Fields)
	field.detail = make(map[string]interface{})
	return field
}

func (l *Fields) Add(keyStr string, valueStr interface{}) *Fields {
	l.detail[keyStr] = valueStr
	return l
}

func (l *Fields) AddMap(values map[string]interface{}) *Fields {
	l.detail = values
	return l
}

func (l *Fields) GetDetail() *Detail {
	return &l.detail
}
