package httpx

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/sirupsen/logrus"
	"io"
	"net/http"
	"net/url"
)

func PostBytes(ctx context.Context, postURL string, buf []byte) ([]byte, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	bodyReader := bytes.NewReader(buf)
	req, err := http.NewRequestWithContext(ctx, dict.SysWordPost, postURL, bodyReader)
	if err != nil {
		return nil, err
	}

	rsp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logrus.Error("PostUrl:" + err.Error())
		}
	}(rsp.Body)

	data, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// PostJSON https post json数据
func PostJSON(url string, body []byte) (string, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return "", err
	}

	rsp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer rsp.Body.Close()

	data, err := io.ReadAll(rsp.Body)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

func PostBackJsonByMap(host string, params map[string]string) ([]byte, error) {
	reqData, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	resp, err := http.Post(host, "application/json", bytes.NewReader(reqData))
	if err != nil {
		logrus.Errorf("post error[%v]", string(reqData))
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		errCls := Body.Close()
		if errCls != nil {
			logrus.Errorf("GetBackJson http close path:%s err:%s", host, errCls)
		}
	}(resp.Body)

	s, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		logrus.Infof("PostBackJsonByMap err :%v", err)
	}

	return s, nil
}

func PostBackFormByHeader(uri string, headers map[string]string, params map[string]string) ([]byte, error) {
	formData := url.Values{}
	for key, value := range params {
		formData.Set(key, value)
	}

	bodyReader := bytes.NewBufferString(formData.Encode())

	req, err := http.NewRequest("POST", uri, bodyReader)
	if err != nil {
		return nil, err
	}

	// 设置头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 处理响应
	result, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		logrus.Infof("PostBackByHeader err :%v", err)
		return result, errors.New("http status code not ok")
	}

	return result, nil
}
