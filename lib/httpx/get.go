package httpx

import (
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/sirupsen/logrus"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
)

func GetUrl(getURL string) (string, error) {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	cookieJar, _ := cookiejar.New(nil)
	client := &http.Client{Transport: tr, Jar: cookieJar}

	rsp, err := client.Get(getURL)
	if err != nil {
		return "", err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logrus.Warnf("httpx GetUrl : %v", err)
		}
	}(rsp.Body)

	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

func GetBackJson(host string, params url.Values) ([]byte, error) {
	query := params.Encode()
	path := fmt.Sprintf("%s?%s", host, query)
	resp, err := http.Get(path)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		errCls := Body.Close()
		if errCls != nil {
			logrus.Errorf("GetBackJson http close path:%s err:%s", path, errCls)
		}
	}(resp.Body)

	s, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logrus.Infof("GetBackJson :%s", s)
	return s, nil
}

func GetBackFormByHeader(uri string, headers map[string]string, queryArray map[string]string) ([]byte, error) {
	// 构建完整的URL
	u, err := url.Parse(uri)
	if err != nil {
		return nil, err
	}
	q := u.Query()
	for k, v := range queryArray {
		q.Set(k, v)
	}
	u.RawQuery = q.Encode()

	// 创建请求
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 处理响应
	result, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		logrus.Infof("PostBackByHeader err :%v", err)
		return result, errors.New("http status code not ok")
	}

	return result, nil
}
