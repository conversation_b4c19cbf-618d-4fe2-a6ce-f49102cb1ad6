package utility

import "testing"

func TestCheckAccountPwd_Normal(t *testing.T) {
	// 正常测试，测试一些符合规则的账号
	tests := []string{
		"password123",
		"pA5word",
		"aBcDeFgHiJ",
		"***********",
		"abcdefghij",
	}

	for _, test := range tests {
		if !CheckAccountPwd(test) {
			t.<PERSON><PERSON>("CheckAccountPwd(%s) 返回错误", test)
		}
	}
}

func TestCheckAccountPwd_Abnormal(t *testing.T) {
	// 异常测试，测试一些不符合规则的账号
	tests := []string{
		// "password123!",
		// "pA5word@",
		// "aBcDeFgHiJ#",
		// "***********2sddassasdsa",     // 长度超过16位
		// "你好啊",
		"123",
	}

	for _, test := range tests {
		if !CheckAccountPwd(test) {
			t.<PERSON><PERSON><PERSON>("CheckAccountPwd(%s) 未返回错误", test)
		}
	}
}

func TestAge(t *testing.T) {
	t.Logf("age:%+v", Calc<PERSON>ge(1994, 10, 15))
}
