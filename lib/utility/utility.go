package utility

import (
	"reflect"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
)

// CheckValid 校验参数是否有效
func CheckValid(v interface{}) bool {
	value := reflect.ValueOf(v)
	switch value.Kind() {
	case reflect.String:
		return value.Len() > 0
	case reflect.Bool:
		return value.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return value.Int() > 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return value.Uint() > 0
	case reflect.Float32, reflect.Float64:
		return value.Float() > 0
	case reflect.Struct:
		if _, ok := v.(*time.Time); ok {
			return !value.Interface().(*time.Time).IsZero()
		}
		// 对于自定义的枚举类型或其他结构体，需添加额外的处理逻辑
	default:
		logrus.Errorf("CheckValid, v:%v, un know type!!!", v)
	}
	return false
}

// 计算实际年龄
func CalcAge(year, month, day int32) int32 {
	now := timex.Now()
	// 基础年龄差
	age := int32(now.Year()) - year
	// 计算是否过生日了
	if int32(now.Month()) <= month {
		if int32(now.Day()) < day {
			age -= 1
		}
	}
	return age
}
