package etcdv3

import (
	"context"
	"encoding/json"
	"github.com/sirupsen/logrus"
	clientV3 "go.etcd.io/etcd/client/v3"
	"log"
)

// ServiceRegister 创建租约注册服务
type ServiceRegister struct {
	cli           *clientV3.Client                        // etcd client
	leaseID       clientV3.LeaseID                        // 租约ID
	keepAliveChan <-chan *clientV3.LeaseKeepAliveResponse // 租约Keepalive相应chan
	opts          *Options
}

// NewServiceRegister 新建注册服务
func NewServiceRegister(opt ...FuncOptions) (*ServiceRegister, error) {
	ser := &ServiceRegister{
		opts: newOptions(opt...),
	}

	cli, err := clientV3.New(ser.opts.EtcdConf)
	if err != nil {
		log.Fatal(err)
	}
	ser.cli = cli

	// 申请租约设置时间keepalive
	if errPutKey := ser.putKeyWithLease(ser.opts.RegisterTtl); err != nil {
		return nil, errPutKey
	}

	return ser, nil
}

// 设置租约
func (s *ServiceRegister) putKeyWithLease(lease int64) error {

	data, err := json.Marshal(s.opts)
	if err != nil {
		return err
	}

	// 设置租约时间
	resp, err := s.cli.Grant(context.Background(), lease)
	if err != nil {
		return err
	}

	// 注册服务并绑定租约
	// _, err = s.cli.Put(ctx, s.opts.Key, s.opts.Node.Weight, clientV3.WithLease(resp.ID))
	_, err = s.cli.Put(context.Background(), s.opts.Key, string(data), clientV3.WithLease(resp.ID))
	if err != nil {
		return err
	}

	// 设置续租 定期发送需求请求
	leaseRespChan, kaErr := s.cli.KeepAlive(context.Background(), resp.ID)
	if kaErr != nil {
		return kaErr
	}

	if err != nil {
		return err
	}
	s.leaseID = resp.ID
	s.keepAliveChan = leaseRespChan

	go func() {
		s.ListenLeaseRespChan()
	}()

	logrus.Infof("[etcdv3] Put key:%s  Info:%s  success!", s.opts.Key, data)
	return nil
}

// ListenLeaseRespChan 监听 续租情况
func (s *ServiceRegister) ListenLeaseRespChan() (isClose bool) {
	for ka := range s.keepAliveChan {
		// log.Println("续租心跳收到回包")
		if ka.TTL <= 0 {
			logrus.Debugf("[etcdv3] leaseID : %d  ttl : %d", ka.ID, ka.TTL)
		}
	}
	return true
}

// Close 注销服务
func (s *ServiceRegister) Close() error {
	// 撤销租约
	if _, err := s.cli.Revoke(context.Background(), s.leaseID); err != nil {
		return err
	}
	logrus.Infof("[etcdv3] cance lease")
	return s.cli.Close()
}
