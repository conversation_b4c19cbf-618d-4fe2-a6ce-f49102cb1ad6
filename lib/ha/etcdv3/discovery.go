package etcdv3

import (
	"context"
	"encoding/json"
	"git.keepfancy.xyz/back-end/frameworks/lib/ha/balancer"
	"git.keepfancy.xyz/back-end/frameworks/lib/ha/schema"
	"go.etcd.io/etcd/api/v3/mvccpb"
	clientV3 "go.etcd.io/etcd/client/v3"
	"google.golang.org/grpc/resolver"
	"log"
	"strconv"
	"strings"
	"sync"
)

const addrCapacity = 10 // 容量，即最大多少个Endpoint中Pick

// ServiceDiscovery 服务发现
type ServiceDiscovery struct {
	cli        *clientV3.Client // etcd client
	cc         resolver.ClientConn
	serverList sync.Map // 服务列表
	prefix     string   // 监视的前缀
	opts       *Options
}

// NewServiceDiscovery  新建发现服务
func NewServiceDiscovery(opt ...FuncOptions) (resolver.Builder, error) {
	s := &ServiceDiscovery{
		opts: newOptions(opt...),
	}

	etcdCli, err := clientV3.New(s.opts.EtcdConf)
	if err != nil {
		return nil, err
	}
	s.cli = etcdCli

	resolver.Register(s)

	return s, nil
}

// Build 为给定目标创建一个新的`resolver`，当调用`grpc.Dial()`时执行
func (s *ServiceDiscovery) Build(target resolver.Target, cc resolver.ClientConn, opts resolver.BuildOptions) (resolver.Resolver, error) {
	s.cc = cc
	s.prefix = "/" + target.URL.Scheme + target.URL.Path + "/"

	// 根据前缀获取现有的key
	resp, err := s.cli.Get(context.Background(), s.prefix, clientV3.WithPrefix())
	if err != nil {
		return nil, err
	}

	for _, ev := range resp.Kvs {
		errSetServiceList := s.SetServiceList(string(ev.Key), string(ev.Value))
		if errSetServiceList != nil {
			return nil, errSetServiceList
		}
	}

	errUpdateState := s.cc.UpdateState(resolver.State{Addresses: s.getServices()})
	if errUpdateState != nil {
		return nil, errUpdateState
	}

	// 监视前缀，修改变更的server
	go s.watcher()
	return s, nil
}

// ResolveNow 监视目标更新
func (s *ServiceDiscovery) ResolveNow(rn resolver.ResolveNowOptions) {
	log.Println(rn)
}

func (s *ServiceDiscovery) Scheme() string {
	return schema.Discovery
}

// Close 关闭
func (s *ServiceDiscovery) Close() {
	log.Println("ServiceDiscovery Close")
	errClose := s.cli.Close()
	if errClose != nil {
		return
	}
}

// watcher 监听前缀
func (s *ServiceDiscovery) watcher() {
	rch := s.cli.Watch(context.Background(), s.prefix, clientV3.WithPrefix())
	// log.Printf("watching prefix:%s now...", s.prefix)
	for resp := range rch {
		for _, ev := range resp.Events {
			switch ev.Type {
			case mvccpb.PUT: // 新增或修改
				errSetList := s.SetServiceList(string(ev.Kv.Key), string(ev.Kv.Value))
				if errSetList != nil {
					return
				}
			case mvccpb.DELETE: // 删除
				s.DelServiceList(string(ev.Kv.Key))
			}
		}
	}
}

// SetServiceList 设置服务地址
func (s *ServiceDiscovery) SetServiceList(key, val string) error {
	data := &Options{}
	err := json.Unmarshal([]byte(val), data)
	if err != nil {
		return err
	}

	// 获取服务地址
	addr := resolver.Address{Addr: strings.TrimPrefix(key, s.prefix)}

	// 获取服务地址权重
	nodeWeight, err := strconv.Atoi(data.Node.Weight)
	if err != nil {
		// 非数字字符默认权重为1
		nodeWeight = 1
	}

	// 把服务地址权重存储到resolver.Address的元数据中
	addr = balancer.SetAddrInfo(addr, balancer.AddrInfo{Weight: nodeWeight})
	s.serverList.Store(key, addr)
	errUpdateState := s.cc.UpdateState(resolver.State{Addresses: s.getServices()})
	if errUpdateState != nil {
		return errUpdateState
	}
	// log.Println("put key :", key, "wieght:", val)
	return nil
}

// DelServiceList 删除服务地址
func (s *ServiceDiscovery) DelServiceList(key string) {
	s.serverList.Delete(key)
	err := s.cc.UpdateState(resolver.State{Addresses: s.getServices()})
	if err != nil {
		return
	}
	// log.Println("del key:", key)
}

// GetServices 获取服务地址
func (s *ServiceDiscovery) getServices() []resolver.Address {
	adds := make([]resolver.Address, 0, addrCapacity)
	s.serverList.Range(func(k, v interface{}) bool {
		adds = append(adds, v.(resolver.Address))
		return true
	})
	return adds
}
