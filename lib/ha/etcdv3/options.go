package etcdv3

import (
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/lib/ha/balancer"
	"git.keepfancy.xyz/back-end/frameworks/lib/ha/schema"
	"github.com/google/uuid"
	clientV3 "go.etcd.io/etcd/client/v3"
	"google.golang.org/grpc/resolver"
	"reflect"
	"strings"
	"time"
)

// Node 节点信息
type Node struct {
	Name    string   `json:"name"`
	Path    string   `json:"path"`
	Id      string   `json:"id"`
	Version string   `json:"version"`
	Address string   `json:"address"`
	Weight  string   `json:"weight"`
	Tags    []string `json:"tags"` // 节点标签
}

type Options struct {
	EtcdConf            clientV3.Config      `json:"-"`
	RegisterTtl         int64                `json:"-"`
	Node                *Node                `json:"node"`
	Metadata            map[string]string    `json:"metadata"`
	Key                 string               `json:"key"`
	Endpoints           map[string]Endpoints `json:"endpoints"`
	LoadBalancingPolicy string               `json:"-"`
}

// Endpoints etcd客户端信息
type Endpoints struct {
	Online         bool   `json:"online"`
	LimitingSwitch bool   `json:"limiting_switch"` // 每秒多少次
	Limiting       int64  `json:"limiting"`        // 每秒多少次
	Fuse           bool   `json:"fuse"`            // 熔断
	Defaults       []byte `json:"defaults"`        // 熔断默认值
}

type FuncOptions func(*Options)

func newOptions(opt ...FuncOptions) *Options {
	opts := &Options{
		EtcdConf: clientV3.Config{
			Endpoints:   []string{"127.0.0.1:2379"},
			DialTimeout: 5 * time.Second,
		},
		Node:                &Node{Version: "latest", Weight: "1"},
		RegisterTtl:         10,
		Endpoints:           make(map[string]Endpoints),
		Key:                 "/" + schema.Discovery + "/" + "0.0.0.0",
		LoadBalancingPolicy: balancer.LBWeighted,
	}
	for _, o := range opt {
		o(opts)
	}
	return opts
}

func SetName(name string) FuncOptions {
	return func(options *Options) {
		path := strings.Split(name, ".")
		options.Node.Name = path[len(path)-1]
		options.Node.Id = fmt.Sprintf("%s-%s", options.Node.Name, uuid.Must(uuid.NewUUID()).String())
		options.Node.Path = fmt.Sprintf("/%s", strings.Join(path, "/"))
	}
}

func SetSrv(srv interface{}) FuncOptions {
	return func(options *Options) {
		typ := reflect.TypeOf(srv)
		for m := 0; m < reflect.TypeOf(srv).NumMethod(); m++ {
			options.Endpoints[typ.Method(m).Name] = Endpoints{
				Online:         true,
				LimitingSwitch: false,
				Limiting:       10,
				Fuse:           false,
				Defaults:       nil,
			}
		}
	}
}

func SetRegisterTTL(registerTtl int64) FuncOptions {
	return func(options *Options) {
		options.RegisterTtl = registerTtl
	}
}

func SetVersion(version string) FuncOptions {
	return func(options *Options) {
		options.Node.Version = version
	}
}

func SetWight(weight string) FuncOptions {
	return func(options *Options) {
		options.Node.Weight = weight
	}
}

func SetEtcdConf(conf clientV3.Config) FuncOptions {
	return func(options *Options) {
		options.EtcdConf = conf
	}
}

func SetAddress(address string) FuncOptions {
	return func(options *Options) {
		options.Node.Address = address
	}
}

func SetMetadata(metadata map[string]string) FuncOptions {
	return func(options *Options) {
		options.Metadata = metadata
	}
}

func SetKey(key string) FuncOptions {
	return func(options *Options) {
		options.Key = key
	}
}

func SetTags(tags []string) FuncOptions {
	return func(options *Options) {
		options.Node.Tags = tags
	}
}

func SetLoadBalancingPolicy(lbStr string) FuncOptions {
	return func(options *Options) {
		options.LoadBalancingPolicy = lbStr
	}
}

func GetTarget(r resolver.Builder, tagStr, srvName string) string {
	return fmt.Sprintf("%s:///%s/%s", r.Scheme(), tagStr, srvName)
}

// GetKeyPrefix Key前缀
func GetKeyPrefix() string {
	return "/" + schema.Discovery + "/"
}

// GetKeyForEtcd 格式化带Schema的服务端地址
func GetKeyForEtcd(srvName, addr string) string {
	// return "/" + Schema + "/" + srvName + "/" + addr
	return GetKeyPrefix() + srvName + "/" + addr
}

func GetSrvKeyForEtcd(srvName, tagStr string) string {
	return fmt.Sprintf("%s%s/%s", GetKeyPrefix(), tagStr, srvName)
}
