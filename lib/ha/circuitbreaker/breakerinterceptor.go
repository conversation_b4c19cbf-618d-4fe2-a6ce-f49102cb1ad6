package circuitbreaker

import (
	"context"
	"errors"
	"net"
	"net/http"

	"github.com/afex/hystrix-go/hystrix"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

func init() {
	// 增加熔断器dashboard
	hystrixStreamHandler := hystrix.NewStreamHandler()
	hystrixStreamHandler.Start()
	go http.ListenAndServe(net.JoinHostPort("", "81"), hystrixStreamHandler)
}

// UnaryClientInterceptor ...
func UnaryClientInterceptor() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {

		// DefaultMetricCollector 统计控制器保存熔断所有状态。 调用次数/失败次数/拒绝次数等
		// 保存了10秒内的Buckets数据信息，每一个Bucket的统计时长为1秒
		// 每一次断路器逻辑的执行都会上报执行过程中的状态
		hystrix.ConfigureCommand(method, hystrix.CommandConfig{
			Timeout:                5000, // 超时时间设置  单位毫秒
			MaxConcurrentRequests:  2000, // 最大请求数
			SleepWindow:            1000, // 过多长时间，熔断器再次检测是否开启。单位毫秒
			ErrorPercentThreshold:  50,   // 错误百分比，默认50，即50%
			RequestVolumeThreshold: 20,   // 请求阈值  熔断器是否打开首先要满足这个条件，超过这个次数才开始计算错误百分比
		})

		// hystrix.ConfigureCommand(method, hystrix.CommandConfig{
		//	Timeout:                5000, //超时时间设置  单位毫秒
		//	MaxConcurrentRequests:  2000, //最大请求数
		//	SleepWindow:            1000, //过多长时间，熔断器再次检测是否开启。单位毫秒
		//	ErrorPercentThreshold:  50,   //错误百分比，默认50，即50%
		//	RequestVolumeThreshold: 5,    //请求阈值  熔断器是否打开首先要满足这个条件，超过这个次数才开始计算错误百分比
		// })

		err := hystrix.Do(method,
			// 没有触发熔断时候调用的函数
			func() (err error) {
				err = invoker(ctx, method, req, reply, cc, opts...)
				if err != nil {
					logrus.Errorf("[circuitbreaker]invoker error, [err=%v][ctx=%v]", err, ctx)
				}
				return err
			},
			// 触发熔断时候调用的函数 或者 前面的函数报错了, 保底使用
			func(err error) error {
				logrus.Errorf("[circuitbreaker] method= %s trigger circuit breaker", method)
				err = errors.New("触发熔断")
				return err
			})

		return err
	}
}

// StreamClientInterceptor ...
func StreamClientInterceptor() grpc.StreamClientInterceptor {
	return func(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn,
		method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
		return streamer(ctx, desc, cc, method, opts...)
	}
}
