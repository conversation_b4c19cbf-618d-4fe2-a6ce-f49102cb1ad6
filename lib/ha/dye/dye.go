package dye

import (
	"fmt"
	"github.com/spf13/viper"
	"strings"
)

const (
	TagNormal = "normal" // 染色标签，正常
	TagGray   = "gray"   // 染色标签，灰色
	TagDebug  = "debug"  // 染色标签，调试
)

// GetRegDyeing 服务注册染色标签处理
func GetRegDyeing(srvName string) ([]string, string) {
	tags := []string{TagNormal}
	if viper.GetString("rpc_server_tags") != "" {
		tagsSlice := strings.Split(viper.GetString("rpc_server_tags"), "|")
		tags = []string{}
		tags = append(tags, tagsSlice...)
	}
	tagDyeStr := fmt.Sprintf("%s/%s", TagNormal, srvName)
	if len(tags) > 0 {
		if tags[0] == TagGray {
			tagDyeStr = fmt.Sprintf("%s/%s", TagGray, srvName)
		}
	}
	return tags, tagDyeStr
}

// GetDiscoveryDyeing 获取服务发现染色标签
func GetDiscoveryDyeing(tags []string) ([]string, string) {
	tagDyeStr := fmt.Sprintf("%s", TagNormal)
	if len(tags) > 0 {
		if tags[0] == TagGray {
			tagDyeStr = fmt.Sprintf("%s", TagGray)
		}
	}
	return tags, tagDyeStr
}
