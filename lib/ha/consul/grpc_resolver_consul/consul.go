package consul

import (
	"context"
	"fmt"
	"sort"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/dict"

	"github.com/hashicorp/consul/api"
	"github.com/jpillora/backoff"
	"google.golang.org/grpc/resolver"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/attributes"
)

/*
resolver 基于 https://github.com/mbobakov/grpc-consul-resolver
拓展增加了 weight信息 用于灰度发布
*/

// InitRegister function needs for  auto-handle in resolvers registry
func InitRegister() {
	resolver.Register(&builder{})
}

// addressInfo 定义一个带weight的地址信息
type addressInfo struct {
	Address string
	Passing int
}

// resolvr implements resolver.Resolver from the gRPC package.
// It watches for endpoints changes and pushes them to the underlying gRPC connection.
type resolvr struct {
	cancelFunc context.CancelFunc
}

// ResolveNow will be skipped due unnecessary in this case
func (r *resolvr) ResolveNow(resolver.ResolveNowOptions) {}

// Close closes the resolver.
func (r *resolvr) Close() {
	r.cancelFunc()
}

// service 用于调用consul api
type service interface {
	Service(string, string, bool, *api.QueryOptions) ([]*api.ServiceEntry, *api.QueryMeta, error)
}

// watchConsulService watch consul数据变更 使用协程处理
func watchConsulService(ctx context.Context, s service, tgt target, out chan<- []addressInfo) {
	res := make(chan []addressInfo)
	quit := make(chan struct{})
	bck := &backoff.Backoff{
		Factor: 2,
		Jitter: true,
		Min:    10 * time.Millisecond,
		Max:    tgt.MaxBackoff,
	}

	go func() {
		var lastIndex uint64
		for {
			ss, meta, err := s.Service(
				tgt.Service,
				// 过滤掉tag 以获取所有服务
				// tgt.Tag,
				"",
				tgt.Healthy,
				&api.QueryOptions{
					WaitIndex:         lastIndex,
					Near:              tgt.Near,
					WaitTime:          tgt.Wait,
					Datacenter:        tgt.Dc,
					AllowStale:        tgt.AllowStale,
					RequireConsistent: tgt.RequireConsistent,
				},
			)

			if err != nil {
				logrus.Errorf("[Consul resolver] Couldn't fetch endpoints. target={%s}", tgt.String())
				time.Sleep(bck.Duration())
				continue
			}

			bck.Reset()
			lastIndex = meta.LastIndex

			// ✅ 如果请求带tag，则优先筛选带tag的服务，没有就使用普通的服务
			if tgt.Tag != "" {
				tagNodes := filterByTag(ss, tgt.Tag)
				if len(tagNodes) > 0 {
					ss = tagNodes
				}
			}

			// logrus.Infof("[Consul resolver] %d endpoints fetched in(+wait) %s for target={%s}",
			//	len(ss),
			//	meta.RequestTime,
			//	tgt.String(),
			// )

			// ✅ 构造 addressInfo 列表 没有带权重， 不能weight round balance
			ee := make([]addressInfo, 0, len(ss))
			for _, s := range ss {
				var tmpAddressInfo addressInfo
				address := s.Service.Address
				if s.Service.Address == "" {
					address = s.Node.Address
				}
				tmpAddressInfo.Address = fmt.Sprintf("%s:%d", address, s.Service.Port)

				tmpAddressInfo.Passing = s.Service.Weights.Passing
				ee = append(ee, tmpAddressInfo)
			}

			if tgt.Limit != 0 && len(ee) > tgt.Limit {
				ee = ee[:tgt.Limit]
			}

			select {
			case res <- ee:
				continue
			case <-quit:
				return
			}
		}
	}()

	for {
		select {
		case ee := <-res:
			out <- ee
		case <-ctx.Done():
			close(quit)
			return
		}
	}
}

// populateEndpoints 发布endpoints 和watchConsulService 基于chan进行通信
func populateEndpoints(ctx context.Context, clientConn resolver.ClientConn, input <-chan []addressInfo) {
	for {
		select {
		case cc := <-input:
			connSet := make(map[addressInfo]struct{}, len(cc))
			for _, c := range cc {
				connSet[c] = struct{}{}
			}

			conn := make([]resolver.Address, 0, len(connSet))
			for c := range connSet {
				// 参照grpc-go官方建议， 将路由策略相关的信息放在Address.Attributes中
				weightInfo := attributes.New(dict.ConfigPassingWeight, c.Passing)
				conn = append(conn, resolver.Address{Addr: c.Address, Attributes: weightInfo})
			}

			sort.Sort(byAddressString(conn)) // Don't replace the same address list in the balancer
			clientConn.UpdateState(resolver.State{Addresses: conn})
		case <-ctx.Done():
			logrus.Info("[Consul resolver] Watch has been finished")
			return
		}
	}
}

// filterByTag 筛选出带有指定 tag 的服务实例
func filterByTag(entries []*api.ServiceEntry, tag string) []*api.ServiceEntry {
	var filtered []*api.ServiceEntry
	for _, entry := range entries {
		for _, t := range entry.Service.Tags {
			if t == tag {
				filtered = append(filtered, entry)
				break
			}
		}
	}
	return filtered
}

// byAddressString sorts resolver.Address by Address Field  sorting in increasing order.
type byAddressString []resolver.Address

func (p byAddressString) Len() int           { return len(p) }
func (p byAddressString) Less(i, j int) bool { return p[i].Addr < p[j].Addr }
func (p byAddressString) Swap(i, j int)      { p[i], p[j] = p[j], p[i] }
