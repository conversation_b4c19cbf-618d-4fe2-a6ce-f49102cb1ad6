package consul

import (
	"context"
	"strings"

	"git.keepfancy.xyz/back-end/frameworks/lib/ha/schema"

	"github.com/hashicorp/consul/api"
	"github.com/pkg/errors"
	"google.golang.org/grpc/resolver"
)

// builder 适配grpc resolver
type builder struct{}

func (b *builder) Build(target resolver.Target,
	cc resolver.ClientConn, opts resolver.BuildOptions) (resolver.Resolver, error) {
	dsn := strings.Join([]string{schema.ConsulSchema + ":/", target.URL.Host, target.Endpoint()}, "/")
	tgt, err := parseURL(dsn)
	// parse url 中tag参数解析不到，手动赋值
	tgt.Tag = target.URL.Query().Get("tag")
	tgt.Healthy = true
	if err != nil {
		return nil, errors.Wrap(err, "Wrong consul URL")
	}
	cli, err := api.NewClient(tgt.consulConfig())
	if err != nil {
		return nil, errors.Wrap(err, "Couldn't connect to the Consul API")
	}

	ctx, cancel := context.WithCancel(context.Background())
	pipe := make(chan []addressInfo)
	go watchConsulService(ctx, cli.Health(), tgt, pipe)
	go populateEndpoints(ctx, cc, pipe)

	return &resolvr{cancelFunc: cancel}, nil
}

// Scheme returns the scheme supported by this resolver.
// Scheme is defined at https://github.com/grpc/grpc/blob/master/doc/naming.md.
func (b *builder) Scheme() string {
	return schema.ConsulSchema
}
