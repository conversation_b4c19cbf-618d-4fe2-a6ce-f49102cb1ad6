package discovery

import (
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/hashicorp/consul/api"
	"github.com/sirupsen/logrus"
	"time"
)

const (
	IntervalHealthCheck = time.Duration(10) * time.Second // consul健康检查间隔
	TimeoutHealthCheck  = time.Duration(1) * time.Second  // timeout
	TTLHealthCheck      = time.Duration(10) * time.Second // 需要在多长时间内更新TTL
	Deregister          = time.Duration(10) * time.Minute
)

type ConsulService struct {
	IP      string   // 注册本地服务的IP
	Port    int      // 注册本地服务的Port
	Tag     []string // 注册本地服务的Tags,是一个字符串数组
	Name    string   // 注册本地服务的Name
	Weights int      // 注册本地服务的权重
}

// RegisterService RegisterServiceService 服务注册
// ca : ConsulAddress
func RegisterService(ca string, cs *ConsulService) (*api.Client, error) {

	// handle consul
	consulConfig := api.DefaultConfig()
	consulConfig.Address = ca
	client, err := api.NewClient(consulConfig)
	if err != nil {
		logrus.Printf("NewClient error\n%v", err)
		return nil, errors.New("NewClient error")
	}

	id := FormatServerID(cs)
	agent := client.Agent()

	reg := &api.AgentServiceRegistration{
		ID:              id,      // 服务节点的名称
		Name:            cs.Name, // 服务名称
		Tags:            cs.Tag,  // tag，可以为空
		Port:            cs.Port, // 服务端口
		Address:         cs.IP,   // 服务 IP
		TaggedAddresses: map[string]api.ServiceAddress{},
		Meta: map[string]string{
			dict.ConfigHttpAddr: cs.IP + ":" + string(cs.Port), // http_addr 用于 Prometheus 的服务发现
		},
		Check: &api.AgentServiceCheck{
			Interval: IntervalHealthCheck.String(), // 健康检查间隔 （Consul服务器检查）
			CheckID:  id,
			Timeout:  TimeoutHealthCheck.String(), // 健康检查超时时间
			Name:     cs.Name + " 的健康检测",
			// TTL: TTLHealthCheck.String(), // 服务节点主动上报，TTL时间内向Consul发送健康状态不能跟Interval同时指定
			DeregisterCriticalServiceAfter: Deregister.String(),
			// grpc 支持，执行健康检查的地址，service 会传到 Health.Check 函数中
			GRPC: fmt.Sprintf("%v:%v/%v", cs.IP, cs.Port, cs.Name),
		},
	}

	if cs.Weights > 0 {
		reg.Weights = &api.AgentWeights{
			Passing: cs.Weights, // 权重设置
		}
	}

	logrus.Tracef("registing to %v\n", ca)
	if err := agent.ServiceRegister(reg); err != nil {
		logrus.Errorf("Service Register error\n%v", err)
		return nil, errors.New("service Register error")
	}
	return client, nil
}

// DeregisterService 服务注销
func DeregisterService(ca string, cs *ConsulService) bool {
	consulConfig := api.DefaultConfig()
	consulConfig.Address = ca
	client, err := api.NewClient(consulConfig)
	if err != nil {
		logrus.WithError(err).Errorf("NewClient error")
		return false
	}

	id := FormatServerID(cs)
	err = client.Agent().ServiceDeregister(id)
	if err != nil {
		logrus.WithError(err).Errorf("反注册服务失败")
		return false
	} else {
		return true
	}
}
