package ratelimit

import (
	"github.com/juju/ratelimit"
	"sync/atomic"
	"time"
	"unsafe"

	"github.com/andres-erbsen/clock"
)

// Limiter 限流器
type Limiter interface {
	Limit() bool
}

type rateLimiter struct {
	state unsafe.Pointer
	//lint:ignore U1000 Padding is unused but it is crucial to maintain performance
	// of this rate limiter in case of collocation with other frequently accessed memory.
	padding [56]byte // cache line size - state pointer size = 64 - 8; created to avoid false sharing.

	perRequest time.Duration
	maxSlack   time.Duration
	clock      Clock

	TokenBucket *ratelimit.Bucket
}

// Limit 返回true表示需要限流
func (t *rateLimiter) Limit() bool {

	newState := state{}
	taken := false
	for !taken {
		now := t.clock.Now()

		previousStatePointer := atomic.LoadPointer(&t.state)
		oldState := (*state)(previousStatePointer)

		newState = state{}
		newState.last = now

		// 第一个请求直接通过
		if oldState.last.IsZero() {
			taken = atomic.CompareAndSwapPointer(&t.state, previousStatePointer, unsafe.Pointer(&newState))
			continue
		}

		// 比较当前和上一次请求时间间隔
		newState.sleepFor += t.perRequest - now.Sub(oldState.last)
		// 结果一： newState.sleepFor > 0 此前多余出来的时间 无法完全抵消此次所需量
		// 结果二： newState.sleepFor < 0 此前有多余出来的时间 继续累加

		// 对于结果二： 如果多余出来的时间太长了 不能持续累加，需要一下策略处理 对应
		if newState.sleepFor < t.maxSlack {
			newState.sleepFor = t.maxSlack
		}

		// 对于结果一： 快速失败， 已经超过量了， 进行限流
		if newState.sleepFor > 0 {
			// 此时请求无效，当什么都没有发生过
			return true
		}

		// 乐观锁处理， 否则需要用Mutex
		taken = atomic.CompareAndSwapPointer(&t.state, previousStatePointer, unsafe.Pointer(&newState))
	}
	return false
}

type Clock interface {
	Now() time.Time
	Sleep(time.Duration)
}

type state struct {
	last     time.Time
	sleepFor time.Duration
}

func NewLimiter(rate int, maxSlack int) Limiter {
	l := &rateLimiter{
		perRequest: time.Second / time.Duration(rate),
		maxSlack:   -1 * time.Duration(maxSlack) * time.Second / time.Duration(rate),
		clock:      clock.New(),
	}

	initialState := state{
		last:     time.Time{},
		sleepFor: 0,
	}
	atomic.StorePointer(&l.state, unsafe.Pointer(&initialState))
	return l
}
