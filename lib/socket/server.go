//go:build !windows

package socket

import (
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	reuseport "github.com/kavu/go_reuseport"
	"net"
)

type Server interface {
	Listen(addr string) error
	Accept() (Socket, error)
	Close() error
}

type serverImpl struct {
	lis           net.Listener
	unPacker      UnPacker
	socketFactory Factory
}

func NewServer(unPacker UnPacker, socketFactory Factory) (Server, error) {
	if unPacker == nil {
		return nil, fmt.Errorf("unpacker should have a non-nil value")
	}

	if socketFactory == nil {
		socketFactory = &socketFactoryImpl{}
	}

	return &serverImpl{
		unPacker:      unPacker,
		socketFactory: socketFactory,
	}, nil
}

var _ Server = &serverImpl{}

func (server *serverImpl) Listen(addr string) error {
	var err error
	server.lis, err = reuseport.Listen(dict.SysWordTcp, addr)
	if err != nil {
		return err
	}
	return nil
}

func (server *serverImpl) Accept() (Socket, error) {
	if server.lis == nil {
		return nil, fmt.Errorf("not listen yet")
	}

	conn, err := server.lis.Accept()
	if err != nil {
		return nil, err
	}
	return server.socketFactory.NewSocket(conn, server.unPacker)
}

func (server *serverImpl) Close() error {
	if server.lis == nil {
		return nil
	}
	return server.lis.Close()
}
