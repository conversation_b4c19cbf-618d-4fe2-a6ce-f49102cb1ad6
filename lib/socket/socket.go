package socket

import (
	"fmt"
	"net"
)

type Socket interface {
	SendPackage(pkg []byte) error
	RecvPackage() (pkg []byte, err error)
	Close() error
	GetRemoteAddr() net.Addr
}

type Factory interface {
	NewSocket(conn net.Conn, unPacker UnPacker) (Socket, error)
}

type socketImpl struct {
	conn     net.Conn
	unPacker UnPacker
}

var _ Socket = new(socketImpl)

func (socket *socketImpl) SendPackage(pkg []byte) error {
	sz, err := socket.conn.Write(pkg)
	if err != nil {
		return err
	}
	if sz != len(pkg) {
		return fmt.Errorf("package send failed. write size(%v) does not equal to expected size(%v)", sz, len(pkg))
	}
	return nil
}

func (socket *socketImpl) RecvPackage() (pkg []byte, err error) {
	return socket.unPacker.Unpack(socket.conn)
}

func (socket *socketImpl) Close() error {
	return socket.conn.Close()
}

// GetRemoteAddr GetRemoteIP 获取远端地址
func (socket *socketImpl) GetRemoteAddr() net.Addr {
	return socket.conn.RemoteAddr()
}

type socketFactoryImpl struct{}

var _ Factory = new(socketFactoryImpl)

func (factory *socketFactoryImpl) NewSocket(conn net.Conn, unPacker UnPacker) (Socket, error) {
	if conn == nil || unPacker == nil {
		return nil, fmt.Errorf("conn and unpacker can't be nil")
	}
	// 增加发包超时时间为10秒
	// conn.SetWriteDeadline(time.Second*10)
	return &socketImpl{
		conn:     conn,
		unPacker: unPacker,
	}, nil
}
