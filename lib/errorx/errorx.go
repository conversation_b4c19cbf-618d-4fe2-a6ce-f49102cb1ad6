package errorx

import "fmt"

type ErrCode struct {
	code int
	msg  string
}

// 用于类型锁定
var _ IErr = &ErrCode{}

// 错误类型
type IErr interface {
	GetMsg() string
	GetCode() int
}

// Error
func (e *ErrCode) Error() string {
	return fmt.Sprintf("%d:%s", e.GetCode(), e.GetMsg())
}

func (e *ErrCode) SetMsg(msg string) {
	e.msg = msg
}

// 获取埋点消息
func (e *ErrCode) GetMsg() string {
	return e.msg
}

// 获取埋点code
func (e *ErrCode) GetCode() int {
	return e.code
}

func (e *ErrCode) SetCode(code int) {
	e.code = code
}

func NewErrorCode(code int, msg string) *ErrCode {
	return &ErrCode{
		code: code,
		msg:  msg,
	}
}
