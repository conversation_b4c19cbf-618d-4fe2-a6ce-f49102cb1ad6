package crypto

import (
	"bytes"
	"testing"
)

func TestSnappy(t *testing.T) {
	data := bytes.Repeat([]byte("hello world "), 100)
	z, err := Compress(data)
	if err != nil {
		t.Fail()
		return
	}
	unz, err := UnCompress(z)
	if err != nil {
		t.Fail()
		return
	}
	if !bytes.Equal(data, unz) {
		t.Fail()
	}
}

func BenchmarkCompress(b *testing.B) {
	data := bytes.Repeat([]byte(`hello`), 100)
	for i := 0; i < b.N; i++ {
		Compress(data)
	}
}
