package crypto

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"golang.org/x/xerrors"
)

type OpensslEncryptType int

const (
	OpenSSLAes128ECB OpensslEncryptType = iota + 1 // 默认PHP里面使用的加密方式
	OpenSSLAes192ECB                               // @暂时未使用
	OpenSSLAes256ECB                               // @暂时未使用
)

func getBlockSize(cipherType OpensslEncryptType) int {
	switch cipherType {
	case OpenSSLAes128ECB:
		return 16
	case OpenSSLAes192ECB:
		return 24
	case OpenSSLAes256ECB:
		return 32
	}
	return -1
}

func opensslKeyPadding(key []byte, cipherType OpensslEncryptType) []byte {
	blockSize := getBlockSize(cipherType)
	padding := blockSize - len(key)%blockSize
	padText := bytes.Repeat([]byte{0}, padding) // 用0填充
	return append(key, padText...)
}

// OpenSSLAesEncrypt 用于猪来了/IK PHP里面对deviceToken加解密的兼容算法
// 注意： 如果数据长度超过16字节，这个加解密和PHP的不兼容
func OpenSSLAesEncrypt(iData interface{}, cipherType OpensslEncryptType, iKey interface{}) (out []byte, err error) {
	var (
		data []byte
		key  []byte
	)
	switch vv := iData.(type) {
	case string:
		data = []byte(vv)
	case []byte:
		data = vv
	}
	switch vv := iKey.(type) {
	case string:
		key = []byte(vv)
	case []byte:
		key = vv
	}
	if len(key) == 0 || len(data) == 0 {
		err = xerrors.Errorf("data or key invalid ")
	}
	paddingKey := opensslKeyPadding(key, cipherType)
	block, err := aes.NewCipher(paddingKey)
	if err != nil {
		err = xerrors.Errorf("NewCipher err")
		return

	}
	blockSize := block.BlockSize()
	plainText := pkcs5Padding(data, blockSize)
	blockMode := newECBEncrypter(block)
	out = make([]byte, len(plainText))
	blockMode.CryptBlocks(out, plainText)
	return
}

// OpenSSLAesDecrypt 用于猪来了/IK PHP里面对deviceToken加解密的兼容算法
// 注意： 如果数据长度超过16字节，这个加解密和PHP的不兼容
func OpenSSLAesDecrypt(iData interface{}, cipherType OpensslEncryptType, iKey interface{}) (out []byte, err error) {
	var (
		data []byte
		key  []byte
	)
	switch vv := iData.(type) {
	case string:
		data = []byte(vv)
	case []byte:
		data = vv
	}
	switch vv := iKey.(type) {
	case string:
		key = []byte(vv)
	case []byte:
		key = vv
	}
	if len(key) == 0 || len(data) == 0 {
		err = xerrors.Errorf("data or key invalid ")
	}
	paddingKey := opensslKeyPadding(key, cipherType)
	block, err := aes.NewCipher(paddingKey)
	if err != nil {
		err = xerrors.Errorf("invalid key length")
		return

	}
	blockSize := block.BlockSize()
	if len(data)%blockSize != 0 {
		err = xerrors.Errorf("invalid encrypted data length")
		return
	}
	blockMode := newECBDecrypter(block)
	out = make([]byte, len(data))
	blockMode.CryptBlocks(out, data)
	out = pkcs5UnPadding(out, blockSize)
	return
}

type ecb struct {
	b         cipher.Block
	blockSize int
}

func newECB(b cipher.Block) *ecb {
	return &ecb{
		b:         b,
		blockSize: b.BlockSize(),
	}
}

type ecbEncrypter ecb

// NewECBEncrypter returns a BlockMode which encrypts in electronic code book
// mode, using the given Block.
func newECBEncrypter(b cipher.Block) cipher.BlockMode {
	return (*ecbEncrypter)(newECB(b))
}

func (x *ecbEncrypter) BlockSize() int { return x.blockSize }

func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	blocks := len(src) / x.blockSize
	for i := 0; i < blocks; i++ {
		start := i * x.blockSize
		end := start + x.blockSize
		s := src[start:end]
		d := dst[start:end]
		x.b.Encrypt(d, s)
	}
}

type ecbDecrypter ecb

// NewECBDecrypter returns a BlockMode which decrypts in electronic code book
// mode, using the given Block.
func newECBDecrypter(b cipher.Block) cipher.BlockMode {
	return (*ecbDecrypter)(newECB(b))
}

func (x *ecbDecrypter) BlockSize() int { return x.blockSize }

func (x *ecbDecrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	blocks := len(src) / x.blockSize
	for i := 0; i < blocks; i++ {
		start := i * x.blockSize
		end := start + x.blockSize
		s := src[start:end]
		d := dst[start:end]
		x.b.Decrypt(d, s)
	}
}
