package crypto

import "testing"

/*
<?php
const DEVICE_SECRET = 'Device_Aladin';
const ENCRYPT_METHOD = 'aes128';
$data = "asdfasfasdf112341234&@$%!$@#%#@$~&@^!#@~@#~@#~";
$enc_data = @openssl_encrypt($data,ENCRYPT_METHOD, DEVICE_SECRET);
echo "enc_data is : $enc_data \n";

	if (!empty($enc_data )){
	    $plain_data = @openssl_decrypt($enc_data, ENCRYPT_METHOD, DEVICE_SECRET);
	    echo "dec_data is: $plain_data \n";
	}

------------输出：
enc_data is : aLcvvr3HXVVHB/VdRn4JNpq59r1aSvYqdXp5qGSUrOr9s8djK/j36gKWuFqhNZ4O
dec_data is: asdfasfasdf112341234&@$%!$@#%#@$~&@^!#@~@#~@#~
*/
const (
	DEVICE_SECRET  = "Device_Aladin"
	ENCRYPT_METHOD = OpenSSLAes128ECB
)

var (
	plainData  = "11111111111111"
	cipherData = "gEX8rLuUAhyDTwvbJeyCZQ=="
)

func TestOpenSSLAesEncrypt(t *testing.T) {
	out, err := OpenSSLAesEncrypt([]byte(plainData), ENCRYPT_METHOD, []byte(DEVICE_SECRET))
	if err != nil {
		panic(err)
	}
	str := Base64Encode(out)
	if str != cipherData {
		t.Fatalf("encrypt data error, %s:%s\n", str, cipherData)
	}
}

func TestOpenSSLAesDecrypt(t *testing.T) {
	data, err := Base64Decode(cipherData)
	if err != nil {
		panic(err)
	}
	out, err := OpenSSLAesDecrypt(data, ENCRYPT_METHOD, []byte(DEVICE_SECRET))
	if err != nil {
		panic(err)
	}
	str := string(out)
	if str != plainData {
		t.Fatalf("decrypt data error, %s:%s\n", str, plainData)
	}
}
