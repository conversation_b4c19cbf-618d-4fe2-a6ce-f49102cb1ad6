package crypto

import (
	"crypto/md5"
	"encoding/hex"
	"git.keepfancy.xyz/back-end/frameworks/lib/zerocopy"
	"golang.org/x/xerrors"
	"io"
	"os"
)

func Md5SumStr(iData interface{}) (digest string, err error) {
	var data []byte
	switch vv := iData.(type) {
	case string:
		data = zerocopy.StrToBytes(vv)
	case []byte:
		data = vv
	default:
		err = xerrors.Errorf("data type not supported")
		return
	}
	h := md5.Sum(data)
	digest = hex.EncodeToString(h[:])
	return
}

func Md5SumRaw(iData interface{}) (digest []byte, err error) {
	var data []byte
	switch vv := iData.(type) {
	case string:
		data = zerocopy.StrToBytes(vv)
	case []byte:
		data = vv
	default:
		err = xerrors.Errorf("data type not supported")
		return
	}
	h := md5.Sum(data)
	digest = h[:]
	return
}

func Md5File(file string) (digest string, err error) {
	fp, err := os.Open(file)
	if err != nil {
		return
	}
	defer fp.Close()
	m := md5.New()
	io.Copy(m, fp)
	h := m.Sum(nil)
	digest = hex.EncodeToString(h)
	return
}
