package crypto

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"git.keepfancy.xyz/back-end/frameworks/lib/zerocopy"
	"golang.org/x/xerrors"
)

// AesEncrypt aes encrypt
// The key argument should be the AES key,
// either 16, 24, or 32 bytes to select
// AES-128, AES-192, or AES-256.
func AesEncrypt(iPlainText, iKey interface{}) (cipherText []byte, err error) {
	var (
		plainText []byte
		key       []byte
	)
	switch vv := iPlainText.(type) {
	case []byte:
		plainText = vv
	case string:
		plainText = zerocopy.StrToBytes(vv)
	default:
		err = xerrors.Errorf("plain type not supported")
		return
	}
	switch vv := iKey.(type) {
	case []byte:
		key = vv
	case string:
		key = zerocopy.StrToBytes(vv)
	default:
		err = xerrors.Errorf("key type not supported")
		return
	}
	if !checkKey(key) {
		err = xerrors.Errorf("key size not correct")
		return
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return
	}
	blockSize := block.BlockSize()
	plainText = pkcs5Padding(plainText, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	cipherText = make([]byte, len(plainText))
	blockMode.CryptBlocks(cipherText, plainText)
	return
}

// AesDecrypt aes decrypt
func AesDecrypt(ICipherText, iKey interface{}) (plainText []byte, err error) {
	var (
		cipherText []byte
		key        []byte
	)
	switch vv := ICipherText.(type) {
	case []byte:
		cipherText = vv
	case string:
		cipherText = zerocopy.StrToBytes(vv)
	default:
		err = xerrors.Errorf("cipher text type not support")
		return
	}
	switch vv := iKey.(type) {
	case []byte:
		key = vv
	case string:
		key = zerocopy.StrToBytes(vv)
	default:
		err = xerrors.Errorf("key type not support")
		return
	}
	if !checkKey(key) {
		err = xerrors.Errorf("key size not correct")
		return
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return
	}

	blockSize := block.BlockSize()
	if len(cipherText)%blockSize != 0 || len(cipherText) <= 0 {
		err = xerrors.Errorf("cipher text size not correct")
		return
	}

	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	plainText = make([]byte, len(cipherText))
	blockMode.CryptBlocks(plainText, cipherText)
	plainText = pkcs5UnPadding(plainText, blockSize)
	if plainText == nil {
		err = xerrors.Errorf("fail to decode text")
	}
	return
}

func checkKey(key []byte) bool {
	switch len(key) {
	case 16, 24, 32:
		return true
	}
	return false
}

func pkcs5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func pkcs5UnPadding(origData []byte, blockSize int) []byte {
	length := len(origData)
	// remove the last unpadding bytes
	unpadding := int(origData[length-1])
	if unpadding <= 0 || unpadding > blockSize {
		return nil
	}
	padData := origData[(length - unpadding):]
	for _, v := range padData {
		if v != byte(unpadding) {
			return nil
		}
	}
	return origData[:(length - unpadding)]
}

// ZeroPadding zero padding
func ZeroPadding(cipherText []byte, blockSize int) []byte {
	padding := blockSize - len(cipherText)%blockSize
	padText := bytes.Repeat([]byte{0}, padding) // 用0填充

	return append(cipherText, padText...)
}
