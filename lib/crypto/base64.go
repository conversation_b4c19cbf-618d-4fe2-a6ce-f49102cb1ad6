package crypto

import (
	"encoding/base64"
	"git.keepfancy.xyz/back-end/frameworks/lib/zerocopy"
)

func Base64Encode(IData interface{}) (str string) {
	var data []byte
	switch vv := IData.(type) {
	case string:
		data = zerocopy.StrToBytes(vv)
	case []byte:
		data = vv
	default:
		return ""
	}
	str = base64.StdEncoding.EncodeToString(data)
	return
}

func Base64Decode(str string) (data []byte, err error) {
	data, err = base64.StdEncoding.DecodeString(str)
	return
}
