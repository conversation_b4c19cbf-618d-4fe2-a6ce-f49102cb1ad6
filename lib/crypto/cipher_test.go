package crypto

import (
	"bytes"
	"git.keepfancy.xyz/back-end/frameworks/lib/zerocopy"

	"fmt"
	"testing"
)

func TestAes(t *testing.T) {
	data := "http://gitlab.boomegg.net/forevernine.com/baseRepo/vendor/merge_requests/new?merge_request%5Bsource_branch%5D=develop"
	// key, _ := Md5SumStr("testkey")
	key := "d9d7e8f297a6c1719fe4c891c862b28f"
	enc, err := AesEncrypt(data, key)
	if err != nil {
		t.Fail()
		return
	}
	// fmt.Println("len is ", len(enc))
	// for _, d := range enc {
	//	fmt.Print(d, ", ")
	// }
	// decKey := "87c247afd8744284964641b58b141ccc"
	plain, err := AesDecrypt(enc, key)
	if err != nil {
		fmt.Println(err.<PERSON>rror())
		t.Fail()
		return
	}
	if len(plain) != len(data) {
		t.Fail()
		return
	}
	if !bytes.Equal(plain, zerocopy.StrToBytes(data)) {
		t.Fail()
		return
	}
}
