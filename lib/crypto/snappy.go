package crypto

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/zerocopy"
	"github.com/golang/snappy"
	"golang.org/x/xerrors"
)

func Compress(IData interface{}) (out []byte, err error) {
	var data []byte
	switch vv := IData.(type) {
	case string:
		data = zerocopy.StrToBytes(vv)
	case []byte:
		data = vv
	default:
		err = xerrors.Errorf("data type not supported")
		return
	}
	out = snappy.Encode(nil, data)
	return
}

func UnCompress(data []byte) (out []byte, err error) {
	out, err = snappy.Decode(nil, data)
	if err != nil {
		err = xerrors.Errorf(err.<PERSON><PERSON>r())
	}
	return
}
