package jobqueue

import (
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"sync"
	"testing"
	"time"
)

func TestSimpleQueueJob(t *testing.T) {
	jobQueue, err := NewOrderJobQueue(1000)
	if err != nil {
		return
	}

	defer jobQueue.Close()

	var wg sync.WaitGroup
	pushCount := 10
	for i := 0; i < pushCount; i++ {
		wg.Add(1)
		go func(i int) {
			// defer wg.Done()

			key := fmt.Sprintf("%d", time.Now().UnixNano())

			pCnt := 1
			if i%2 == 0 {
				pCnt = 2
			}

			for m := 0; m < pCnt; m++ {
				errPush := jobQueue.Push(key, func() {
					t.Logf("do something start %d - %d\n", i, m)
					t.Logf("uid:%s, queue update job cnt %d", key, i)
					time.Sleep(50 * time.Millisecond)
					t.Logf("do something end %d - %d\n", i, m)
				})
				if errPush != nil {
					t.<PERSON><PERSON><PERSON>("Error pushing job: %v", errPush)
				}
			}
		}(i)
	}

	// 等待所有push任务完成
	wg.Wait()
}

func TestOrderQueue(t *testing.T) {
	q, err := NewOrderJobQueue(10)
	if err != nil {
		t.Logf("new order queue failed %s\n", err.Error())
		t.FailNow()
	}

	cnt := 100
	ary := make([]int, cnt)
	var (
		wg sync.WaitGroup
		m  sync.Map // 存储当前执行函数列表
	)
	wg.Add(cnt)
	for i := 0; i < cnt; i++ {
		ary[i] = i
		k := strconv.Itoa(i)
		j := i
		time.Sleep(time.Millisecond)
		if err = q.Push(k, func() {
			m.Store(j, j)
			wg.Done()
		}); err != nil {
			t.Logf("push error %s\n", err.Error())
			t.FailNow()
		}
	}
	wg.Wait()

	ary2 := make([]int, 0, cnt)
	m.Range(func(k, v interface{}) bool {
		i := k.(int)
		ary2 = append(ary2, i)
		return true
	})
	sort.Ints(ary2)
	t.Logf("ary \n%v\n %v", ary, ary2)
	if !reflect.DeepEqual(ary, ary2) {
		t.Logf("ary : %v\n", ary)
		t.Logf("ary2: %v\n", ary2)
		t.FailNow()
	}
}

func TestOrderQueue_Lock(t *testing.T) {
	q, err := NewOrderJobQueue(10)
	if err != nil {
		t.Logf("new order queue failed %s\n", err.Error())
		t.FailNow()
	}

	var ary, ary2 []int
	cb := func(i int) {
		t.Logf("get %d\n", i)
		ary = append(ary, i)
		time.Sleep(50 * time.Millisecond)
		t.Logf("set %d\n", i)
		ary2 = append(ary2, i)
	}

	for i := 0; i < 5; i++ {
		n := i
		q.Push("1001", func() {
			cb(n)
		})
	}
	time.Sleep(time.Second * 3)
	if !reflect.DeepEqual(ary, ary2) {
		t.Logf("ary %v\n", ary)
		t.Logf("ary2 %v\n", ary2)
		t.FailNow()
	}
}
