package jobqueue

import (
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"sync"
)

type orderWorker struct {
	ch chan *orderJob
}

func newOrderWorker() *orderWorker {
	w := &orderWorker{
		ch: make(chan *orderJob, 1),
	}
	return w
}

func (w *orderWorker) work(doneCh chan<- *orderJobDone, closeCh <-chan bool, wg *sync.WaitGroup) {
	for {
		select {
		case j := <-w.ch:
			safego.SafeFunc(j.fn)

			doneCh <- &orderJobDone{
				key: j.key,
				ch:  w.ch,
			}
		case <-closeCh:
			wg.Done()
			return
		}
	}
}
