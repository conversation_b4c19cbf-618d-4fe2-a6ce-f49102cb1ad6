// Package jobqueue 任务调度有序作业队列（OrderJobQueue）
// 支持并发操作和基于键（key）的作业去重。队列内部通过一个通道（ch）接收作业，
// 并使用多个工作线程（worker）来并发执行作业。作业以orderJob结构体的形式表示，
// 其中包含一个键和一个回调函数。用户可以通过调用Push方法将作业加入队列，
// 通过调用Close或GracefulClose方法关闭队列。
// 适用于需要处理一系列有序任务的场景，切这些任务可能需要并发执行但又必须保持特定顺序
// 同一个Key，如果有在执行的job，则后面会排队执行
package jobqueue

import (
	"container/list"
	"errors"
	"sync"
	"sync/atomic"
)

type orderJob struct {
	key string
	fn  func()
}

type orderJobDone struct {
	key string
	ch  chan *orderJob
}

// OrderCallback callback function
type OrderCallback func(k string, fn func())

const (
	orderQueueStatRunning = iota
	orderQueueStatClosed
	orderQueueStatGracefulClosed
)

// OrderJobQueue order queue of job
type OrderJobQueue struct {
	size       int
	ch         chan *orderJob // job receive channel
	bList      *list.List     // job backup list when conflict occurs
	lockMap    sync.Map       // lock map of job's key
	busy       bool           // whether all workers are busy
	closed     int32          // "0" for running "1" for closed
	closeWg    sync.WaitGroup
	closeCh    chan bool
	doneCh     chan *orderJobDone // job done channel
	idleWorker []chan *orderJob   // idle worker's job channel
}

var q *OrderJobQueue

// NewOrderJobQueue create an OrderJobQueue
func NewOrderJobQueue(size int) (q *OrderJobQueue, err error) {
	q = &OrderJobQueue{
		size:  0,
		ch:    make(chan *orderJob, size),
		bList: list.New(),
		// lockMap:    make(map[string]bool),
		lockMap:    sync.Map{},
		busy:       false,
		closed:     orderQueueStatRunning,
		closeWg:    sync.WaitGroup{},
		closeCh:    make(chan bool, size),
		doneCh:     make(chan *orderJobDone, size),
		idleWorker: make([]chan *orderJob, 0, size),
	}

	q.payout(size)

	go q.loop()
	return
}

func (q *OrderJobQueue) payout(size int) {
	for i := 0; i < size; i++ {
		w := newOrderWorker()
		q.idleWorker = append(q.idleWorker, w.ch)
		go w.work(q.doneCh, q.closeCh, &q.closeWg)
	}
	q.size += size
}

func (q *OrderJobQueue) IdleLen() int {
	return len(q.idleWorker)
}

// queue errors
// var (
//	//ErrOrderQueueClosed = errors.New("order queue closed")
//	//ErrOrderQueueFull = errors.New("order queue's channel is full")
//	//ErrOrderQueueBusy = errors.New("order queue's workers are all busy")
// )

// Push func push job to queue
func (q *OrderJobQueue) Push(k string, cb func()) error {
	n := atomic.LoadInt32(&q.closed)
	if n == orderQueueStatClosed || n == orderQueueStatGracefulClosed {
		return errors.New("order queue closed")
	}

	currSize := q.size
	if currSize < 100 {
		q.payout(currSize * 2)
	} else {
		q.payout(currSize * (1 / 4))
	}

	j := &orderJob{
		key: k,
		fn:  cb,
	}
	var err error
	select {
	case q.ch <- j:
	default:
		err = errors.New("order queue's channel is full")
	}
	return err
}

// Size return queue size
func (q *OrderJobQueue) Size() int {
	return q.size
}

// Close func close queue
func (q *OrderJobQueue) Close() {
	b := atomic.CompareAndSwapInt32(&q.closed, orderQueueStatRunning, orderQueueStatClosed)
	if !b {
		return
	}
	// q.size + 1, extra 1 is for "loop" goroutine
	q.closeWg.Add(q.size + 1)
	q.closeWg.Wait()
}

// GracefulClose graceful close queue.
// Queue exits when all jobs have done.
func (q *OrderJobQueue) GracefulClose() {
	b := atomic.CompareAndSwapInt32(&q.closed, orderQueueStatRunning, orderQueueStatGracefulClosed)
	if !b {
		return
	}
	// q.size + 1, extra 1 is for "loop" goroutine
	q.closeWg.Add(q.size + 1)
	q.closeWg.Wait()
}

func (q *OrderJobQueue) closeQueue() {
	q.closeWg.Done()
	for i := 0; i < q.size; i++ {
		q.closeCh <- true
	}
}

// perform only one job in one loop
func (q *OrderJobQueue) loop() {
	// tk := time.NewTicker(5 * time.Millisecond)
	for {
		n := atomic.LoadInt32(&q.closed)
		if n == orderQueueStatClosed {
			q.closeQueue()
			return
		} else if n == orderQueueStatGracefulClosed {
			if q.bList.Len() == 0 && len(q.ch) == 0 {
				q.closeQueue()
				return
			}
		}

		// First recycle idle workers
		if !q.busy {
			select {
			case dn := <-q.doneCh:
				q.putCh(dn.ch, dn.key)
			default:
				// Note: Nothing is wrong. It means there is no worker to recycle.
			}
		} else {
			dn := <-q.doneCh
			q.putCh(dn.ch, dn.key)
		}

		// Handle the job of the backup list first, if the job's key isn't locked.
		// Otherwise, handle the job of queue channel.
		e := q.bList.Front()
		if e != nil {
			j := e.Value.(*orderJob)
			_, loaded := q.lockMap.LoadOrStore(j.key, true)
			if !loaded {
				// Get an idle worker to work
				ch, err := q.getCh()
				if err != nil {
					// Fatal: should not happend.
					// If all workers are busy, it would continue the loop in the previous code.
				} else {
					q.bList.Remove(e)
					q.lockMap.Store(j.key, true)
					ch <- j
				}
				continue
			}
		}

		// If "q.ch" is empty and "q.bList" is not empty, "select" would blocked.
		// So add "default" case.
		if q.bList.Len() > 0 {
			select {
			case j := <-q.ch:
				q.doAJob(j)
			default:
			}
		} else {
			select {
			case j := <-q.ch:
				q.doAJob(j)
			}
		}
	}
}

func (q *OrderJobQueue) doAJob(j *orderJob) {
	// If the job's key locked, push job to back up list.
	_, loaded := q.lockMap.LoadOrStore(j.key, true)
	if loaded {
		if q.bList.Len() >= q.size {
			// Warning: Backup list is full. Could not happend is our business models.
		} else {
			q.bList.PushBack(j)
		}
		return
	}

	// Get an idle worker to work
	ch, err := q.getCh()
	if err != nil {
		// Fatal: should not happend.
		// If all workers are busy, it would continue the loop in the previous code.
		return
	}

	// q.lockMap[j.key] = true
	ch <- j
}

func (q *OrderJobQueue) getCh() (chan *orderJob, error) {
	ln := len(q.idleWorker)
	if ln == 0 {
		return nil, errors.New("order queue's workers are all busy")
	}
	ch := q.idleWorker[ln-1]
	q.idleWorker = q.idleWorker[:ln-1]
	if len(q.idleWorker) == 0 {
		q.busy = true
	}
	return ch, nil
}

func (q *OrderJobQueue) putCh(ch chan *orderJob, key string) {
	q.idleWorker = append(q.idleWorker, ch)
	// delete(q.lockMap, key)
	q.lockMap.Delete(key)
	q.busy = false
}
