package logdog

import (
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"testing"
	"time"
)

// TestKafkaHook 测试 Kafka 钩子功能
func TestKafkaHook(t *testing.T) {
	// 设置测试配置
	setupTestConfig()

	// 设置基础日志
	SetupLog("debug", true)

	// 添加 Kafka 钩子
	err := AddHookLog2KafkaWithTopic("test-log-topic")
	if err != nil {
		t.Logf("Failed to add kafka hook (expected in test environment): %v", err)
		return
	}
	defer StopKafkaHook()

	// 测试不同级别的日志
	logrus.Debug("This is a debug message - should NOT go to Kafka")
	logrus.Info("This is an info message - should NOT go to Kafka")
	logrus.Warn("This is a warn message - should NOT go to Kafka")
	logrus.Error("This is an error message - should go to Kafka")
	logrus.WithFields(logrus.Fields{
		"user_id": 12345,
		"action":  "login",
	}).Error("User login failed - should go to Kafka with fields")

	// 等待消息发送
	time.Sleep(2 * time.Second)
}

// TestKafkaHookWithoutTopic 测试不指定主题的 Kafka 钩子
func TestKafkaHookWithoutTopic(t *testing.T) {
	// 设置测试配置
	setupTestConfig()

	// 设置基础日志
	SetupLog("debug", true)

	// 添加 Kafka 钩子（使用配置文件中的默认主题）
	err := AddHookLog2Kafka()
	if err != nil {
		t.Logf("Failed to add kafka hook (expected in test environment): %v", err)
		return
	}
	defer StopKafkaHook()

	// 测试错误日志
	logrus.WithFields(logrus.Fields{
		"error_code": "E001",
		"module":     "auth",
	}).Error("Authentication failed")

	// 等待消息发送
	time.Sleep(2 * time.Second)
}

// TestKafkaHookLevels 测试钩子只处理指定级别的日志
func TestKafkaHookLevels(t *testing.T) {
	hook := &KafkaHook{}
	levels := hook.Levels()

	expectedLevels := []logrus.Level{
		logrus.ErrorLevel,
		logrus.FatalLevel,
		logrus.PanicLevel,
	}

	if len(levels) != len(expectedLevels) {
		t.Errorf("Expected %d levels, got %d", len(expectedLevels), len(levels))
	}

	for i, level := range levels {
		if level != expectedLevels[i] {
			t.Errorf("Expected level %v at index %d, got %v", expectedLevels[i], i, level)
		}
	}
}

// setupTestConfig 设置测试配置
func setupTestConfig() {
	// 设置 Kafka 配置
	viper.Set("kafka-producer.brokers", []string{"localhost:9092"})
	viper.Set("kafka-producer.topic", "test-topic")
	viper.Set("kafka-producer.timeout", 10)
	viper.Set("kafka-producer.sasl_mechanism", "")
	viper.Set("kafka-producer.sasl_username", "")
	viper.Set("kafka-producer.sasl_password", "")
	viper.Set("kafka-producer.ssl_ca_location", "")
}

// BenchmarkKafkaHook 性能测试
func BenchmarkKafkaHook(b *testing.B) {
	setupTestConfig()
	SetupLog("error", false)

	// 创建钩子但不添加到 logrus（避免实际发送）
	hook, err := NewKafkaHookWithTopic("benchmark-topic")
	if err != nil {
		b.Skip("Kafka not available for benchmark")
	}
	defer hook.Close()

	entry := &logrus.Entry{
		Logger:  logrus.StandardLogger(),
		Data:    logrus.Fields{"test": "value"},
		Time:    time.Now(),
		Level:   logrus.ErrorLevel,
		Message: "Benchmark test message",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		hook.Fire(entry)
	}
}
