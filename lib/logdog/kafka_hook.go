package logdog

import (
	"context"
	"encoding/json"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
	"sync"
	"time"
)

// KafkaHook 使用 event kafka 基础设施的 Kafka 日志钩子
type KafkaHook struct {
	sender event.Sender
	topic  string
	mu     sync.RWMutex
}

// LogMessage 发送到 Kafka 的日志消息结构
type LogMessage struct {
	Timestamp string                 `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields"`
	Caller    string                 `json:"caller,omitempty"`
	Source    string                 `json:"source"`
}

// Fire 实现 logrus.Hook 接口
func (hook *KafkaHook) Fire(entry *logrus.Entry) error {
	hook.mu.RLock()
	sender := hook.sender
	topic := hook.topic
	hook.mu.RUnlock()

	if sender == nil {
		return fmt.Errorf("kafka sender not initialized")
	}

	// 构建日志消息
	logMsg := LogMessage{
		Timestamp: entry.Time.Format(time.RFC3339Nano),
		Level:     entry.Level.String(),
		Message:   entry.Message,
		Fields:    make(map[string]interface{}),
		Source:    "logdog",
	}

	// 复制字段
	for k, v := range entry.Data {
		logMsg.Fields[k] = v
	}

	// 添加调用者信息
	if entry.HasCaller() {
		logMsg.Caller = fmt.Sprintf("%s:%d", entry.Caller.File, entry.Caller.Line)
	}

	// 序列化为 JSON
	msgBytes, err := json.Marshal(logMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal log message: %w", err)
	}

	// 发送到 Kafka
	kafkaMsg := event.NewMessage(
		fmt.Sprintf("log-%s-%d", entry.Level.String(), entry.Time.Unix()),
		msgBytes,
	)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if topic != "" {
		err = sender.SendWithTopic(ctx, topic, kafkaMsg)
	} else {
		err = sender.Send(ctx, kafkaMsg)
	}

	if err != nil {
		return fmt.Errorf("failed to send log to kafka: %w", err)
	}

	return nil
}

// Levels 返回此钩子处理的日志级别（只处理 Error、Fatal、Panic）
func (hook *KafkaHook) Levels() []logrus.Level {
	return []logrus.Level{
		logrus.InfoLevel,
		logrus.ErrorLevel,
		logrus.FatalLevel,
		logrus.PanicLevel,
	}
}

// Close 关闭 Kafka 发送器
func (hook *KafkaHook) Close() error {
	hook.mu.Lock()
	defer hook.mu.Unlock()

	if hook.sender != nil {
		return hook.sender.Close()
	}
	return nil
}

// NewKafkaHookWithTopic 创建带指定主题的 Kafka 钩子
func NewKafkaHookWithTopic(topic string, opts ...event.Option) (*KafkaHook, error) {
	// 将 topic 选项添加到选项列表中
	allOpts := append(opts, event.WithTopic(topic))

	sender, err := event.NewKafkaSender(allOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to create kafka sender: %w", err)
	}

	return &KafkaHook{
		sender: sender,
		topic:  topic,
	}, nil
}
