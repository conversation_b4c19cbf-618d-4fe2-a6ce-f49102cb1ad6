package logdog

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/logkafka"
	"github.com/sirupsen/logrus"
)

const LabelSource = "hook"

type TLogHook struct {
	Fired bool
}

func (hook *TLogHook) Fire(entry *logrus.Entry) error {
	hook.Fired = true
	entry.Data["source"] = LabelSource // 每条日志添加一个来源标签

	logger := logkafka.GetLogger()

	// trace, debug不上报到Kafka
	switch entry.Level {
	case logrus.InfoLevel:
		logger.Info(tranField2KafkaFormat(entry).GetDetail())
	case logrus.WarnLevel:
		logger.Warn(tranField2KafkaFormat(entry).GetDetail())
	case logrus.ErrorLevel:
		logger.Error(tranField2KafkaFormat(entry).GetDetail())
	case logrus.FatalLevel:
		logger.Info(tranField2KafkaFormat(entry).GetDetail())
	case logrus.PanicLevel:
		logger.Panic(tranField2KafkaFormat(entry).GetDetail())
	case logrus.TraceLevel, logrus.DebugLevel:
	default:
	}

	return nil
}

func (hook *TLogHook) Levels() []logrus.Level {
	// 确定那些日志触发Hook
	return []logrus.Level{
		logrus.TraceLevel,
		logrus.DebugLevel,
		logrus.InfoLevel,
		logrus.WarnLevel,
		logrus.ErrorLevel,
		logrus.FatalLevel,
		logrus.PanicLevel,
	}
}

func tranField2KafkaFormat(entry *logrus.Entry) *logkafka.Fields {
	// fields 转换到 logrus fields
	f := logkafka.NewFields()
	for k, v := range entry.Data {
		f.Add(k, v)
	}

	f.Add("context", entry.Message)

	return f
}

func AddHookLog2Kafka() {
	logrus.AddHook(&TLogHook{})
}
