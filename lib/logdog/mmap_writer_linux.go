package logdog

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"os"
	"syscall"
	"time"
)

var (
	// defaultCacheSize 默认页缓存大小
	defaultCacheSize = 4096 * 1000
	writer           *mmapWriter
)

// getCacheSize 获得缓存大小
const MmapCache = 1000

func getCacheSize() int64 {
	cache := MmapCache * syscall.Getpagesize()
	if cache < defaultCacheSize {
		return int64(defaultCacheSize)
	}
	return int64(cache)
}

type mmapWriter struct {
	outputFile bool
	outputErr  bool
	fileName   string
	fileDir    string

	file     *os.File
	fileHour int
	// 页缓存
	pageCache []byte
	// 当前文件映射位置
	at int64
	// 文件大小
	fileSize int64
	// 是否使用mmap
	bUseMmap bool
	// 当前mmap页开始位置
	cacheOrigin int64
}

func newmmapWriter(fileName string, dir string, outputErr bool) *mmapWriter {
	writer = new(mmapWriter)
	writer.outputErr = outputErr
	writer.outputFile = false
	if fileName != "" && dir != "" {
		if err := os.MkdirAll(dir, 0774); err == nil {
			writer.outputFile = true
			writer.fileName = fileName
			writer.fileDir = dir
		}
	}
	writer.fileHour = -1

	return writer
}

func stopMmapWriter() {
	logrus.Infoln("mmap writer关闭")
	if writer != nil {
		writer.unmap()
		writer.file.Close()
	}
}

func (w *mmapWriter) getFileSize() int64 {
	if w.file == nil {
		fmt.Println("未打开文件")
		return w.fileSize
	}
	fileInfo, err := w.file.Stat()
	if err != nil {
		fmt.Printf("获取文件信息错误：%+v\n", err)
		return w.fileSize
	}

	w.fileSize = fileInfo.Size()
	return w.fileSize
}

func (w *mmapWriter) mmapSeek() {
	w.getFileSize()
	w.at = w.fileSize
}

func (w *mmapWriter) getFile() {
	now := time.Now().Local()
	iNowHour := now.Hour()
	if iNowHour == w.fileHour {
		return
	}

	if iNowHour == w.fileHour {
		return
	}
	w.fileHour = iNowHour

	filePath := fmt.Sprintf("%s/%s_%d_%02d_%02d_%02d.log", w.fileDir, w.fileName, now.Year(),
		now.Month(), now.Day(), iNowHour)

	if w.file != nil {
		w.unmap()
		w.file.Close()
	}

	var err error
	if w.file, err = os.OpenFile(filePath, os.O_RDWR|os.O_APPEND|os.O_CREATE, 0664); err != nil {
		fmt.Println("open log file failed:", err)
		return
	}
	w.mmapSeek()
}

func (w *mmapWriter) buffConsume(buf []byte) (int, error) {
	w.getFile()
	for i := 0; i < 1; i++ {
		if len(buf) >= int(w.fileSize)-int(w.at) {
			if err := w.allocate(); err != nil {
				fmt.Printf("allocate mmap fail:%s\n", err.Error())
				break
			}
		}
		if !w.bUseMmap {
			break
		}
		cacheAt := w.at - w.cacheOrigin
		if len(buf)+int(cacheAt) < len(w.pageCache) {
			copy(w.pageCache[cacheAt:], buf)
			w.at += int64(len(buf))
			// fmt.Println("从mmap写")
			return len(buf), nil
		}
	}
	if w.file != nil {
		// fmt.Println("直接写文件")
		return w.file.Write(buf)
	}
	return len(buf), nil
}

// 写日志， 根据 logWriter 的状态判断是否需要写入标准错误和文件
func (w *mmapWriter) Write(buf []byte) (int, error) {
	if w.outputErr {
		// Stderr -->Stdout .(skyWang)
		os.Stdout.Write(buf)
	}
	if w.outputFile {
		return w.buffConsume(buf)
	}
	return len(buf), nil
}

func (w *mmapWriter) unmap() error {
	if len(w.pageCache) == 0 {
		fmt.Println("没有申请页缓存，不需要关闭")
		return nil
	}
	// 关闭映射
	if err := syscall.Munmap(w.pageCache); err != nil {
		fmt.Printf("unmap关闭映射(cachelen:%d)失败，%s\n", len(w.pageCache), err.Error())
		return err
	}
	// 将未写入的内容清空
	// 如果未清空，在文件末位未写入位置，将会出现大量占位符
	err := syscall.Ftruncate(int(w.file.Fd()), w.at)
	if err != nil {
		fmt.Printf("unmap ftruncate file fail %s\n", err.Error())
	}
	// fmt.Println("关闭页缓存映射成功")
	return nil
}

func (w *mmapWriter) allocate() error {
	err := w.unmap()
	if err != nil {
		w.bUseMmap = false
		fmt.Printf("关闭映射失败，%s\n", err.Error())
		return err
	}
	pageCacheSize := getCacheSize()
	page := int64(w.at / 4096)
	cacheOrigin := page * 4096
	// 先扩展文件
	err = syscall.Ftruncate(int(w.file.Fd()), cacheOrigin+pageCacheSize)
	if err != nil {
		w.bUseMmap = false
		fmt.Printf("allocate ftruncate失败，%s\n", err.Error())
		return err
	}
	// fmt.Printf("Ftruncate 成功（fd：%d at：%d pageCacheSize：%d）\n",
	// w.file.Fd(), w.at, pageCacheSize)
	// 扩展后的内容映射到mmap
	data, err := syscall.Mmap(int(w.file.Fd()), cacheOrigin, int(pageCacheSize), syscall.PROT_WRITE, syscall.MAP_SHARED)
	if err != nil {
		w.bUseMmap = false
		fmt.Printf("allocate mmap 失败，fd:%d at:%d err=%s\n",
			w.file.Fd(), w.at, err.Error())
		return err
	}
	w.cacheOrigin = cacheOrigin
	w.fileSize = cacheOrigin + pageCacheSize
	w.pageCache = data
	w.bUseMmap = true
	// fmt.Printf("重新分配页缓存(pagecache=%d,len(data)=%d,at:%d成功\n",
	// 	pageCacheSize, len(data), w.at)
	return nil
}
