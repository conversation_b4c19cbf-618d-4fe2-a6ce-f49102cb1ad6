package logdog

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/logkafka"
	"github.com/sirupsen/logrus"
	"testing"
	"time"
)

func TestLoglog(t *testing.T) {
	SetupLog("trace", false)

	logkafka.GetLogger().Start(
		logkafka.Level(logkafka.LevelTrace), // 日志级别，读取配置文件，设置log.SetLevel(LevelTrace)
		// PrintConsole(false),  // 默认输出到控制台
		logkafka.Module("tLog")) // 模块名

	AddHookLog2Kafka()

	entry := logrus.WithFields(logrus.Fields{"playerID": "12345678"})
	entry.Info("Haha Info")
	entry.Warn("Haha Warn")
	// entry.Error("Haha Error")
	// entry.Fatal("Haha Fatal")

	time.Sleep(2 * time.Second)
}
