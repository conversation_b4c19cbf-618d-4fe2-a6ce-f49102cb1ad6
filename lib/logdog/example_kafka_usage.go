package logdog

import (
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// ExampleKafkaUsage 展示如何在服务中使用 Kafka 日志功能
func ExampleKafkaUsage() {
	// 1. 首先设置配置文件，包含 kafka-producer 配置节
	// 配置示例：
	// kafka-producer:
	//   brokers: ["localhost:9092"]
	//   topic: "service-logs"
	//   timeout: 10
	//   sasl-mechanism: ""
	//   sasl-username: ""
	//   sasl-password: ""

	// 2. 初始化基础日志系统
	SetupLog("info", false)

	// 3. 添加 Kafka 日志钩子（使用配置文件中的默认主题）
	if err := AddHookLog2Kafka(); err != nil {
		logrus.Errorf("Failed to add Kafka log hook: %v", err)
		return
	}

	// 或者指定特定的主题
	// if err := AddHookLog2KafkaWithTopic("custom-log-topic"); err != nil {
	//     logrus.Errorf("Failed to add Kafka log hook: %v", err)
	//     return
	// }

	// 4. 正常使用 logrus 记录日志
	// 只有 Error、Fatal、Panic 级别的日志会发送到 Kafka

	// 这些日志不会发送到 Kafka
	logrus.Debug("Debug message")
	logrus.Info("Info message")
	logrus.Warn("Warning message")

	// 这些日志会发送到 Kafka
	logrus.Error("Error occurred")
	logrus.WithFields(logrus.Fields{
		"user_id":    12345,
		"action":     "payment",
		"error_code": "PAY001",
	}).Error("Payment processing failed")

	logrus.Fatal("Fatal error") // 注意：这会终止程序

	// 5. 在程序退出时清理资源
	defer func() {
		if err := StopKafkaHook(); err != nil {
			logrus.Errorf("Failed to stop Kafka hook: %v", err)
		}
		StopLog()
	}()
}

// ExampleServiceInitialization 展示在服务初始化中如何集成 Kafka 日志
func ExampleServiceInitialization() {
	// 在服务的 Init() 方法中添加以下代码：

	// 1. 读取配置文件
	viper.SetConfigFile("./config.yml")
	if err := viper.ReadInConfig(); err != nil {
		panic("Failed to read config file")
	}

	// 2. 设置基础日志
	logLevel := viper.GetString("log_level")
	prettyPrint := viper.GetBool("log_json")
	SetupLog(logLevel, prettyPrint)

	// 3. 如果需要文件日志
	if viper.GetBool("log_write") {
		fileName := viper.GetString("rpc_server_name")
		logDir := viper.GetString("log_dir")
		SetWriteFile(fileName, logDir, logLevel)
	}

	// 4. 添加 Kafka 日志钩子
	if viper.IsSet("kafka-producer") {
		logTopic := viper.GetString("kafka-producer.topic")
		if logTopic == "" {
			logTopic = "service-error-logs" // 默认主题
		}

		if err := AddHookLog2KafkaWithTopic(logTopic); err != nil {
			logrus.Warnf("Failed to initialize Kafka logging: %v", err)
		} else {
			logrus.Info("Kafka error logging initialized successfully")
		}
	}
}

// ExampleConfigFile 展示配置文件的示例格式
const ExampleConfigFile = `
# 基础日志配置
log_level: "info"
log_write: true
log_dir: "./logs"
log_json: false
rpc_server_name: "my-service"

# Kafka 生产者配置（用于错误日志）
kafka-producer:
  brokers: ["localhost:9092", "localhost:9093"]
  topic: "service-error-logs"
  timeout: 10
  sasl_mechanism: ""
  sasl_username: ""
  sasl_password: ""
  ssl_ca_location: ""
  use_ssl: false
  ssl_insecure_skip_verify: false

# 其他服务配置...
rpc_port: 8080
http_port: 8081
`
