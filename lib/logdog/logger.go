// Package logdog 提供接口初始化日志的记录， 外部使用 logrus 提供的原生方法来记录日志
package logdog

import (
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"io"
	"os"
	"path"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

const IsUseMMap = true    // 启用mmap优化日志写入
const IsStdErr = true     // 标准输出
const IsShowCaller = true // 显示调用者

// log.WithFields logrus推荐使用Fields来进行精细化的、结构化的信息记录

// RecoverAndLog 从 panic 中恢复，如果恢复了 panic，则打印错误日志
func RecoverAndLog(args ...interface{}) {
	if x := recover(); x != nil {
		logrus.Errorf("panic: %v args: %v \n stack: \n %s", x, args, string(debug.Stack()))
	}
}

// FormatCaller 格式化 caller 名称 快速获取调用函数 默认传0， 如果打印有封装需要传1
func FormatCaller(skip int) string {
	pc, filePath, line, ok := runtime.Caller(skip + 1)
	if !ok {
		return "unknow"
	}
	funcName := runtime.FuncForPC(pc).Name()
	return fmt.Sprintf("[%s][%s:%d]",
		funcName[strings.LastIndex(funcName, "/")+1:], filePath[strings.LastIndex(filePath, "/")+1:], line)
}

// logrus在记录Levels()返回的日志级别的消息时，会触发HOOK。
// 然后，按照Fire方法定义的内容，修改logrus.Entry。
// type Hook interface {
//     Levels() []Level
//     Fire(*Entry) error
// }

// 非并发安全的 writer， 用于记录日志
type logWriter struct {
	outputFile bool
	outputErr  bool
	fileName   string
	fileDir    string

	file     *os.File
	fileHour int
	muLock   sync.Mutex
}

func newLogWriter(fileName string, dir string, outputErr bool) *logWriter {
	lw := new(logWriter)
	lw.outputErr = outputErr
	lw.outputFile = false
	if fileName != "" && dir != "" {
		if err := os.MkdirAll(dir, 0774); err == nil {
			lw.outputFile = true
			lw.fileName = fileName
			lw.fileDir = dir
		}
	}
	lw.fileHour = -1

	return lw
}

// 日志文件按小时区分， 如果当前时间和上一次检测的时间相差了一个小时以上， 则打开一个新文件用以记录日志
func (w *logWriter) getFile() {
	now := time.Now().Local()
	iNowHour := now.Hour()
	if iNowHour == w.fileHour {
		return
	}

	w.muLock.Lock()
	if iNowHour == w.fileHour {
		w.muLock.Unlock()
		return
	}
	w.fileHour = iNowHour

	fileName := fmt.Sprintf("%s/%s_%d-%02d-%02d-%02d.log", w.fileDir, w.fileName, now.Year(),
		now.Month(), now.Day(), iNowHour)

	if w.file != nil {
		w.file.Close()
	}

	var err error
	if w.file, err = os.OpenFile(fileName, os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0664); err != nil {
		fmt.Println("open log file failed:", err)
	}

	w.muLock.Unlock()
}

// 写日志， 根据 logWriter 的状态判断是否需要写入标准错误和文件
func (w *logWriter) Write(buf []byte) (int, error) {
	if w.outputErr {
		// Stderr -->Stdout .(skyWang)
		os.Stdout.Write(buf)
	}
	if w.outputFile {
		w.getFile()
		if w.file != nil {
			w.file.Write(buf)
		}
	}
	return len(buf), nil
}

func SetupLog(level string, prettyPrint bool) {

	if lv, err := logrus.ParseLevel(level); err == nil {
		logrus.SetLevel(lv)
	} else {
		fmt.Println("parse level failed:", level, err)
	}

	logrus.SetReportCaller(IsShowCaller)
	logrus.SetFormatter(&logrus.JSONFormatter{
		DisableTimestamp: false,
		// TimestampFormat:  "2006-01-02 15:04:05",
		TimestampFormat: time.RFC3339Nano,
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			s := strings.Split(f.Function, ".")
			funcName := s[len(s)-1]
			filepath, filename := path.Split(f.File)
			return funcName, filepath + filename + fmt.Sprintf(":%d", f.Line)
		},
		PrettyPrint: prettyPrint,
	})
}

func SetWriteFile(fileName string, dir string, level string) {
	var writer io.Writer
	if IsUseMMap && runtime.GOOS == dict.OSLinux {
		writer = newmmapWriter(fileName, dir, IsStdErr)
	} else {
		writer = newLogWriter(fileName, dir, IsStdErr)
	}
	logrus.SetOutput(writer)

	logrus.WithFields(logrus.Fields{
		"file_name": fileName,
		"file_dir":  dir,
		"level":     level,
		"outputErr": IsStdErr,
	}).Debug("########setup log success########")
}

func StopLog() {
	logrus.Infoln("log stop")
	if IsUseMMap && runtime.GOOS == dict.OSLinux {
		stopMmapWriter()
	}
}
