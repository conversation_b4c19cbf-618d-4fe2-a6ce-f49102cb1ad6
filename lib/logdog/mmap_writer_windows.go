package logdog

import (
	"os"

	"github.com/sirupsen/logrus"
)

func newmmapWriter(fileName string, dir string, outputErr bool) *logWriter {
	lw := new(logWriter)
	lw.outputErr = outputErr
	lw.outputFile = false
	if fileName != "" && dir != "" {
		if err := os.MkdirAll(dir, 0774); err == nil {
			lw.outputFile = true
			lw.fileName = fileName
			lw.fileDir = dir
		}
	}
	lw.fileHour = -1

	return lw
}

func stopMmapWriter() {
	logrus.Infoln("mmap writer关闭")
}
