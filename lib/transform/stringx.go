package transform

import "strings"

// Substr 截取字符串
func Substr(str string, start, length int) string {
	rs := []rune(str)
	rl := len(rs)
	end := 0
	if start < 0 {
		start = rl - 1 + start
	}
	end = start + length
	if start > end {
		start, end = end, start
	}
	if start < 0 {
		start = 0
	}
	if start > rl {
		start = rl
	}
	if end < 0 {
		end = 0
	}
	if end > rl {
		end = rl
	}
	return string(rs[start:end])
}

// SubYMDFromIdCardNum 从身份证号获取年月日
func SubYMDFromIdCardNum(idCardNum string) (int32, int32, int32) {
	var icY, icM, icD int32
	if len(idCardNum) > 0 {
		icY = Str2Int32(Substr(idCardNum, 6, 4))
		icM = Str2Int32(Substr(idCardNum, 10, 2))
		icD = Str2Int32(Substr(idCardNum, 12, 2))
	}
	return icY, icM, icD
}

// TrimAndLower 去掉字符串中的空格并且小写
func TrimAndLower(str string) string {
	return strings.ToLower(strings.ReplaceAll(str, " ", ""))
}

// TrimAndUpper 去掉字符串中的空格并且大写
func TrimAndUpper(str string) string {
	return strings.ToUpper(strings.ReplaceAll(str, " ", ""))
}
