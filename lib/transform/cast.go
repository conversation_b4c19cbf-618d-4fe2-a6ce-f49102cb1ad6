package transform

import (
	"reflect"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/sirupsen/logrus"
)

// I32ToStr int32转string
func I32ToStr(v int32) string {
	return strconv.FormatInt(int64(v), 10)
}

// Int2Str int转string
func Int2Str(v int) string {
	return strconv.Itoa(v)
}

// Str2Int64 string转int64
func Str2Int64(s string) int64 {
	v, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		logrus.WithError(err).Errorf("转换失败, s:%s", s)
		return 0
	}
	return v
}

// Str2Int32 string转int32
func Str2Int32(s string) int32 {
	v, err := strconv.Atoi(s)
	if err != nil {
		logrus.WithError(err).Errorf("转换失败, s:%s", s)
		return 0
	}
	return int32(v)
}

// Str2Int string转int
func Str2Int(s string) int {
	return int(Str2Int64(s))
}

// Str2Uint64 string转uint64
func Str2Uint64(s string) uint64 {
	v, err := strconv.ParseUint(s, 10, 64)
	if err != nil {
		logrus.WithError(err).Errorf("转换失败, s:%s", s)
		return 0
	}
	return v
}

// Str2Uint32 字符串转 uint32
func Str2Uint32(str string) uint32 {
	val, _ := strconv.ParseUint(str, 10, 32)
	return uint32(val)
}

func IntArray2Str(slice []int) string {
	var result string
	for _, i := range slice {
		result += strconv.Itoa(i) + ","
	}
	if len(result) > 0 {
		result = result[0 : len(result)-1]
	}
	return result
}

func Str2IntArray(str string) []int {
	var result []int
	if str != "" {
		ss := strings.Split(str, ",")
		for _, s := range ss {
			i, _ := strconv.Atoi(s)
			result = append(result, i)
		}
	}
	return result
}

// Uint642Str Uint64转 string
func Uint642Str(i uint64) string {
	return strconv.FormatUint(i, 10)
}

// Int642Str int64转 string
func Int642Str(i int64) string {
	return strconv.FormatInt(i, 10)
}

// Str2Time string转time
func Str2Time(s string) time.Time {
	return time.Unix(Str2Int64(s), 0)
}

// Str2Bool string 转 bool
func Str2Bool(s string) bool {
	if s == "true" || s == "1" {
		return true
	} else if s == "false" || s == "0" {
		return false
	}
	return false
}

func ByteToString(val []byte) string {
	return *(*string)(unsafe.Pointer(&val))
}

func StringToByte(val string) []byte {
	tmp1 := (*[2]uintptr)(unsafe.Pointer(&val))
	tmp2 := [3]uintptr{tmp1[0], tmp1[1], tmp1[1]}
	return *(*[]byte)(unsafe.Pointer(&tmp2))
}

func ToInt64(val interface{}) int64 {
	switch reflect.TypeOf(val).Kind() {
	case reflect.Int:
		return int64(val.(int))

	case reflect.Int8:
		return int64(val.(int8))

	case reflect.Int16:
		return int64(val.(int16))

	case reflect.Int32:
		return int64(val.(int32))

	case reflect.Int64:
		return val.(int64)

	case reflect.Uint:
		return int64(val.(uint))

	case reflect.Uint8:
		return int64(val.(uint8))

	case reflect.Uint16:
		return int64(val.(uint16))

	case reflect.Uint32:
		return int64(val.(uint32))

	case reflect.Uint64:
		return int64(val.(uint64))

	case reflect.String:
		return Str2Int64(val.(string))
	}
	return 0
}

func ToBool(val interface{}) bool {
	switch reflect.TypeOf(val).Kind() {
	case reflect.Int:
		if val.(int) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Int8:
		if val.(int8) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Int16:
		if val.(int16) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Int32:
		if val.(int32) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Int64:
		if val.(int64) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Uint:
		if val.(uint) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Uint8:
		if val.(uint8) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Uint16:
		if val.(uint16) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Uint32:
		if val.(uint32) > 0 {
			return true
		} else {
			return false
		}

	case reflect.Uint64:
		if val.(uint64) > 0 {
			return true
		} else {
			return false
		}

	case reflect.String:
		// if len(val.(string)) > 0 {
		// 	return true
		// } else {
		// 	return false
		// }
		return Str2Bool(val.(string))
	}
	return false
}

func GetNumberBool(b bool) int {
	v := 0
	if b {
		v = 1
	}
	return v
}

func GetBoolStr(b bool) string {
	v := 0
	if b {
		v = 1
	}
	return Int2Str(v)
}
