package transform

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"reflect"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/sirupsen/logrus"
)

func RandRange(low int, high int) int {
	if low == high {
		return low
	}
	if low > high {
		tmp := low
		low = high
		high = tmp
	}
	return low + rand.Intn(high-low)
}

func ToStrings(playerIDs ...uint64) []string {
	var fields []string
	for _, playerID := range playerIDs {
		field := strconv.Itoa(int(playerID))
		fields = append(fields, field)
	}
	return fields
}

func ToUint64s(dataArray []string) []uint64 {
	var playerIDs []uint64
	for _, data := range dataArray {
		playerID, err := strconv.Atoi(data)
		if err != nil {
			logrus.Errorf("玩家身份号%v不是合法数字:%v", data, err)
			continue
		}
		playerIDs = append(playerIDs, uint64(playerID))
	}
	return playerIDs
}

// Struct2String 自动遍历结构体字段值并使用分隔符连接
func Struct2String(obj interface{}, delimiter string) (string, error) {
	val := reflect.ValueOf(obj)
	if val.Kind() != reflect.Struct {
		return "", fmt.Errorf("input is not a struct")
	}

	var parts []string
	for i := 0; i < val.NumField(); i++ {
		fieldVal := val.Field(i)

		// 尝试将字段值转换为字符串
		var fieldValue string
		switch fieldVal.Kind() {
		case reflect.String:
			fieldValue = fieldVal.String()
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			fieldValue = fmt.Sprintf("%d", fieldVal.Int())
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			fieldValue = fmt.Sprintf("%d", fieldVal.Uint())
		case reflect.Float32, reflect.Float64:
			fieldValue = fmt.Sprintf("%f", fieldVal.Float())
		default:
			// 对于未处理的类型，可以选择忽略或返回错误
			continue // 或者：return "", fmt.Errorf("unsupported type: %s", fieldVal.Kind())
		}

		parts = append(parts, fieldValue)
	}

	return strings.Join(parts, delimiter), nil
}

// MapStrInterfaceToString map转string 以指定字符连接
func MapStrInterfaceToString(fieldValues map[string]interface{}, delimiter string) string {
	var sb strings.Builder

	for key, value := range fieldValues {
		if sb.Len() > 0 {
			sb.WriteString(delimiter)
		}
		sb.WriteString(key)
		sb.WriteString(delimiter)
		sb.WriteString(fmt.Sprintf("%v", value))
	}

	return sb.String()
}

// Struct2Map 将一个结构体对象转换为Map。

func Struct2Map[T any | ~string](obj interface{}, hash map[string]T) error {
	// TODO 临时用json转换
	objJson, err := json.Marshal(obj)
	if err != nil {
		return err
	}
	return json.Unmarshal(objJson, &hash)
}

// Map2Sturct 转换Map数据到结构体
func Map2Struct[T any | ~string](hash map[string]T, obj interface{}) error {

	structValue := reflect.ValueOf(obj).Elem()
	if structValue.Kind() != reflect.Struct {
		return fmt.Errorf("obj must be a struct, but kind of:%v", structValue.Kind())
	}

	structType := reflect.TypeOf(obj).Elem()

	// 遍历所有成员
	for i := 0; i < structValue.NumField(); i++ {
		// TODO 摘成递归方法
		structFieldValue := structValue.Field(i)
		structFieldType := structType.Field(i)

		// 检查是否可以设置
		if !structFieldValue.IsValid() || !structFieldValue.CanSet() {
			return fmt.Errorf("no such field: %s in obj", structFieldValue.String())
		}

		tagName := structFieldType.Tag.Get("json")

		mapValue, ok := hash[tagName]
		if !ok {
			continue
		}

		// 尝试转换目标类型
		switch structFieldValue.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			structFieldValue.SetInt(ToInt64(mapValue))
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			structFieldValue.SetUint(uint64(ToInt64(mapValue)))
		case reflect.String:
			structFieldValue.SetString(fmt.Sprintf("%v", mapValue))
		case reflect.Bool:
			structFieldValue.SetBool(ToBool(mapValue))
		case reflect.Struct:
			typ := structFieldValue.Type()
			if typ.Name() == "Time" && typ.PkgPath() == "time" {
				t, err := time.Parse(time.RFC3339, fmt.Sprintf("%v", mapValue))
				if err != nil {
					return err
				}
				structFieldValue.Set(reflect.ValueOf(t))
			}

		}
	}

	return nil
}

// GetStrLen 字符串长度:标准:中文>=2个字节取2个字段,英文取1个字节
func GetStrLen(s string) int {
	length := 0
	for i := 0; i < len(s); {
		r, size := utf8.DecodeRuneInString(s[i:])
		if r == utf8.RuneError {
			length++
			i += size
		} else if utf8.RuneLen(r) >= 2 {
			length += 2
			i += size
		} else {
			length++
			i += size
		}
	}
	return length
}
