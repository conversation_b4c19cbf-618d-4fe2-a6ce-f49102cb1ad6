package transform

import (
	"strings"
)

// SliceContain 判断 slice 中是否包含某个元素
// 不对 slice 作任何假设
func SliceContain(n int, compare func(i int) bool) bool {
	for k := 0; k < n; k++ {
		if compare(k) {
			return true
		}
	}
	return false
}

// IntSliceContain 判断 int slice 中是否包含某个元素
func IntSliceContain(slice []int, val int) bool {
	return SliceContain(len(slice), func(i int) bool {
		return slice[i] == val
	})
}

// StringSliceContain 判断 int slice 中是否包含某个元素
func StringSliceContain(slice []string, val string) bool {
	return SliceContain(len(slice), func(i int) bool {
		return slice[i] == val
	})
}

// Int32SliceContain 判断 int slice 中是否包含某个元素
func Int32SliceContain(slice []int32, val int32) bool {
	return SliceContain(len(slice), func(i int) bool {
		return slice[i] == val
	})
}

// Uint64SliceContain 判断 uint64 slice 中是否包含某个元素
func Uint64SliceContain(slice []uint64, val uint64) bool {
	return SliceContain(len(slice), func(i int) bool {
		return slice[i] == val
	})
}

// Int64SliceContain 判断 uint64 slice 中是否包含某个元素
func Int64SliceContain(slice []int64, val int64) bool {
	return SliceContain(len(slice), func(i int) bool {
		return slice[i] == val
	})
}

func StrSplit2Slice(originStr, symbol string) []string {
	return strings.Split(originStr, symbol)
}

func Slice2Str(originStr []string, symbol string) string {
	return strings.Join(originStr, symbol)
}
