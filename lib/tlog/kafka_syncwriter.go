package tlog

import (
	"git.coinmoney.xyz/backend/go-sdk/utils"
	"os"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
	"golang.org/x/net/context"
)

const (
	topic          = "log"
	writeBatchSize = 2
)

type kafkaSyncWriter struct {
	Topic     string
	Address   []string //brokers
	writer    *kafka.Writer
	batchSize int
	buf       []kafka.Message
	lastTime  time.Time
	formatter LoggerFormatter
}

func NewKafkaSyncWriter(formatter LoggerFormatter) LoggerWriter {
	var address []string
	url := os.Getenv(utils.KafkaUrlEnv)
	if url != "" {
		address = strings.Split(url, ",")
	}
	return &kafkaSyncWriter{
		Address:   address,
		Topic:     topic,
		batchSize: writeBatchSize,
		formatter: formatter,
	}
}

func (w *kafkaSyncWriter) Write(entry *logEntry) (err error) {
	if err := w.ensure(entry); err != nil {
		return err
	}
	var buf []byte
	if err := w.formatter.Format(entry, &buf); err != nil {
		return err
	}
	w.buf = append(w.buf, kafka.Message{Value: buf})
	now := time.Now()
	if now.UnixMilli()-w.lastTime.UnixMilli() > 1000 {
		err = w.writer.WriteMessages(context.Background(),
			w.buf...,
		)
		w.buf = w.buf[:0]
		w.lastTime = now
		return err
	}
	if len(w.buf) >= w.batchSize {
		err = w.writer.WriteMessages(context.Background(),
			w.buf...,
		)
		w.buf = w.buf[:0]
		w.lastTime = now
		return err
	}
	return nil
}

func (w *kafkaSyncWriter) ensure(_ *logEntry) (err error) {
	if w.writer == nil {
		w.buf = make([]kafka.Message, 0, w.batchSize)
		w.writer = &kafka.Writer{
			Addr:      kafka.TCP(w.Address...),
			Topic:     w.Topic,
			BatchSize: 1,
			Async:     true,
		}
	}

	return
}

func (w *kafkaSyncWriter) Sync() error {
	return nil
}

func (w *kafkaSyncWriter) Close() error {
	return w.writer.Close()
}
