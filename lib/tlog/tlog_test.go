package tlog

import (
	"testing"
)

//goos: windows
//goarch: amd64
//pkg: qysdk/tlog
//cpu: AMD Ryzen 5 5600G with Radeon Graphics
//BenchmarkKafkaLogInfo
//BenchmarkKafkaLogInfo-12    	  280258	      4634 ns/op
//PASS
func BenchmarkKafkaLogInfo(b *testing.B) {
	GetLogger().Start(
		ErrorPath("D:\\mycache\\devlog"),
		Level(LevelTrace), // 日志级别，读取配置文件，设置log.SetLevel(LevelTrace)
		Module("tLog"))    // 模块名

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			// 添加Fields
			f := NewFields()
			f.Add("message", "api").Add("database_get", "1.349958ms")
			f.Add("old_amount", "78406.86040000(4.422µs)")
			f.Add("old_gift_amount", "0.00000000(1.771µs)")
			f.Add("path", "/balance")
			f.Add("ua", "Go-http-client/2.0e")
			f.Add("retry_umber", "0(17.447µs)")

			// 打印到Kafka，每个调用都会上传一次
			log := GetLogger()
			log.Trace(f.GetDetail())
		}
	})
}
