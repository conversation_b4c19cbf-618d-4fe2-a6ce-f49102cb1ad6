package tlog

import (
	"fmt"
	"github.com/stretchr/testify/require"
	"strconv"
	"sync"
	"testing"
	"time"
)

func TestLog2Kafka(t *testing.T) {
	// 开启日志收集
	GetLogger().Start(
		ErrorPath("D:\\mycache\\devlog"),
		Level(LevelTrace), // 日志级别，读取配置文件，设置log.SetLevel(LevelTrace)
		//PrintConsole(false),  // 默认输出到控制台
		Module("tLog")) // 模块名

	// 添加Fields
	f := NewFields()
	f.Add("message", "api")
	f.Add("database_get", "1.349958ms")
	f.Add("old_amount", "78406.86040000(4.422µs)")
	f.Add("old_gift_amount", "0.00000000(1.771µs)")
	f.Add("path", "/balance")
	f.Add("ua", "Go-http-client/2.0e")
	f.Add("retry_umber", "0(17.447µs)")

	// 打印到Kafka，每个调用都会上传一次
	log := GetLogger()
	log.Trace(f.GetDetail())
	log.Debug(f.GetDetail())
	log.Warn(f.GetDetail())
	log.Error(f.GetDetail())
	log.Fatal(f.GetDetail())

	f2 := NewFields()
	f2.Add("message", "mysql")
	f2.Add("database_get", "1.349958ms")
	log.Debug(f2.GetDetail())

	time.Sleep(2 * time.Second)
	//logger.Close()
}

var wg = sync.WaitGroup{}

func TestLogParallel(t *testing.T) {

	GetLogger().Start(
		ErrorPath("D:\\mycache\\devlog"),
		Level(LevelInfo),
		Module("tLog"))

	beginTime := time.Now()
	maxTimes := 10000 * 100
	times := 0
	wg.Add(1)
	go func() {

		for {
			if times > maxTimes {
				return
			}

			log := GetLogger()

			f := NewFields()
			f.Add("message", "index_"+strconv.Itoa(times))
			log.Info(f.GetDetail())
			if times%10000 == 0 {
				fmt.Println("该函数执行完成耗时：", times, time.Since(beginTime))
			}
			times++
		}
	}()

	t.Logf("over: times - %d, rt - %d", times, time.Since(beginTime))

	wg.Wait()

	time.Sleep(time.Second * 2)
}

func TestLogFailLog(t *testing.T) {
	// 开启日志收集
	GetLogger().Start(
		Writer(NewKafkaSyncWriter(NewJsonFormatter())),
		ErrorPath("D:\\mycache\\devlog"),
		Level(LevelTrace), // 日志级别，读取配置文件，设置log.SetLevel(LevelTrace)
		Module("tLog"))    // 模块名

	log := GetLogger()

	f2 := NewFields()
	f2.Add("message", "mysql")
	f2.Add("database_get", "1.349958ms")
	log.Debug(f2.GetDetail())

	err := GetLogger().Close()
	require.Nil(t, err)
}
