package retryx

import (
	"fmt"
	"time"
)

/*
重试：
本包目的是统一规范及限制使用重试策略
微服务比较重要的环节，重试可以提高请求成功率，减少故障影响，但是需要注意拖垮下游服务
退避策略：
* BackOffDelay 指数退避策略，连续重试时，每次等待时间是前一次的2倍
* FixedDelay 每次重试，等待一个固定延迟
* RandomDelay 0 - config.maxJitter 内随机等待一个时间后重试
* CombineDelay 结合多种策略实现一个新策略的能力
retry-go默认的退避策略为 BackOffDelay和RandomDelay结合的方式，即在指数递增的同时，加一个随机时间。

适用场景：
1. http请求，例如API调用
2. 第三方请求查询，例如查询AppStore的支付，或者GooglePay
3. 内部订单发货查询
4. 关键RPC节点。但是需要注意的是，gRPC已经实现了指数退避策略
*/

// RetrievableError 可挽救错误
type RetrievableError struct {
	Err        error
	RetryAfter time.Duration
}

func (e *RetrievableError) Error() string {
	return fmt.Sprintf("%s (retry after %v)", e.Err.Error(), e.RetryAfter)
}
