// Package logzap desc  : zap记录rpc日志
package logzap

import (
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"io"
	"os"
	"time"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// getWriter 获取日志输出
func getWriter(filename string) io.Writer {
	// 保存7天内的日志，每1小时(整点)分割一次日志
	hook, err := rotatelogs.New(
		filename+".%Y%m%d%H",
		rotatelogs.WithLinkName(filename),
		rotatelogs.WithMaxAge(time.Hour*24*7),
		rotatelogs.WithRotationTime(time.Hour),
	)

	if err != nil {
		panic(err)
	}
	return hook
}

// exists 查看目录是否存在
func exists(path string) bool {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsExist(err) {
			return true
		}
		return false
	}
	return true
}

// initEncoder 初始化Encoder
func initEncoder() zapcore.Encoder {
	return zapcore.NewJSONEncoder(zapcore.EncoderConfig{
		MessageKey: "msg",
		LevelKey:   "level",
		TimeKey:    "time",
		CallerKey:  "caller",
		// 基本zapcore.LowercaseLevelEncoder。将日志级别字符串转化为小写
		EncodeLevel: zapcore.CapitalLevelEncoder,
		EncodeTime: func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
			enc.AppendString(t.Format(time.DateTime))
		},

		// 一般zapcore.ShortCallerEncoder，以包/文件:行号 格式化调用堆栈
		// EncodeCaller: zapcore.ShortCallerEncoder,

		// 一般zapcore.SecondsDurationEncoder,执行消耗的时间转化成浮点型的秒
		// EncodeDuration: func(d time.Duration, enc zapcore.PrimitiveArrayEncoder) {
		//	enc.AppendInt64(int64(d) / 1000000)
		// },
	})
}

// InitLogger 初始化日志
func InitLogger(file string) (Logger *zap.Logger) {
	rpcLogDir := viper.GetString(dict.ConfigLogDir) + dict.SysDefaultDirAccessLog
	logrus.Infof("grpc server log path ：%s", rpcLogDir)

	if !exists(rpcLogDir) {
		if err := os.MkdirAll(rpcLogDir, 0744); err != nil {
			logrus.Warnf("InitLogger mkdir logPath err=%v", err)
		}
	}
	encoder := initEncoder()

	logWriter := getWriter(rpcLogDir + file)

	// 创建Logger
	core := zapcore.NewTee(
		zapcore.NewCore(encoder, zapcore.AddSync(logWriter), zap.InfoLevel),
	)
	Logger = zap.New(core)
	// fmt.Printf("%v", Logger)
	// Logger = logger.Sugar()
	return Logger
}
