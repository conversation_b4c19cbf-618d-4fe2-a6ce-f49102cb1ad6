package random

import (
	"crypto/md5"
	"encoding/hex"
	"math/big"
	"math/rand"
	"time"
)

const keyLength = 8

// GenerateCDKey 生成CD密钥
func GenerateCDKey() string {
	rand.Seed(time.Now().UnixNano())

	// 生成随机字符串作为CD密钥
	cdKey := make([]byte, keyLength)
	for i := 0; i < keyLength; i++ {
		cdKey[i] = byte(rand.Intn(26) + 65) // 生成A到Z的字母
	}

	// 计算CD密钥的MD5哈希值
	hash := md5.Sum(cdKey)
	cdKeyHash := hex.EncodeToString(hash[:])

	return cdKeyHash
}

// GenerateCDKeyMix 生成字母数字CD密钥
func GenerateCDKeyMix(length int) string {
	const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"

	charSet := big.NewInt(int64(len(chars)))
	randomBytes := make([]byte, length)
	_, err := rand.Read(randomBytes)
	if err != nil {
		panic(err)
	}

	// 将随机字节映射为字符集中的字符
	cdKey := make([]byte, length)
	for i := 0; i < length; i++ {
		index := big.NewInt(int64(randomBytes[i]))
		index.Mod(index, charSet)
		cdKey[i] = chars[index.Int64()]
	}

	return string(cdKey)
}

func ValidateCDKey(cdKey, userInput string) bool {
	// 计算用户输入的CD密钥的MD5哈希值
	hash := md5.Sum([]byte(userInput))
	userInputHash := hex.EncodeToString(hash[:])

	// 比较hash值是否一致
	return cdKey == userInputHash
}
