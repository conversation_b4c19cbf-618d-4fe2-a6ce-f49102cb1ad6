package random

import (
	"testing"
	"time"
)

// TestSingleKeyGeneration 测试单个CDKEY的生成
func TestSingleKeyGeneration(t *testing.T) {
	generator := NewCDKeyGenerator(12)
	key := generator.Generate()

	// 检查生成的key长度是否正确
	if len(key) != 12 {
		t.<PERSON><PERSON><PERSON>("期望生成长度为12的CDKEY，实际长度为: %d", len(key))
	}

	// 检查字符集是否正确
	for i, c := range key {
		valid := false
		for _, v := range generator.charset {
			if c == v {
				valid = true
				break
			}
		}
		if !valid {
			t.<PERSON><PERSON><PERSON>("在位置 %d 的字符 '%c' 不在预定义字符集中", i, c)
		}
	}

	t.Logf("生成的CDKEY: %s", key)
}

// TestLargeBatchGeneration 测试大批量CDKEY生成
func TestLargeBatchGeneration(t *testing.T) {

	generator := NewCDKeyGenerator(12)
	largeCount := 1000000

	t.Logf("开始生成 %d 个CDKEY...", largeCount)
	start := time.Now()

	largeKeys, err := generator.GenerateBatch(largeCount)
	if err != nil {
		t.Fatalf("大批量生成失败: %v", err)
	}

	duration := time.Since(start)
	t.Logf("生成完成，耗时: %v (平均每个 %.2f 微秒)",
		duration, float64(duration.Microseconds())/float64(largeCount))

	// 验证唯一性
	uniqueMap := make(map[string]bool, largeCount)
	for _, key := range largeKeys {
		// t.Logf("生成的CDKEY: %s", key)
		uniqueMap[key] = true
	}

	uniqueCount := len(uniqueMap)
	if uniqueCount != largeCount {
		t.Errorf("在 %d 个生成的CDKEY中，只有 %d 个是唯一的，发现 %d 个重复",
			largeCount, uniqueCount, largeCount-uniqueCount)
	} else {
		t.Logf("成功验证所有 %d 个CDKEY均唯一", largeCount)
	}
}

// 用于测试的正确编码函数，与生成器中使用的逻辑相同
func correctEncodeTimestamp(t time.Time, charset string, referenceTime time.Time) string {
	const timePartLen = 6
	base := int64(len(charset))
	result := make([]byte, timePartLen)

	// 计算相对于参考时间点的秒数
	relativeSeconds := t.Unix() - referenceTime.Unix()
	if relativeSeconds < 0 {
		relativeSeconds = 0
	}

	// 编码时间戳（从右到左）
	for i := timePartLen - 1; i >= 0; i-- {
		idx := relativeSeconds % base
		result[i] = charset[idx]
		relativeSeconds /= base
	}

	return string(result)
}

func TestVerifyEncoding(t *testing.T) {
	charset := "23456789ABCDEFGHJKLMNPQRSTUVWXYZ" // 32个字符
	referenceTime := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	testTime := time.Date(2023, 11, 15, 0, 0, 0, 0, time.UTC)

	// 计算相对秒数
	relativeSeconds := testTime.Unix() - referenceTime.Unix()

	encoded := correctEncodeTimestamp(testTime, charset, referenceTime)
	t.Logf("相对时间戳 %d 秒 (约%.2f年) 编码为: %s\n",
		relativeSeconds, float64(relativeSeconds)/(60*60*24*365.25), encoded)

	// 手动验证
	t.Logf("手动编码验证:")
	t.Logf("原始相对秒数: %d\n", relativeSeconds)

	tempSeconds := relativeSeconds
	for i := 0; i < 6; i++ {
		remainder := tempSeconds % 32
		tempSeconds = tempSeconds / 32
		t.Logf("步骤%d: %d ÷ 32 = %d 余 %d → 字符 '%c'\n",
			i+1, (tempSeconds*32 + remainder), tempSeconds, remainder, charset[remainder])
	}

	// 验证最大可表示的年数
	maxSeconds := int64(32*32*32*32*32*32 - 1) // 6位base32能表示的最大秒数
	maxYears := float64(maxSeconds) / (60 * 60 * 24 * 365.25)
	t.Logf("6位base32最多可表示 %d 秒，约等于 %.2f 年", maxSeconds, maxYears)

	// 验证参考时间点的有效期
	validUntil := referenceTime.Add(time.Duration(maxSeconds) * time.Second)
	t.Logf("以2020年1月1日为参考点，编码有效期至: %s", validUntil.Format("2006-01-02"))
}
