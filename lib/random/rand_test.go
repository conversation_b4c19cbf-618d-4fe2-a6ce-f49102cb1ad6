package random

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetString(t *testing.T) {
	for i := 0; i < 10; i++ {
		t.Log(GetString(8))
	}
}

func TestGetBytes(t *testing.T) {
	for i := 0; i < 10; i++ {
		bts := GetBytes(20)
		t.Log(bts)
		if len(bts) != 20 {
			panic("not equal")
		}
	}
	bts := GetBytes(-1)
	assert.Nil(t, bts)

	bts = GetBytes(2048)
	assert.Nil(t, bts)

	bts = GetBytes(1024)
	assert.Equal(t, 1024, len(bts))

}

func TestInt32Part(t *testing.T) {
	type args struct {
		min int32
		max int32
	}
	tests := []struct {
		name  string
		args  args
		want1 int32
		want2 int32
	}{
		{name: "1", args: args{1, 10}, want1: 1, want2: 10},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Int32Part(tt.args.min, tt.args.max); got < tt.want1 || got > tt.want2 {
				t.<PERSON>("Int32Part() = %v, want section %d - %d but %d", got, tt.want1, tt.want2, got)
			}
		})
	}
}

func TestInt32n(t *testing.T) {
	type args struct {
		n int32
	}
	tests := []struct {
		name  string
		args  args
		want1 int32
		want2 int32
	}{
		{name: "2", args: args{1}, want1: 0, want2: 1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Int32n(tt.args.n); got > tt.want1 && got <= tt.want2 {
				t.Errorf("Int32n()")
			}
		})
	}
}

func TestInt64Part(t *testing.T) {
	type args struct {
		min int64
		max int64
	}
	tests := []struct {
		name  string
		args  args
		want1 int64
		want2 int64
	}{
		{name: "1", args: args{min: 1, max: 5}, want1: 1, want2: 5},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Int64Part(tt.args.min, tt.args.max); got <= tt.want1 || got >= tt.want2 {
				t.Errorf("Int64Part() , not want %v", got)
			}
		})
	}
}

func TestInt64n(t *testing.T) {
	type args struct {
		n int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{name: "BasicCase", args: args{1}, want: Int64n(1)},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Int64n(tt.args.n); got != tt.want {
				t.Errorf("Int64n() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIntN(t *testing.T) {
	type args struct {
		n int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{name: "BasicCase", args: args{1}, want: IntN(1)},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IntN(tt.args.n); got != tt.want {
				t.Errorf("IntN() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestShuffleSliceInt(t *testing.T) {
	type args struct {
		n    int
		swap func(i, j int)
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "BacicName", args: args{1, func(i, j int) {

		}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func TestUint32Part(t *testing.T) {
	type args struct {
		min uint32
		max uint32
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "BasicCase", args: args{min: 1, max: 10}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Uint32Part(tt.args.min, tt.args.max); got == 0 {
				t.Errorf("Uint32Part() = %v", got)
			}
		})
	}
}

func TestUint32Rate(t *testing.T) {
	type args struct {
		rate uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "BasicCase", args: args{1}, want: Uint32Rate(1)},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Uint32Rate(tt.args.rate); got != tt.want {
				t.Errorf("Uint32Rate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUint32Weight(t *testing.T) {
	type args struct {
		list []uint32
	}
	var aList []uint32
	aList = append(aList, 1)
	tests := []struct {
		name string
		args args
		want int
	}{
		{name: "BasicCase", args: args{list: aList}, want: Uint32Weight(aList)},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Uint32Weight(tt.args.list); got != tt.want {
				t.Errorf("Uint32Weight() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUint32n(t *testing.T) {
	type args struct {
		n uint32
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{name: "basic", args: args{1}, want: Uint32n(1)},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Uint32n(tt.args.n); got != tt.want {
				t.Errorf("Uint32n() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestShuffle(t *testing.T) {
	type args struct {
		n    int
		swap func(i, j int)
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "basic", args: args{1, func(i, j int) {

		}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func TestValidateCDKey(t *testing.T) {
	cdKey := GenerateCDKey()
	t.Logf("gen cdkey: %s", cdKey)

	// 假设输入的CDKey 密钥
	// userInput := "ABC12345"
	userInput := cdKey

	// 验证
	valid := ValidateCDKey(cdKey, userInput)
	if valid {
		t.Log("验证通过")
	} else {
		t.Log("验证失败")
	}
}

func TestGenerateTransactionID(t *testing.T) {
	t.Log(GenerateTransactionID(1001))
}
