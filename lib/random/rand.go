package random

import (
	"math/rand"
)

func init() {
	NewInitSource()
}

// Shuffle Shuffle Rand
func Shuffle(n int, swap func(i, j int)) {
	rand.Shuffle(n, swap)
}

// IntN [0,n)
func IntN(n int) int {
	return rand.Intn(n)
}

// Uint32n [0,n)
func Uint32n(n uint32) uint32 {
	return uint32(IntN(int(n)))
}

// Int32n [0,n)
func Int32n(n int32) int32 {
	return int32(IntN(int(n)))
}

// Int64n [0,n)
func Int64n(n int64) int64 {
	return rand.Int63n(n)
}

// Float32N [0,n)
func Float32N(n float32) float32 {
	return rand.Float32() * n
}

// Float64N [0,n)
func Float64N(n float64) float64 {
	return rand.Float64() * n
}

// Uint32Part 取一个范围的随机数
// [2, 5]
func Uint32Part(min, max uint32) uint32 {
	// [2, 5]
	n := max - min + 1
	return uint32(rand.Int31n(int32(n))) + min
}

func Int64Part(min, max int64) int64 {
	if min == max {
		return min
	}
	// [2, 5]
	n := max - min + 1
	return rand.Int63n(n) + min
}

func Int32Part(min, max int32) int32 {
	// [2, 5]
	n := max - min + 1
	return rand.Int31n(n) + min
}

// 加权随机
// func MapWeight(m map[string]uint32) string {
//	//去掉0
//	for k, v := range m {
//		if v <= 0 {
//			delete(m, k)
//		}
//	}
//
//	//
//	itemList := make([]string, len(m))
//	weightList := make([]uint32, len(m))
//
//	i := 0
//	for k, v := range m {
//		itemList = append(itemList, k)
//		weightList = append(weightList, v)
//		i++
//	}
//
//	pos := Uint32Weight(weightList)
//	return itemList[pos]
// }

func Uint32Weight(list []uint32) int {
	var totalWeight uint32
	for _, weight := range list {
		totalWeight = totalWeight + weight
	}

	rate := uint32(rand.Int31n(int32(totalWeight))) // [0,sum)  panic if sum=0
	var total uint32
	var i int
	for k, weight := range list {
		total = total + weight
		if rate < total {
			i = k
			break
		}
	}

	return i
}

// Uint32Rate 百分比 随机
func Uint32Rate(rate uint32) bool {

	rrate := uint32(rand.Int31n(int32(100))) // [0,100)  panic if sum=0 0-99
	rrate++                                  // 1-100
	return rrate <= rate
}

// ShuffleSliceInt 混淆打乱一个slice
// Shuffle pseudo-randomizes the order of elements.
// n is the number of elements. Shuffle panics if n < 0.
// swap the elements with indexes i and j.
func ShuffleSliceInt(n int, swap func(i, j int)) {
	if n < 0 {
		panic("invalid argument to Shuffle")
	}

	// Fisher-Yates shuffle: https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle
	// Shuffle really ought not be called with n that doesn't fit in 32 bits.
	// Not only will it take a very long time, but with 2³¹! possible permutations,
	// there's no way that any PRNG can have a big enough internal state to
	// generate even a minuscule percentage of the possible permutations.
	// Nevertheless, the right API signature accepts an int n, so handle it as best we can.
	i := n - 1
	for ; i > 1<<31-1-1; i-- {
		j := int(rand.Int63n(int64(i + 1)))
		swap(i, j)
	}
	for ; i > 0; i-- {
		j := int(rand.Int31n(int32(i + 1)))
		swap(i, j)
	}
}

// RandForInt64Weight 根据权重随机选择一个键
func RandForInt64Weight(weights map[int64]int32) int64 { 
	if len(weights) == 0 {
		return 0 // 没有元素时返回0
	}

	var total int32
	for _, w := range weights {
		total += w
	}

	if total == 0 {
		return 0 // 所有权重都为0时返回0
	}

	r := rand.Int31n(total)
	for k, v := range weights {
		if r < v {
			return k
		}
		r -= v
	}

	// 理论上不会到达这里，因为总权重一定大于0且覆盖了所有可能的随机数
	return 0 // 添加一个兜底返回，尽管在逻辑上不会执行
}

// RandForInt32Weight 根据权重随机选择一个键
func RandForInt32Weight(weights map[int32]int32) int32 {
	if len(weights) == 0 {
		return 0 // 没有元素时返回0
	}

	var total int32
	for _, w := range weights {
		total += w
	}

	if total == 0 {
		return 0 // 所有权重都为0时返回0
	}

	r := rand.Int31n(total)
	for k, v := range weights {
		if r < v {
			return k
		}
		r -= v
	}

	// 理论上不会到达这里，因为总权重一定大于0且覆盖了所有可能的随机数
	return 0 // 添加一个兜底返回，尽管在逻辑上不会执行
}
