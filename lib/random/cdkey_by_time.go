package random

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"time"
)

// CDKeyGenerator 用于生成基于时间的CDKEY
type CDKeyGenerator struct {
	charset string
	length  int
}

// NewCDKeyGenerator 创建一个新的CDKEY生成器
func NewCDKeyGenerator(length int) *CDKeyGenerator {
	if length < 8 {
		length = 8 // 最小长度为8，确保足够的随机性和唯一性
	}

	return &CDKeyGenerator{
		charset: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",
		length:  length,
	}
}

// Generate 生成单个CDKEY
func (g *CDKeyGenerator) Generate() string {
	timePrefix := g.encodeTimestamp(time.Now())
	randomPart := g.generateRandomPart(g.length - len(timePrefix))

	// 组合时间部分和随机部分
	return timePrefix + randomPart
}

// GenerateBatch 批量生成不重复的CDKEY， 12位长度，一秒生成100W左右
func (g *CDKeyGenerator) GenerateBatch(count int) ([]string, error) {
	if count <= 0 {
		return nil, fmt.Errorf("生成数量必须大于0")
	}

	keys := make([]string, count)
	uniqueCheck := make(map[string]struct{}, count)

	// 获取当前时间
	now := time.Now()

	timePrefix := g.encodeTimestamp(now)
	randomPartLen := g.length - len(timePrefix)

	for i := 0; i < count; i++ {
		// 生成key并确保在本批次内不重复
		var key string
		for {
			key = timePrefix + g.generateRandomPart(randomPartLen)
			if _, exists := uniqueCheck[key]; !exists {
				break
			}
			// 极少数情况下可能出现碰撞，继续尝试生成新的key
			timePrefix = g.encodeTimestamp(time.Now())
		}

		keys[i] = key
		uniqueCheck[key] = struct{}{}
	}

	return keys, nil
}

// encodeTimestamp 将时间戳编码为base62字符串
func (g *CDKeyGenerator) encodeTimestamp(t time.Time) string {
	base := int64(len(g.charset))
	num := t.Unix()
	var result []byte

	if num == 0 {
		return string(g.charset[0])
	}

	for num > 0 {
		result = append(result, g.charset[num%base])
		num /= base
	}

	return string(result)
}

// generateRandomPart 生成指定长度的随机字符串
func (g *CDKeyGenerator) generateRandomPart(length int) string {
	if length <= 0 {
		return ""
	}

	result := make([]byte, length)
	max := big.NewInt(int64(len(g.charset)))

	for i := 0; i < length; i++ {
		n, err := rand.Int(rand.Reader, max)
		if err != nil {
			// 极少情况下会出错，这里简单处理
			result[i] = g.charset[0]
			continue
		}
		result[i] = g.charset[n.Int64()]
	}

	return string(result)
}
