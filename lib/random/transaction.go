package random

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"time"
)

// GenerateTransactionID 交易ID生成
func GenerateTransactionID(uid uint64) string {
	timestamp := time.Now().UnixMilli()

	randomBytes := make([]byte, 3) // 减少到3字节以避免大部分情况下出现填充字符

	_, _ = rand.Read(randomBytes) // 忽略潜在的错误，实践中应适当处理

	// 将用户ID、时间戳和随机字节组合并进行Base64编码，以获得可读性较强的字符串
	combined := fmt.Sprintf("%d-%d", timestamp, uid) + base64.URLEncoding.EncodeToString(randomBytes)
	return combined
}
