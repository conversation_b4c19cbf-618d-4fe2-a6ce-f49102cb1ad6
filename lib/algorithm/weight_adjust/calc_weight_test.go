package weight_adjust

import (
	"testing"
)

func TestCalculateAdjustedWeights(t *testing.T) {
	fishTypes := []FishType{
		{Name: "A", Weight: 600, Value: 50, Ratio: 0.05},
		{Name: "B", Weight: 600, Value: 30, Ratio: 0.15},
		{Name: "C", Weight: 800, Value: 20, Ratio: 0.30},
	}

	adjustedWeights := CalculateAdjustedWeights(fishTypes)

	t.Log("调整后的权重：")
	for name, weight := range adjustedWeights {
		t.Logf("%s: %.2f\n", name, weight)
	}
}
