package weight_adjust

// FishType 表示鱼种的信息
type FishType struct {
	Name   string  // 鱼种名称
	Weight float64 // 初始权重
	Value  float64 // 单价价值
	Ratio  float64 // 期望价值比例
}

// CalculateAdjustedWeights 计算调整后的权重
func CalculateAdjustedWeights(fishTypes []FishType) map[string]float64 {
	// 计算总价值
	var totalValue float64
	for _, fish := range fishTypes {
		totalValue += fish.Value
	}

	// 计算权重
	var totalWeight float64
	for _, fish := range fishTypes {
		totalWeight += fish.Weight
	}

	adjustedWeights := make(map[string]float64)

	for _, fish := range fishTypes {
		expectedValue := totalValue * fish.Ratio
		adjustedWeight := expectedValue * totalWeight / fish.Value
		adjustedWeights[fish.Name] = adjustedWeight
	}

	return adjustedWeights
}
