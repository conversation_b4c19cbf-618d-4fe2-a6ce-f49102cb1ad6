package montecarlo

import "testing"

func TestRewardByTickets(t *testing.T) {
	lottery := Lottery{
		Tickets: []LotteryTicket{
			{"一等奖", 100, 10},
			{"二等奖", 100, 20},
			{"三等奖", 100, 30},
		},
	}

	simulations := 1000000
	winningProbabilities := lottery.estimateWinningProbabilities(simulations)

	for _, ticket := range lottery.Tickets {
		t.Logf("%s 的中奖概率约为: %.2f%%\n", ticket.Name, winningProbabilities[ticket.Name]*100)
	}
}
