package montecarlo

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// 蒙特卡洛模拟抽奖 券票

// LotteryTicket 表示抽奖活动的奖项
type LotteryTicket struct {
	Name           string // 奖券名
	TotalTickets   int    // 总奖券数量
	WinningTickets int    // 获奖奖券数量
}

// Lottery 模拟抽奖活动
type Lottery struct {
	Tickets []LotteryTicket
}

// simulateLotteryDraw 模拟单次抽奖
func simulateLotteryDraw(ticket LotteryTicket) bool {
	random.NewInitSource()
	drawnTicket := random.IntN(ticket.TotalTickets) + 1
	return drawnTicket <= ticket.WinningTickets
}

// estimateWinningProbabilities 使用蒙特卡洛模拟估计每个奖项的中奖概率
func (l *Lottery) estimateWinningProbabilities(sims int) map[string]float64 {
	winningCounts := make(map[string]int)
	for i := 0; i < sims; i++ {
		for _, ticket := range l.Tickets {
			if simulateLotteryDraw(ticket) {
				winningCounts[ticket.Name]++
			}
		}
	}

	probabilities := make(map[string]float64)
	for name, count := range winningCounts {
		probabilities[name] = float64(count) / float64(sims)
	}
	return probabilities
}
