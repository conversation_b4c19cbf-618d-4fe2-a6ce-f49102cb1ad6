package montecarlo

import "testing"

func TestRewardWeighted(t *testing.T) {
	lottery := LotteryWeighted{
		Tickets: []LotteryTicketWeighted{
			{"一等奖", 20},
			{"二等奖", 40},
			{"三等奖", 40},
		},
	}

	simulations := 1000000
	winningProbabilities := lottery.estimateWinningProbabilitiesWeighted(simulations)

	for _, ticket := range lottery.Tickets {
		t.Logf("%s 的中奖概率约为: %.2f%%\n", ticket.Name, winningProbabilities[ticket.Name]*100)
	}
}
