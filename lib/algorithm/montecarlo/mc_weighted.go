package montecarlo

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// 蒙特卡洛模拟抽奖 权重

// LotteryTicketWeighted 表示抽奖活动权重的奖项
type LotteryTicketWeighted struct {
	Name   string
	Weight int
}

// LotteryWeighted 模拟抽奖活动
type LotteryWeighted struct {
	Tickets []LotteryTicketWeighted
}

// simulateLotteryDraw 模拟单次抽奖，根据权重决定中奖
func simulateLotteryDrawWeighted(tickets []LotteryTicketWeighted) (string, bool) {
	random.NewInitSource()
	totalWeight := 0
	for _, ticket := range tickets {
		totalWeight += ticket.Weight
	}

	drawnWeight := random.IntN(totalWeight) + 1
	cumulativeWeight := 0
	for _, ticket := range tickets {
		cumulativeWeight += ticket.Weight
		if cumulativeWeight >= drawnWeight {
			return ticket.Name, true
		}
	}
	return "", false // 不应该到达这里，除非计算错误
}

// estimateWinningProbabilities 使用蒙特卡洛模拟估计每个奖项的中奖概率
func (l *LotteryWeighted) estimateWinningProbabilitiesWeighted(sims int) map[string]float64 {
	winningCounts := make(map[string]int)
	for i := 0; i < sims; i++ {
		winnerName, _ := simulateLotteryDrawWeighted(l.Tickets)
		if winnerName != "" { // 确保有奖项被抽中
			winningCounts[winnerName]++
		}
	}

	probabilities := make(map[string]float64)
	for name, count := range winningCounts {
		probabilities[name] = float64(count) / float64(sims)
	}
	return probabilities
}
