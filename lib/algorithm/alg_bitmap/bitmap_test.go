package alg_bitmap

import (
	"fmt"
	"testing"
)

func TestNewBitmap(t *testing.T) {
	array := [...]uint64{0, 6, 3, 7, 2, 8, 1, 4}

	var maxNum uint64 = 9
	bm := NewBitmap(maxNum)

	for _, v := range array {
		bm.Set(v)
	}

	bm.Set(5)
	fmt.Println("BitMap IsFully:", bm.IsFully())
	fmt.Println("BitMap IsEmpty:", bm.IsEmpty())
	fmt.Println("bitmap 中存在的数字:")
	fmt.Println(bm.GetData())
	fmt.Println("bitmap 中的二进制串")
	fmt.Println(bm.String())
	fmt.Println("bitmap 中的数字个数:", bm.Count())
	fmt.Println("bitmap size:", bm.Size())
	fmt.Println("Test(0):", bm.Test(0))
	bm.Reset(5)
	fmt.Println(bm.String())
	fmt.Println("Test(5):", bm.Test(5))
	fmt.Println(bm.GetData())
}

func TestBitmapRedis(t *testing.T) {
	var maxNum uint64 = 60
	bm := NewBitmap(maxNum)
	t.Logf("bm bits ahead: %v", bm.String())
	bm.Set(1)
	bm.Set(3)
	bm.Set(5)
	bm.Set(6)
	t.Logf("bm bits ahead 2: %s", bm.String())
	t.Logf("bm bits ahead Count: %d", bm.GetData())
}

func TestHugeDataRank(t *testing.T) {
	testMax := 100000000
	bm := NewBitmap(uint64(testMax))
	for i := 0; i < testMax; i++ {
		bm.Set(uint64(i))
	}

	fmt.Println(bm.Size())
	fmt.Println(bm.GetData()[bm.Size()-1])
}
