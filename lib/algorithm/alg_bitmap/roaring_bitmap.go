package alg_bitmap

import (
	"encoding/hex"

	"github.com/RoaringBitmap/roaring/roaring64"
	"github.com/sirupsen/logrus"
)

// InBitmap 判断指定id在 bitmap 中是否存在
func InBitmap(bitStr string, bit uint64) bool {
	bs, err := hexToBitmap(bitStr)
	if err != nil {
		logrus.Warnf("check in bitset, bitStr:%s, bit:%d bitmap error:%v", bitStr, bit, err)
		return false
	}

	return bs.Contains(bit)
}

// AddBit 指定数据添加到 bitmap 返回设置后的字符串
func AddBit(bitStr string, bit uint64) (string, error) {
	bs, err := hexToBitmap(bitStr)
	if err != nil {
		logrus.Warnf("init bitmap, bitStr:%s, bit:%d error:%v", bitStr, bit, err)
		return bitStr, err
	}

	bs.Add(bit)

	return bitmapToHex(bs), nil
}

// RemoveBit 指定数据从 bitmap 中删除指定值返回设置后的字符串
func RemoveBit(bitStr string, bit uint64) (string, error) {
	bs, err := hexToBitmap(bitStr)
	if err != nil {
		logrus.Warnf("delete bitmap, bitStr:%s, bit:%d error:%v", bitStr, bit, err)
		return bitStr, err
	}

	bs.Remove(bit)

	return bitmapToHex(bs), nil
}

// GetMarkedBits 遍历 bitmap 中的所有位并返回标记位列表
func GetMarkedBits(bitStr string) ([]uint64, error) {
	bs, err := hexToBitmap(bitStr)
	if err != nil {
		logrus.Errorf("init bitmap, bitStr:%s error:%v", bitStr, err)
		return nil, err
	}

	var markedBits []uint64
	it := bs.Iterator()
	for it.HasNext() {
		markedBits = append(markedBits, it.Next())
	}

	return markedBits, nil
}

// hexToBitmap 将十六进制字符串转换为 roaring64.Bitmap
func hexToBitmap(bitStr string) (*roaring64.Bitmap, error) {
	// 空字符串直接返回空的 bitmap
	if bitStr == "" {
		return roaring64.NewBitmap(), nil
	}

	data, err := hex.DecodeString(bitStr)
	if err != nil {
		return nil, err
	}

	bs := roaring64.NewBitmap()
	if err := bs.UnmarshalBinary(data); err != nil {
		return nil, err
	}

	return bs, nil
}

// bitmapToHex 将 roaring64.Bitmap 转换为十六进制字符串
func bitmapToHex(bs *roaring64.Bitmap) string {
	data, err := bs.MarshalBinary()
	if err != nil {
		logrus.Errorf("marshal bitmap to binary error: %v", err)
		return ""
	}

	return hex.EncodeToString(data)
}