package alg_bitmap

import (
	"fmt"
	"strings"
)

/*
BitMap
主要用于快速检索关键字状态，大数据排序，查找，去重
bit-map基本思想就是用一个bit位来标记某个元素对应的Value，而Key即该元素。
由于采用了Bit为单位来存储数据，因此可以大大节省存储空间
eg：在20亿随机整数中找出某个数m是否存在其中。并假设32位操作系统，4G内存
int占4个字节，1字节=8位（1byte=8bit），
如果用int存储，那就是20亿个int，因此占用空间约为(2000000000*4/1024/1024/1024)≈7.45G
如按位存储就不一样了，20亿个数就是20亿位，占用(2000000000/8/1024/1024/1024)≈0.233G
*/

const bitSize = 8

var bitmask = []byte{1, 1 << 1, 1 << 2, 1 << 3, 1 << 4, 1 << 5, 1 << 6, 1 << 7}

// 首字母小写 只能调用 工厂函数 创建
type bitmap struct {
	bits     []byte
	bitCount uint64 // 已填入数字的数量
	capacity uint64 // 容量
}

// NewBitmap 创建工厂函数
func NewBitmap(maxNum uint64) *bitmap {
	return &bitmap{bits: make([]byte, (maxNum+7)/bitSize), bitCount: 0, capacity: maxNum}
}

// Set 填入数字
func (b *bitmap) Set(num uint64) {
	byteIndex, bitPos := b.offset(num)

	// 1 左移 bitPos 位 进行 按位或 (置为 1)
	b.bits[byteIndex] |= bitmask[bitPos]
	b.bitCount++
}

// Reset 清除填入的数字
func (b *bitmap) Reset(num uint64) {
	byteIndex, bitPos := b.offset(num)
	// 重置为空位 (重置为 0)
	b.bits[byteIndex] &= ^bitmask[bitPos]
	b.bitCount--
}

// Test 数字是否在位图中
func (b *bitmap) Test(num uint64) bool {
	byteIndex := num / bitSize
	if byteIndex >= uint64(len(b.bits)) {
		return false
	}

	bitPos := num % bitSize

	// 右移 bitPos 位 和 1 进行 按位与
	return !(b.bits[byteIndex]&bitmask[bitPos] == 0)
}

func (b *bitmap) offset(num uint64) (byteIndex uint64, bitPos byte) {
	byteIndex = num / bitSize // 字节索引
	if byteIndex >= uint64(len(b.bits)) {
		panic(fmt.Sprintf(" runtime error: index value %d out of range", byteIndex))
		return
	}

	bitPos = byte(num % bitSize) // bit位置

	return byteIndex, bitPos
}

// Size 位图的容量
func (b *bitmap) Size() uint64 {
	return uint64(len(b.bits) * bitSize)
}

// IsEmpty 是否空位图
func (b *bitmap) IsEmpty() bool {
	return b.bitCount == 0
}

// IsFully 是否已填满
func (b *bitmap) IsFully() bool {
	return b.bitCount == b.capacity
}

// Count 已填入的数字个数
func (b *bitmap) Count() uint64 {
	return b.bitCount
}

// GetData 获取填入的数字切片
func (b *bitmap) GetData() []uint64 {
	var data []uint64
	count := b.Size()

	for index := uint64(0); index < count; index++ {
		if b.Test(index) {
			data = append(data, index)
		}
	}

	return data
}

func (b *bitmap) String() string {
	var sb strings.Builder
	for index := len(b.bits) - 1; index >= 0; index-- {
		sb.WriteString(byteToBinaryString(b.bits[index]))
		sb.WriteString(" ")
	}
	return sb.String()
}

func byteToBinaryString(data byte) string {
	var sb strings.Builder
	for index := 0; index < bitSize; index++ {
		if (bitmask[7-index] & data) == 0 {
			sb.WriteString("0")
		} else {
			sb.WriteString("1")
		}
	}

	return sb.String()
}
