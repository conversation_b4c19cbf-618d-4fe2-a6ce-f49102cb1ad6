package lottery

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"math/rand"
)

// Weighted Reservoir Sampling
// 加权蓄水池抽样算法 - 保底优化
// 可用于从N个种类按照权重抽样M个元素，有保底概率

const (
	rareProb        = 0.01 // 抽中稀有概率
	guaranteedAfter = 10   // 连抽必中次数
)

// ItemGuarantee 代表带有权重的元素,
type ItemGuarantee struct {
	Value  string
	Weight int
	IsRare bool // 是否为稀有奖励
}

// WeightedReservoirSamplingGuarantee 保底蓄水池抽样 （k 样本数）
func WeightedReservoirSamplingGuarantee(items []ItemGuarantee, k int) []ItemGuarantee {
	random.NewInitSource()

	reservoir := make([]ItemGuarantee, k)
	totalWeight := 0
	for i := range items[:k] { // 确保只初始化k个元素
		reservoir[i] = items[i]
		totalWeight += items[i].Weight
	}

	for i := k; i < len(items); i++ {
		// 计算当前元素被选中的概率
		prob := float64(items[i].Weight) / float64(totalWeight)
		r := rand.Float64()
		if r < prob {
			idx := rand.Intn(k)
			reservoir[idx] = items[i]
			// 更新总权重
			if reservoir[idx].IsRare {
				// 如果是稀有奖励，检查是否达到保底要求
				count := 0
				for _, item := range reservoir {
					if item.IsRare {
						count++
					}
				}
				if count >= guaranteedAfter {
					// 达到保底，停止抽签
					return reservoir
				}
				// 更新总权重，移除旧的稀有奖励
				totalWeight -= items[i].Weight
				// 添加新的稀有奖励
				totalWeight += reservoir[idx].Weight
			}
		}
	}

	// 如果没有达到保底，检查水池中是否有稀有奖励
	for _, item := range reservoir {
		if item.IsRare {
			return reservoir
		}
	}

	// 应该不会执行到这里，但为了完整性
	return reservoir
}
