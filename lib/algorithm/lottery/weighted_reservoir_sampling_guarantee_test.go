package lottery

import (
	"fmt"
	"testing"
)

func TestWrsGuarantee(t *testing.T) {
	items := []ItemGuarantee{
		{"Item-1", 100, false},
		{"Item-2", 200, false},
		{"Item-3", 300, false},
		{"Item-4", 400, false},
		{"Item-5", 500, true}, // 稀有奖励
		{"Item-6", 600, false},
		{"Item-7", 700, true}, // 稀有奖励
		{"Item-8", 800, false},
		{"Item-9", 900, false},
		{"Item-10", 1000, true}, // 稀有奖励
	}

	samples := WeightedReservoirSamplingGuarantee(items, 3)
	fmt.Println("连抽结果：", samples)
}
