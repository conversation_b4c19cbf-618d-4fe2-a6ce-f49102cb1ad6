package lottery

import (
	"math/rand"

	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// Weighted Reservoir Sampling
// 加权蓄水池抽样算法
// 可用于从N个种类按照权重抽样M个元素

// Item 代表带有权重的元素
type Item struct {
	Value  string
	Weight int
}

// k 样本数
func WeightedReservoirSampling(items []Item, k int) []Item {
	random.NewInitSource()

	// 初始化水池，并且计算总权重
	reservoir := make([]Item, k)
	totalWeight := 0
	for i := 0; i < k; i++ {
		reservoir[i] = items[i]
		totalWeight += items[i].Weight
	}

	// 遍历剩余的元素，计算出被选中的概率
	for _, item := range items[k:] {
		// 计算当前元素被选中的概率
		prob := float64(item.Weight) / float64(totalWeight)

		// 生成一个[0,1)范围内的随机数
		r := rand.Float64()

		// 如果随机数小于概率，则替换水池中的一个元素
		if r < prob {
			idx := rand.Intn(k) // 随机选择一个水池中的位置
			reservoir[idx] = item

			// 更新总权重
			totalWeight += item.Weight - reservoir[idx].Weight
			reservoir[idx].Weight = item.Weight
		}
	}

	return reservoir
}
