package reflectext

import (
	"github.com/sirupsen/logrus"
	"reflect"
)

// Invoke 带参字符串反射调用函数
func Invoke(value reflect.Value, name string, args ...interface{}) {
	inputs := make([]reflect.Value, len(args))
	for i, _ := range args {
		inputs[i] = reflect.ValueOf(args[i])
	}
	value.MethodByName(name).Call(inputs)
}

// IsMethodExist 反射机制判断是否存在某个函数
func IsMethodExist(value reflect.Value, name string) bool {
	f := value.MethodByName(name)
	if !f.IsValid() {
		logrus.Errorf("method reflect invalid %s", name)
		return false
	}

	return true
}

// IsBlank 判断reflect.Value是否为空
func IsBlank(value reflect.Value) bool {
	switch value.Kind() {
	case reflect.String:
		return value.Len() == 0
	case reflect.Bool:
		return !value.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return value.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return value.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return value.Float() == 0
	case reflect.Interface, reflect.Ptr:
		return value.IsNil()
	}

	return reflect.DeepEqual(value.Interface(), reflect.Zero(value.Type()).Interface())
}
