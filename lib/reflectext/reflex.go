package reflectext

import (
	"fmt"
	"reflect"
	"strings"

	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

func Deref(t reflect.Type) reflect.Type {
	if t.Kind() == reflect.Ptr {
		return t.Elem()
	} else {
		return t
	}
}

// ValuesPtr 返回结构遍历给定的字段的值
func ValuesPtr(v reflect.Value, values []interface{}) {
	for i := range values {
		values[i] = reflect.Indirect(v).Field(i).Addr().Interface()
	}
}

func MapToStruct(input map[string]interface{}, output interface{}) error {
	// 校验是否是结构类型
	valueOfDest := reflect.ValueOf(output).Elem()
	if valueOfDest.Kind() != reflect.Struct {
		return fmt.Errorf("应为%s,但得到%s", reflect.Struct, valueOfDest.Kind())
	}

	// 获取结构体实例的反射类型对象
	typeOfDest := reflect.TypeOf(output).Elem()

	// 遍历结构体所有成员
	for i := 0; i < typeOfDest.NumField(); i++ {
		// 获取每个成员的结构体字段类型
		fieldType := typeOfDest.Field(i)

		mapValue, _ := input[fieldType.Tag.Get("json")]

		switch fieldType.Type.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			valueOfDest.FieldByName(fieldType.Name).SetInt(transform.ToInt64(mapValue))

		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			valueOfDest.FieldByName(fieldType.Name).SetInt(transform.ToInt64(mapValue))

		case reflect.String:
			valueOfDest.FieldByName(fieldType.Name).SetString(mapValue.(string))

		case reflect.Bool:
			valueOfDest.FieldByName(fieldType.Name).SetBool(transform.ToBool(mapValue))
		}
	}

	return nil
}


// 
// GetJSONToStructFieldMap 函数将结构体类型映射到JSON字段的映射关系
//
// 参数:
//     structType: 需要映射的结构体类型
//
// 返回值:
//     返回一个映射，键为JSON字段名，值为结构体字段名
//	
// 示例:
//     type Person struct {
//         Name    string `json:"name"`
//         Age     int    `json:"age"`
//         Address string `json:"address,omitempty"`
//     }
//
//     jsonToStructFieldMap := GetJSONToStructFieldMap(reflect.TypeOf(Person{}))
//     fmt.Println(jsonToStructFieldMap)
//     // 输出: map[address:Address age:Age name:Name]

func GetJSONToStructFieldMap(structType reflect.Type) map[string]string {
	jsonToStructFieldMap := make(map[string]string)
	elemType := structType.Elem()

	for i := 0; i < elemType.NumField(); i++ {
		field := elemType.Field(i)
		jsonTag := field.Tag.Get("json")
		if jsonTag != "" {
			// 处理 json 标签中的逗号分隔值
			parts := strings.Split(jsonTag, ",")
			if len(parts) > 0 {
				jsonToStructFieldMap[parts[0]] = field.Name
			}
		}
	}

	return jsonToStructFieldMap
}

// GetStructValidMap 函数用于从一个结构体对象中提取出所有非零值字段，并将其键值对存入一个map中返回
// 参数：
// obj: 需要被处理的接口，该接口应指向一个结构体或结构体指针
// 返回值：
// map[string]interface{}: 包含结构体中非零值字段的键值对映射表
// error: 如果输入的obj不是结构体或结构体指针，将返回一个错误信息
func GetStructValidMap(obj interface{}) (map[string]interface{}, error) {
	structValue := reflect.ValueOf(obj)
	if structValue.Kind() == reflect.Ptr {
		structValue = structValue.Elem()
	}
	if structValue.Kind() != reflect.Struct {
		return nil, fmt.Errorf("obj must be a struct or a pointer to a struct")
	}

	structType := structValue.Type()

	// 初始化updateInfo为空map
	updateInfo := make(map[string]interface{})

	// 遍历所有成员
	for i := 0; i < structValue.NumField(); i++ {
		structFieldValue := structValue.Field(i)
		structFieldType := structType.Field(i)

		// 获取字段的json标签
		jsonTag := structFieldType.Tag.Get("json")
		if jsonTag == "" {
			continue
		}

		// 处理 json 标签中的逗号分隔值
		parts := strings.Split(jsonTag, ",")
		if len(parts) == 0 {
			continue
		}

		jsonFieldName := parts[0]

		// 检查字段是否为零值
		if !IsBlank(structFieldValue) {
			// 将字段值添加到updateInfo中
			updateInfo[jsonFieldName] = structFieldValue.Interface()
		}
	}

	return updateInfo, nil
}