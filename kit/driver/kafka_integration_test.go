package driver

import (
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"testing"
)

// TestSetupKafkaErrorLog 测试 Kafka 错误日志设置功能
func TestSetupKafkaErrorLog(t *testing.T) {
	// 保存原始配置
	originalConfig := viper.AllSettings()
	defer func() {
		viper.Reset()
		for k, v := range originalConfig {
			viper.Set(k, v)
		}
	}()

	tests := []struct {
		name           string
		config         map[string]interface{}
		expectSuccess  bool
		expectWarning  bool
		description    string
	}{
		{
			name: "valid_kafka_config",
			config: map[string]interface{}{
				"kafka-producer": map[string]interface{}{
					"brokers": []string{"localhost:9092"},
					"topic":   "test-logs",
					"timeout": 10,
				},
				"rpc_server_name": "test-service",
			},
			expectSuccess: true,
			expectWarning: false,
			description:   "有效的 Kafka 配置应该成功初始化",
		},
		{
			name: "no_kafka_config",
			config: map[string]interface{}{
				"rpc_server_name": "test-service",
			},
			expectSuccess: false,
			expectWarning: false,
			description:   "没有 Kafka 配置时应该跳过初始化",
		},
		{
			name: "empty_brokers",
			config: map[string]interface{}{
				"kafka-producer": map[string]interface{}{
					"brokers": []string{},
					"topic":   "test-logs",
				},
				"rpc_server_name": "test-service",
			},
			expectSuccess: false,
			expectWarning: true,
			description:   "空的 brokers 配置应该产生警告",
		},
		{
			name: "no_topic_with_service_name",
			config: map[string]interface{}{
				"kafka-producer": map[string]interface{}{
					"brokers": []string{"localhost:9092"},
					"timeout": 10,
				},
				"rpc_server_name": "my-service",
			},
			expectSuccess: true,
			expectWarning: false,
			description:   "没有主题配置时应该使用服务名称生成默认主题",
		},
		{
			name: "no_topic_no_service_name",
			config: map[string]interface{}{
				"kafka-producer": map[string]interface{}{
					"brokers": []string{"localhost:9092"},
					"timeout": 10,
				},
			},
			expectSuccess: true,
			expectWarning: false,
			description:   "没有主题和服务名称时应该使用默认主题",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置配置
			viper.Reset()
			
			// 设置测试配置
			for k, v := range tt.config {
				viper.Set(k, v)
			}

			// 设置基础日志（避免重复初始化）
			logrus.SetLevel(logrus.DebugLevel)

			// 调用被测试的函数
			setupKafkaErrorLog()

			// 这里我们主要测试函数不会 panic
			// 实际的 Kafka 连接测试需要真实的 Kafka 环境
			t.Logf("Test '%s' completed: %s", tt.name, tt.description)
		})
	}
}

// TestSetupLogWithKafka 测试完整的日志设置流程
func TestSetupLogWithKafka(t *testing.T) {
	// 保存原始配置
	originalConfig := viper.AllSettings()
	defer func() {
		viper.Reset()
		for k, v := range originalConfig {
			viper.Set(k, v)
		}
	}()

	// 设置测试配置
	viper.Reset()
	viper.Set("log_level", "debug")
	viper.Set("log_json", false)
	viper.Set("rpc_server_name", "test-service")
	viper.Set("kafka-producer", map[string]interface{}{
		"brokers": []string{"localhost:9092"},
		"topic":   "test-error-logs",
		"timeout": 10,
	})

	// 调用完整的日志设置
	setupLog()

	// 测试日志记录
	logrus.Debug("Debug message - should not go to Kafka")
	logrus.Info("Info message - should not go to Kafka")
	logrus.Warn("Warning message - should not go to Kafka")
	
	// 注意：这些错误日志会尝试发送到 Kafka，但在测试环境中会失败
	// 这是预期的行为
	logrus.Error("Error message - should attempt to send to Kafka")
	logrus.WithFields(logrus.Fields{
		"test_field": "test_value",
	}).Error("Error with fields - should attempt to send to Kafka")

	t.Log("Complete log setup test completed successfully")
}

// BenchmarkSetupKafkaErrorLog 性能测试
func BenchmarkSetupKafkaErrorLog(b *testing.B) {
	// 设置测试配置
	viper.Reset()
	viper.Set("kafka-producer", map[string]interface{}{
		"brokers": []string{"localhost:9092"},
		"topic":   "benchmark-logs",
		"timeout": 10,
	})
	viper.Set("rpc_server_name", "benchmark-service")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		setupKafkaErrorLog()
	}
}
