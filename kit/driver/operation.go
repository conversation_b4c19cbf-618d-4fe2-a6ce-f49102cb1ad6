package driver

import (
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/ginx"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"git.keepfancy.xyz/back-end/frameworks/lib/ip"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/gin-gonic/gin"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

type DiskInfo struct {
	Disks []DiskUsage `json:"disks"`
}

type DiskUsage struct {
	MountPoint string  `json:"mountpoint"`
	Device     string  `json:"device"`
	Total      uint64  `json:"total"`
	Used       uint64  `json:"used"`
	Free       uint64  `json:"free"`
	UsedPct    float64 `json:"used_percent"`
}

// gracefulStop 优雅退休
func gracefulStop(service Service) {
	signalChan := make(chan os.Signal, 1)
	runningChan := make(chan bool, 1)

	var deadLineNum int
	r := ginx.GetGinEngine()
	infoGroup := r.Group("sys")
	{
		infoGroup.GET(dict.SysWordErRouteShutdown, func(c *gin.Context) {
			deadline := c.DefaultQuery(dict.SysWordErDeadline, "3")
			if deadline != "" {
				interval := transform.Str2Int(deadline)
				if interval <= 0 {
					c.JSON(http.StatusBadRequest, gin.H{
						"error": "graceful stop time must not zero",
					})
					logrus.Errorln("graceful stop time zero")
					return
				}
				deadLineNum = interval

				sForce := ""
				if deadLineNum > 0 {
					sForce = fmt.Sprintf("[graceful stop] => %ds后服务未关闭，强制停止服务", deadLineNum)
					logrus.Infoln(sForce)

					safego.Go(func() {
						forceStopTick := time.NewTicker(time.Duration(deadLineNum) * time.Second)
						select {
						case <-forceStopTick.C:
							if err := service.ForceStop(); err != nil {
								logrus.Errorf("force stop service failed[%v]", err)
							}
						}
					})
				}

				runningChan <- true

				c.JSON(http.StatusOK, gin.H{
					"data": fmt.Sprintf("Graceful stop %s service. Force stop after %d second.",
						viper.GetString(dict.ConfigRpcServerName), deadLineNum),
					"machine_ip": ip.LocalIP(),
					"srv_port":   viper.GetString(dict.ConfigRpcPort),
					"srv_tag":    viper.GetString(dict.ConfigRpcServerTags),
				})
			}
		})

		infoGroup.GET(dict.SysWordErRouteSysOperate, func(c *gin.Context) {
			instruct := c.Query(dict.SysWordErRouteSysInstruct)
			switch instruct {
			case "disk":
				// 获取所有分区的磁盘使用情况
				parts, err := disk.Partitions(true)
				if err != nil {
					logrus.Error("get machine disk info error")
				}

				var allDisks []DiskUsage
				for _, part := range parts {
					diskUsage, err := disk.Usage(part.Mountpoint)
					if err != nil {
						logrus.Error("get machine disk info error")
					}

					du := DiskUsage{
						MountPoint: part.Mountpoint,
						Device:     part.Device,
						Total:      diskUsage.Total,
						Used:       diskUsage.Used,
						Free:       diskUsage.Free,
						UsedPct:    diskUsage.UsedPercent,
					}
					allDisks = append(allDisks, du)
				}

				diskInfo := DiskInfo{Disks: allDisks}

				c.JSON(http.StatusOK, gin.H{
					"data":       fmt.Sprintf("Operate %s service", viper.GetString(dict.ConfigRpcServerName)),
					"machine_ip": ip.LocalIP(),
					"disk_info":  diskInfo,
				})
				break
			case "cpu":
				// 获取CPU使用率
				usage, err := cpu.Percent(time.Second*1, false) // 第二个参数为false，表示不阻塞等待
				if err != nil {
					fmt.Println("Error getting CPU usage:", err)
					return
				}

				// 获取每个核的CPU使用率
				cpuTimes, err := cpu.Times(false)
				if err != nil {
					fmt.Println("Error getting CPU times:", err)
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"data":          fmt.Sprintf("Operate %s service", viper.GetString(dict.ConfigRpcServerName)),
					"machine_ip":    ip.LocalIP(),
					"now_cpu_used":  usage,
					"now_cpu_every": cpuTimes,
				})
				break
			case "mem":
				memInfo, err := mem.VirtualMemory()
				if err != nil {
					logrus.Error("get machine mem info error")
				}

				c.JSON(http.StatusOK, gin.H{
					"data":       fmt.Sprintf("Operate %s service", viper.GetString(dict.ConfigRpcServerName)),
					"machine_ip": ip.LocalIP(),
					"mem_info":   memInfo,
				})
				break
			}
		})
	}

	signal.Notify(signalChan,
		os.Interrupt,
		syscall.SIGQUIT,
		os.Kill,
		syscall.SIGTERM,
		syscall.SIGINT,
		syscall.SIGKILL)
	go func() {
		sig := <-signalChan
		logrus.Infof("接收到结束信号%v，服务将退出", sig)
		runningChan <- true
	}()
	<-runningChan
}
