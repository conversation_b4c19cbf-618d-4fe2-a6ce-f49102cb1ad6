// Package driver
// Copyright 2024 tCloud.yin All rights reserved.
// 服务基础类
package driver

import (
	"flag"
	"fmt"
	"net/http"
	"os"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/ginx"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/pflag"

	"sync"

	"github.com/sirupsen/logrus"

	"github.com/spf13/viper"
)

// Service 是每个使用服务必须实现的接口
type Service interface {

	// Init 作一些初始化操作。 参数 e 为服务提供了一些通用的接口。
	// 如果该函数返回非 nil 错误， driver 不会执行 Start 函数
	Init() error

	// Start 启动服务，服务可以在该函数内部 block
	// driver 在调用 Init 函数返回成功后，调用 Start 函数启动服务
	Start() error

	// Stop 停止服务 服务可以在该函数内部 block
	Stop() error

	// ForceStop 强制停止，不执行退休策略
	ForceStop() error
}

// 环境配置设置
func setup() {
	flag.String(dict.SysWordConfig, "", "config file (default is ./config.yml)")
	flag.Int(dict.ConfigTcpPort, 0, "tcp port")
	flag.Int(dict.ConfigRpcPort, 0, "intranet port")
	flag.Bool(dict.ConfigReadmeHelp, false, "show help")

	pflag.CommandLine.AddGoFlagSet(flag.CommandLine)
	pflag.Parse()
	err := viper.BindPFlags(pflag.CommandLine)
	if err != nil {
		return
	}

	if cfgFile := viper.GetString(dict.SysWordConfig); cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		viper.SetConfigFile(dict.SysDefaultConfig)
	}
	viper.AutomaticEnv()
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Sprintf("Read config file fail:%v", err))
	}

	viper.WatchConfig()
	viper.OnConfigChange(watchConfigChange)

	// Notice : 开启Prometheus上报服务
	// http.Handle("/metrics", promhttp.Handler())
}

// 日志设置
func setupLog() {
	logLevel := viper.GetString(dict.ConfigLogLevel)     // 日志级别
	printFormatJson := viper.GetBool(dict.ConfigLogJson) // 输出格式化Json
	logdog.SetupLog(logLevel, printFormatJson)

	if viper.GetBool(dict.ConfigLogWrite) {
		fileName := viper.GetString(dict.ConfigRpcServerName) + "_" + viper.GetString(dict.ConfigRpcPort)
		logDir := viper.GetString(dict.ConfigLogDir) // 日志文件目录
		logdog.SetWriteFile(fileName, logDir, logLevel)
	}

	// logdog.AddHookLog2Kafka() // 添加日志钩子

	rpcSrvName := viper.GetString(dict.ConfigRpcServerName)
	logrus.Infof(" - [tcgo]using srvname: {%s} run beginning...", rpcSrvName)
	logrus.Infof(" - [tcgo]using logLevel:  # %s", logLevel)
	logrus.Infof(" - [tcgo]using printJson:  # %t", printFormatJson)
	logrus.Infof(" - [tcgo]using logdir:  # %s", viper.GetString(dict.ConfigLogDir))
}

// 监听配置变化
func watchConfigChange(e fsnotify.Event) {
	cfgLogLevel, _ := logrus.ParseLevel(viper.GetString(dict.ConfigLogLevel))

	if logrus.GetLevel() != cfgLogLevel {
		logrus.Tracef("watch config change of log level : {%s}", viper.GetString(dict.ConfigLogLevel))
		logrus.SetLevel(cfgLogLevel)
	}
}

// Run 启动服务
func Run(service Service) {
	random.NewInitSource()

	// 环境设置的初始化
	setup()

	if viper.GetBool(dict.ConfigReadmeHelp) {
		ShowAppInfo()
		return
	}

	// nsq初始设置
	nsqx.Setup()

	// 日志文件初始化
	setupLog()

	// 服务初始化
	if err := service.Init(); err != nil {
		panic(err)
	}
	logrus.Infof("[%s]服务初始化完成", viper.GetString("rpc_server_name"))

	wg := sync.WaitGroup{}

	// 启动RPC服务
	wg.Add(1)
	go func() {
		defer wg.Done()

		if err := rpc.StartServer(); err != nil {
			panic(err)
		}
		logrus.Infof("关闭 intranet 服务完成")
	}()

	// 初始化网关链路通道，接受来自网关转发客户端消息
	transport.InitIntranetGrpcServer()

	// 调用服务Start
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer logdog.RecoverAndLog("runs-vc")
		if err := service.Start(); err != nil {
			panic(err)
		}
	}()

	// http服务
	viper.SetDefault(dict.ConfigHttpPort, dict.SysDefaultHttpPort)
	httpSvr := ginx.GetRawHttpEngine()
	wg.Add(1)
	go func() {
		defer wg.Done()
		logrus.Infof("开启 http 服务，addr: %s", httpSvr.Addr)
		if err := httpSvr.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			panic(err)
		}
	}()
	// 开启prometheus监控指标Handler
	if !viper.GetBool(dict.ConfigPrometheusClose) {
		ginx.GetGinEngine().GET("/metrics", gin.WrapH(promhttp.Handler()))
	}

	// 定时拉取日志配置级别
	// TODO 需要重构,viper本身就支持配置文件的变化监听,不需要通过定时拉取
	// watchConfigChange()

	// 设置优雅关闭,提供内部http接口,可以curl,也可以供给管理平台调用
	gracefulStop(service)

	logrus.Infoln("服务开始关闭...")

	// 先反注册，不让新的匹配进来
	rpc.DeregisterServer()

	if err := service.Stop(); err != nil {
		logrus.Errorf("stop service failed[%v]", err)
	}

	// 停止RPC和NSQ
	rpc.StopServer()

	// 停止nsq
	nsqx.Stop()

	logrus.Infoln("server closed...")
	logdog.StopLog()
	os.Exit(0)
	wg.Wait()
}
