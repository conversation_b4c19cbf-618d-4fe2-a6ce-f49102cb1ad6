package nsqx

import (
	"fmt"
	"strconv"
	"testing"

	"github.com/spf13/viper"
)

func init() {
	viper.Set("nsqd_addr", "localhost:4150")
	viper.Set("nsqlookupd_addrs", []string{"localhost:4161"})
	viper.Set("nsqd_http_addr", "localhost:4151")

	Setup()
}

func TestPubAndSub(t *testing.T) {
	// CreateTopic("test-topic")

	err := Subscribe("test-topic", "test-channel", func(body []byte) {
		fmt.Printf("收到消息: %s\n", string(body))
	})
	if err != nil {
		t.Fatalf("订阅失败: %s", err)
	}

	for i := 0; i < 1000; i++ {
		err := Publish("test-topic", []byte(strconv.Itoa(i)))
		if err != nil {
			t.Fatalf("发布失败")
		}
	}
}

func Test_CreateTwice(t *testing.T) {
	// err := CreateTopic("bindPhone")
	// err := nsq.c
	// fmt.Println(err)
}
