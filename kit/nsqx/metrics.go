package nsqx

import (
	"github.com/prometheus/client_golang/prometheus"
)

var (
	nsqPublishCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "nsq_publish_count",
			Help: "nsq topic 发布完成数量",
		},
		[]string{"topic"},
	)

	nsqPublishErrCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "nsq_publish_err_count",
			Help: "nsq topic 发布失败数量",
		},
		[]string{"topic"},
	)

	nsqConsumeStartCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "nsq_consume_start_count",
			Help: "nsq 消息开始消费数量",
		},
		[]string{"topic", "channel"},
	)

	nsqConsumeFinishCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "nsq_consume_finish_count",
			Help: "nsq 消息完成消费数量",
		},
		[]string{"topic", "channel"},
	)

	nsqConsumeSeconds = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "nsq_consume_handle_seconds",
			Help:    "nsq 消息消费时长",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"topic", "channel"},
	)
)

func init() {
	prometheus.MustRegister(
		nsqPublishCounter,
		nsqPublishErrCounter,
		nsqConsumeStartCounter,
		nsqConsumeFinishCounter,
		nsqConsumeSeconds)
}
