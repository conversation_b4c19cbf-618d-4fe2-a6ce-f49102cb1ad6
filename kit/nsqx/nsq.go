// Package nsqx module
// 服务节点消息队列
package nsqx

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"runtime"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/nsqio/go-nsq"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

var (
	// producer 全局的发布者对象，driver 在启动时创建
	producer *nsq.Producer

	consumers []*nsq.Consumer
	mu        sync.Mutex

	uniques []topicChannel
)

type topicChannel struct {
	topic, channel string
}

// Setup 初始化包，driver 在启动时调用。
//
// 如果没有配置 nsqd_addr 地址，将不会创建发布者对象。
func Setup() {
	nsqDAddr := viper.GetString(dict.ConfigNsqDAddr)
	if len(nsqDAddr) == 0 {
		logrus.Warnf("未配置 nsqd_addr，不创建 producer")
		return
	}

	var err error
	producer, err = nsq.NewProducer(nsqDAddr, nsq.NewConfig())

	// nsq 输出日志级别
	if viper.IsSet(dict.ConfigGinEnvMode) {
		envMode := viper.GetString(dict.ConfigGinEnvMode)
		// release debug test
		if envMode == "debug" {
			producer.SetLoggerLevel(nsq.LogLevelDebug)
		} else {
			producer.SetLoggerLevel(nsq.LogLevelInfo)
		}
	} else {
		producer.SetLoggerLevel(nsq.LogLevelError)
	}

	if err != nil {
		panic(err)
	}
}

// Stop 停止服务时调用。
func Stop() {
	mu.Lock()
	defer mu.Unlock()

	logrus.Infof("准备停止 nsq 的所有 consumer, 数量: %d", len(consumers))
	for _, consumer := range consumers {
		consumer.Stop()
	}
	logrus.Infof("停止 nsq 的所有 consumer 完成")

	for _, tc := range uniques {
		url := fmt.Sprintf("http://%s/channel/delete?topic=%s&channel=%s",
			viper.GetString(dict.ConfigNsqHttpAddr), tc.topic, tc.channel)
		resp, err := http.Post(url, "", nil)
		if err != nil {
			logrus.WithError(err).Errorf("删除channel失败, topic:%s channel:%s", tc.topic, tc.channel)
			continue
		}

		if resp.StatusCode != 200 {
			logrus.Errorf("删除 channel 失败，statusCode: %d topic:%s channel:%s", resp.StatusCode, tc.topic, tc.channel)
			continue
		}

		logrus.Infof("删除 channel 成功，topic:%s channel:%s", tc.topic, tc.channel)
	}
}

// PublishJson 发布消息，Json格式数据
func PublishJson(topic string, body interface{}) error {
	data, err := json.Marshal(body)
	if err != nil {
		logrus.WithError(err).Errorf("发布消息序列化失败，topic: %s, body:%v", topic, body)
		return err
	}
	return Publish(topic, data)
}

// Publish 发布 nsq 消息
func Publish(topic string, body []byte) error {
	safego.Go(func() {
		err := producer.Publish(topic, body)
		if err != nil {
			logrus.WithError(err).Errorf("发布消息失败，topic: %s", topic)
			nsqPublishErrCounter.WithLabelValues(topic).Inc()
		}
		nsqPublishCounter.WithLabelValues(topic).Inc()
	})
	return nil
}

// DeferredPublish 延迟消息
func DeferredPublish(topic string, delay time.Duration, body []byte) error {
	err := producer.DeferredPublish(topic, delay, body)
	if err != nil {
		logrus.WithError(err).Errorf("发布延迟消息失败，topic：%s，body：%s", topic, string(body))
		nsqPublishErrCounter.WithLabelValues(topic).Inc()
		return err
	}

	nsqPublishCounter.WithLabelValues(topic).Inc()
	return nil
}

// DeferredPublishJSON 延迟发送json消息
func DeferredPublishJSON(topic string, delay time.Duration, body interface{}) error {
	data, err := json.Marshal(body)
	if err != nil {
		logrus.WithError(err).Errorf("发布消息序列化失败，topic: %s, body:%v", topic, body)
		return err
	}
	return DeferredPublish(topic, delay, data)
}

// Subscribe 使用 channel 订阅消息 topic。
//
// 此函数需要在 driver 启动后调用，否则会订阅失败。
// MaxInFlight 固定设置为 cpu num，表示窗口（可以类比 tcp 的滑动窗口）大小。
// 同时并发处理的数量固定设置为 runtime.NumCPU()。
func Subscribe(topic, channel string, handler func(body []byte)) error {
	createTopicChannel(topic, channel)
	entry := logrus.WithFields(logrus.Fields{
		"topic": topic, "channel": channel,
	})

	lookupAddr := viper.GetStringSlice(dict.ConfigNsqLookUpdAddress)
	if len(lookupAddr) == 0 {
		entry.Errorf("未配置 nsqlookupd_addrs")
		return errors.New("配置错误")
	}

	config := nsq.NewConfig()
	// config.MaxInFlight = 20 // 配置不能随意配置，需要大于或者等于nsqd的数量
	config.MaxInFlight = runtime.NumCPU()
	maxInFlight := viper.GetInt(dict.ConfigNsqMaxInflight)
	if maxInFlight != 0 {
		config.MaxInFlight = maxInFlight
	}

	consumer, err := nsq.NewConsumer(topic, channel, config)
	if err != nil {
		entry.WithError(err).Errorf("创建 consumer 失败")
		return err
	}

	concurrent := runtime.NumCPU()
	consumer.AddConcurrentHandlers(nsq.HandlerFunc(func(message *nsq.Message) error {
		defer logdog.RecoverAndLog()

		nsqConsumeStartCounter.WithLabelValues(topic, channel).Inc()
		start := time.Now()
		handler(message.Body)
		nsqConsumeSeconds.WithLabelValues(topic, channel).Observe(time.Now().Sub(start).Seconds())

		nsqConsumeFinishCounter.WithLabelValues(topic, channel).Inc()

		return nil
	}), concurrent)

	if err := consumer.ConnectToNSQLookupds(lookupAddr); err != nil {
		entry.WithError(err).Errorf("连接 nsqlookupd 失败")
		return err
	}

	logrus.Infof("订阅成功，concurrent: %d", concurrent)

	mu.Lock()
	consumers = append(consumers, consumer)
	mu.Unlock()

	return nil
}

// SubscribeUnique 独占通道订阅topic
// 服务正常结束后，channel自动释放
func SubscribeUnique(topic string, handler func(body []byte)) error {
	// 唯一订阅需要在服务结束后删除 channel，所以强制要求配置 nsqd_http_addr。
	if len(viper.GetString(dict.ConfigNsqHttpAddr)) == 0 {
		logrus.Errorf("未配置 nsqd_http_addr")
		return errors.New("未配置 nsqd_http_addr")
	}

	channel := fmt.Sprintf("%s-%s",
		viper.GetString(dict.ConfigRpcAddr),
		viper.GetString(dict.ConfigRpcPort))

	if err := Subscribe(topic, channel, handler); err != nil {
		return err
	}

	mu.Lock()
	uniques = append(uniques, topicChannel{
		topic: topic, channel: channel,
	})
	mu.Unlock()

	return nil
}

// createTopicChannel 创建 topic和channel
func createTopicChannel(topic string, channel string) error {
	addr := viper.GetString(dict.ConfigNsqHttpAddr)
	if len(addr) == 0 {
		logrus.Errorf("未配置 nsqd_http_addr")
		return errors.New("未配置 nsqd_http_addr")
	}

	url := fmt.Sprintf("http://%s/topic/create?topic=%s", addr, topic)
	topicRsp, err := http.Post(url, "", nil)
	if err != nil {
		logrus.WithField("url", url).WithError(err).Errorf("创建topic请求失败")
		return err
	}
	defer topicRsp.Body.Close()
	if topicRsp.StatusCode != 200 {
		logrus.WithField("url", url).Errorf("topic请求失败，statusCode: %d", topicRsp.StatusCode)
		return errors.New("http 请求失败")
	}

	url = fmt.Sprintf("http://%s/channel/create?topic=%s&channel=%s", addr, topic, channel)
	channelRsp, err := http.Post(url, "", nil)
	if err != nil {
		logrus.WithField("url", url).WithError(err).Errorf("创建channel请求失败")
		return err
	}

	defer channelRsp.Body.Close()
	if channelRsp.StatusCode != 200 {
		logrus.WithField("url", url).Errorf("channel请求失败，statusCode: %d", channelRsp.StatusCode)
		return errors.New("http 请求失败")
	}

	logrus.Infof("创建topic和channel 成功，topic: %s, channel: %s", topic, channel)
	return nil
}
