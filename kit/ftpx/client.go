package ftpx

import (
	"github.com/jlaffaye/ftp"
	"os"
	"path/filepath"
	"time"
)

type FtpServer struct {
	c *ftp.ServerConn
	o *Options
}

func (f *FtpServer) Login(opt ...FuncOptions) error {
	f.o = newOptions(opt...)
	var err error
	f.c, err = ftp.Dial(f.o.Host, ftp.DialWithTimeout(5*time.Second))
	if err != nil {
		panic(err)
	}

	if err := f.c.<PERSON>gin(f.o.UserName, f.o.Password); err != nil {
		panic(err)
	}

	return nil
}

func (f *FtpServer) Close() {
	f.c.Quit()
}

func (f *FtpServer) UploadDir(baseDir, localDir string) error {
	// 获取本地文件夹内容
	infos, errRead := os.ReadDir(localDir)
	if errRead != nil {
		return errRead
	}

	for _, info := range infos {
		localPath := filepath.Join(localDir, info.Name())
		remotePath := filepath.Join(baseDir, info.Name())

		if info.IsDir() {
			// 递归创建远程文件夹
			err := f.c.MakeDir(remotePath)
			if err != nil {
				return err
			}

			err = f.UploadDir(remotePath, localPath)
			if err != nil {
				return err
			}
		} else {
			// 上传文件
			file, err := os.Open(localPath)
			if err != nil {
				return err
			}

			err = f.c.Stor(remotePath, file)
			if err != nil {
				return err
			}

			file.Close()
		}
	}
	return nil
}
