package cache_strategy

// 数据不存在
const Empty = CacheError("data not exist")

// 业务未开启
const SessionNotOpen = CacheError("session not open")

// 事务重复开启
const TransactionAlreadyOpen = CacheError("transaction already open")

// 数据库未开启
const EngineNotOpen = CacheError("engine not open")

// 数据库操作失败
const DBOperationFailed = CacheError("db operation failed")

// 未开启redis
const RedisNotOpen = CacheError("redis not open")

type CacheError string

func (e CacheError) Error() string {
	return string(e)
}
