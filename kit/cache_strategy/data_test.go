package cache_strategy

import (
	"context"
	"fmt"
	"testing"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/cache_strategy/mock_data"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"
	"github.com/ldy105cn/xorm"
	"github.com/spf13/viper"
)

func testInit() {
	severHost := "************"
	initRedis(severHost)
	initSql(severHost)
}

func initRedis(server string) {
	addr := server + ":6379"
	passwd := "8888"
	conf := map[string]string{
		"addr":   server + ":6379",
		"passwd": "8888",
	}
	viper.Set(dict.ConfigRedisAddr, addr)
	viper.Set(dict.ConfigRedisPwd, passwd)
	viper.Set("redis_list", map[string]interface{}{
		"general": conf,
	})
}

func initSql(server string) {
	db := "general"
	conf := map[string]interface{}{
		"addr":   server + ":3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     db,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		db: conf,
	})
}
func TestSyncTable(t *testing.T) {
	testInit()
	user := &mock_data.User{}
	engine, err := user.GetEngine()
	if err != nil {
		t.Fatal(err)
	}
	err = engine.Table(user.GetTableName()).Sync2(user)
	if err != nil {
		t.Fatal(err)
	}
}

func NewUserDataMgr(session *xorm.Session) (*CSDataMgr, error) {
	engine, err := mysql.GetDefaultMgr().GetMyEngine("general")
	if err != nil {
		return nil, err
	}
	redisCli := redisfactory.GetRedisClient("general", 7)
	csData := &CSDataMgr{NewDataMgr(engine, redisCli), false}
	if session != nil {
		csData.SetSession(session)
	}
	return csData, nil
}

func TestCacheAsideSession(t *testing.T) {
	testInit()
	csData, err := NewUserDataMgr(nil)
	if err != nil {
		t.Fatal(err)
	}
	csData.ShowSQL()
	close, err := csData.StartTransaction()
	if err != nil {
		t.Fatal(err)
	}
	defer close()

	user := &mock_data.User{
		Id:   8,
		Name: "test",
		Age:  18,
	}
	ctx := context.Background()
	err = csData.UpdateSingle(ctx, user)
	if err != nil {
		t.Fatal(err)
	}
	// err = csData.DeleteSingle(ctx, user)
	// if err != nil {
	// 	t.Fatal(err)
	// }
	err = csData.Commit()
	if err != nil {
		t.Fatal(err)
	}
}

func TestCacheAsideSingleUpdate(t *testing.T) {
	testInit()
	cssingle, err := NewUserDataMgr(nil)
	if err != nil {
		t.Fatal(err)
	}
	user := &mock_data.User{
		Id:   3,
		Name: "test",
		Age:  18,
	}
	ctx := context.Background()
	err = cssingle.UpdateSingle(ctx, user)
	if err != nil {
		t.Fatal(err)
	}
}

func TestCacheAsideSingleQuery(t *testing.T) {
	testInit()
	cssingle, err := NewUserDataMgr(nil)
	if err != nil {
		t.Fatal(err)
	}
	ctx := context.Background()
	user := &mock_data.User{
		Id: 2,
	}
	data, err := cssingle.QuerySingle(ctx, user)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Printf("user: %+v", data.(*mock_data.User))
}

func TestCacheAsideSingleDelete(t *testing.T) {
	testInit()
	cssingle, err := NewUserDataMgr(nil)
	if err != nil {
		t.Fatal(err)
	}
	ctx := context.Background()
	user := &mock_data.User{
		Id: 3,
	}
	cssingle.DeleteSingle(ctx, user)
}

func TestCacheAsideSingleHash(t *testing.T) {
	testInit()
	cssingle, err := NewUserDataMgr(nil)
	if err != nil {
		t.Fatal(err)
	}
	cssingle.IsHash = true
	user := &mock_data.User{
		Id:   88,
		Name: "testHash",
		Age:  18,
	}
	ctx := context.Background()
	// err = cssingle.UpdateSingle(ctx, user)
	// if err != nil {
	// 	t.Fatal(err)
	// }
	data, err := cssingle.QuerySingle(ctx, user)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Printf("user: %+v", data.(*mock_data.User))
	err = cssingle.DeleteSingle(ctx, user)
	if err != nil {
		t.Fatal(err)
	}
}
