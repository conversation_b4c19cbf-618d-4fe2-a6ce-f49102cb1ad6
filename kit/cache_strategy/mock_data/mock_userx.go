package mock_data

import (
	"fmt"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"
	"github.com/go-redis/redis/v8"
	"github.com/ldy105cn/xorm"
	"xorm.io/core"
)

type User struct {
	Id   int    `xorm:"pk"`
	Name string `xorm:"varchar(255)"`
	Age  int    `xorm:"int"`
}

func (u *User) GetTableName() string {
	return "userx"
}

func (u *User) GetRedisKey() string {
	return fmt.Sprintf("userx:%d", u.Id)
}

func (u *User) GetBean() interface{} {
	bean := &User{
		Id: u.Id,
	}
	return bean
}

func (u *User) GetExpire() time.Duration {
	return 10 * time.Minute
}

func (u *User) GetEngine() (*xorm.Engine, error) {
	engine, err := mysql.GetDefaultMgr().GetMyEngine("general")
	if err != nil {
		return nil, err
	}
	engine.ShowSQL(true)
	engine.SetLogLevel(core.LOG_DEBUG)

	return engine, nil
}

func (u *User) GetRedisCli() *redis.Client {
	cli := redisfactory.GetRedisClient("general", 7)
	return cli
}
