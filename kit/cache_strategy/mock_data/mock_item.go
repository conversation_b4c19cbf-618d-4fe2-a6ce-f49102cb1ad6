package mock_data

import (
	"fmt"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"
	"github.com/go-redis/redis/v8"
	"github.com/ldy105cn/xorm"
	"xorm.io/core"
)

type Item struct {
	PlayerId int    `xorm:"pk"`
	Id       int    `xorm:"int autoincr"`
	Name     string `xorm:"varchar(255)"`
	Price    int    `xorm:"int"`
}

func (u *Item) GetTableName() string {
	return "itemx"
}

func (u *Item) GetRedisKey() string {
	return fmt.Sprintf("itemx:%d", u.PlayerId)
}

func (u *Item) GetBean() interface{} {
	bean := &Item{
		PlayerId: u.PlayerId,
		Id:       u.Id,
	}
	return bean
}

func (u *Item) GetExpire() time.Duration {
	return 10 * time.Minute
}

func (u *Item) GetEngine() (*xorm.Engine, error) {
	engine, err := mysql.GetDefaultMgr().GetMyEngine("general")
	if err != nil {
		return nil, err
	}
	engine.ShowSQL(true)
	engine.SetLogLevel(core.LOG_DEBUG)

	return engine, nil
}

func (u *Item) GetRedisCli() *redis.Client {
	cli := redisfactory.GetRedisClient("general", 7)
	return cli
}
