package cache_strategy

import (
	"context"

	"github.com/go-redis/redis/v8"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
	"xorm.io/core"
)

const (
	data_nil_key = "nil"
)

type IDataMgrFast interface {
	GetEngine() (*xorm.Engine, error)
	GetRedisCli() *redis.Client
}

// 通常操作数据不会散列到多个表，所以这里只提供一个session和redisCli
type DataMgr struct {
	session  *xorm.Session
	engine   *xorm.Engine
	redisCli *redis.Client
}

type IDataMgr interface {
	GetEngine() *xorm.Engine
	GetRedisCli() *redis.Client
	GetSession() (*xorm.Session, error)
	ShowSQL()
	StartTransaction() (func(), error)
	Commit() error
	Rollback() error
}

var _ IDataMgr = &DataMgr{}

// NewDataMgr 创建数据管理器
func NewDataMgr(engine *xorm.Engine, redisCli *redis.Client) *DataMgr {
	return &DataMgr{
		engine:   engine,
		redisCli: redisCli,
	}
}

// NewDataMgrByModel 根据数据源创建数据管理器
func NewDataMgrByModel(data IDataMgrFast) *DataMgr {
	engine, err := data.GetEngine()
	if err != nil {
		return nil
	}
	redisCli := data.GetRedisCli()
	return NewDataMgr(engine, redisCli)
}

// Check 检查数据源是否开启
func (d *DataMgr) Check() error {
	if d.engine == nil {
		return EngineNotOpen
	}
	if d.redisCli == nil {
		return RedisNotOpen
	}
	return nil
}

// SetSession 设置session
func (d *DataMgr) SetSession(session *xorm.Session) {
	d.session = session
}

// ShowSQL 显示sql
func (d *DataMgr) ShowSQL() {
	if d.engine == nil {
		return
	}
	d.engine.ShowSQL(true)
	d.engine.SetLogLevel(core.LOG_DEBUG)
}

// StartTransaction 开启事务
func (d *DataMgr) StartTransaction() (func(), error) {
	if d.engine == nil {
		return nil, EngineNotOpen
	}
	if d.session != nil {
		logrus.Error("transaction already open") // 防呆接口
		return nil, TransactionAlreadyOpen
	}
	d.session = d.engine.NewSession()

	err := d.session.Begin()
	if err != nil {
		return nil, err
	}

	return func() {
		d.session.Close()
	}, nil
}

// 获取session （用于统一提交)
func (d *DataMgr) GetSession() (*xorm.Session, error) {
	// 非事务模式操作
	if d.session == nil {
		return d.engine.Context(context.Background()), nil
	}

	return d.session, nil
}

// 使用相同的redis db 操作
func (d *DataMgr) GetRedisCli() *redis.Client {
	return d.redisCli
}

// Commit 提交事务
func (d *DataMgr) Commit() error {
	if d.session == nil {
		return SessionNotOpen
	}
	return d.session.Commit()
}

// Rollback 回滚
func (d *DataMgr) Rollback() error {
	if d.session == nil {
		return SessionNotOpen
	}
	return d.session.Rollback()
}

func (d *DataMgr) GetEngine() *xorm.Engine {
	return d.engine
}
