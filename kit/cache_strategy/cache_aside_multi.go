package cache_strategy

import "context"

// Cash-Aside 列式缓存 hash
func (c *CSDataMgr) QueryMulti(ctx context.Context, bean ICSData) (data []any, err error) {
	data, err = c.queryMultiPersistence(ctx, bean)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (c *CSDataMgr) UpdateMulti(ctx context.Context, bean ICSData) error {
	return nil
}

func (c *CSDataMgr) DeleteMulti(ctx context.Context, bean ICSData) error {
	return nil
}

func (c *CSDataMgr) queryMultiPersistence(ctx context.Context, bean ICSData) ([]any, error) {
	session, err := c.GetSession()
	if err != nil {
		return nil, err
	}
	session.Table(bean.GetTableName())
	data := make([]any, 0)
	err = session.Find(&data, bean.GetBean())
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (c *CSDataMgr) deleteMultiPersistence(ctx context.Context, bean ICSData) error {
	return nil
}

func (c *CSDataMgr) updateMultiPersistence(ctx context.Context, bean ICSData) error {
	return nil
}
