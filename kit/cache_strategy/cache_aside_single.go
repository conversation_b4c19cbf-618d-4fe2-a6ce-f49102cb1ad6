package cache_strategy

import (
	"context"
	"encoding/json"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
)

// Cache-Aside 数据管理器 (单结构数据)
type CSDataMgr struct {
	*DataMgr
	IsHash bool // 是否使用hash存储
}

// QuerySingle 结构体缓存
func (c *CSDataMgr) QuerySingle(ctx context.Context, bean ICSData) (data any, err error) {
	// 查询redis
	data, err = c.queryCache(ctx, bean)
	if err != nil {
		if err == redis.Nil {
			// 缓存未命中，查询数据库
			data, err = c.queryPersistence(ctx, bean)
			// 数据库未命中，返回空
			if err == Empty {
				// 更新空缓存
				err = c.updateCacheNil(ctx, bean)
				if err != nil {
					return nil, err
				}
				return nil, Empty
			}
			if err != nil {
				return nil, err
			}
			err = c.updateCache(ctx, data.(ICSData))
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	return data, nil
}

// UpdateSingle 简单结构体更新
func (c *CSDataMgr) UpdateSingle(ctx context.Context, data ICSData) error {
	err := c.delCache(ctx, data)
	if err != nil {
		return err
	}
	// 存储数据
	err = c.updatePersistence(ctx, data)
	if err != nil {
		return err
	}

	// 延迟双删
	time.AfterFunc(time.Second, func() {
		c.delCache(ctx, data)
	})
	return nil
}

// DeleteSingle 删除数据
func (c *CSDataMgr) DeleteSingle(ctx context.Context, bean ICSData) error {
	err := c.delCache(ctx, bean)
	if err != nil {
		return err
	}
	err = c.deletePersistence(ctx, bean)
	if err != nil {
		return err
	}
	time.AfterFunc(time.Second, func() {
		c.delCache(ctx, bean)
	})
	return nil
}

// 查询缓存
func (c *CSDataMgr) queryCache(ctx context.Context, bean ICSData) (any, error) {

	redisCli := c.GetRedisCli()
	if redisCli == nil {
		return nil, RedisNotOpen
	}
	key := bean.GetRedisKey()

	rtn := bean.GetBean()
	if c.IsHash {
		// 处理成散列数据存储
		data, err := redisCli.HGetAll(ctx, key).Result()
		if err != nil {
			return nil, err
		}
		// 模拟 get模式下空返回
		if len(data) == 0 {
			return nil, redis.Nil
		}
		// 数据空标记拦截
		if data[data_nil_key] != "" {
			return nil, Empty
		}
		// 检查是否有自定义方法
		if f, ok := bean.(IHashFunc); ok {
			hash, err := f.QueryRdsHash(data)
			if err != nil {
				return nil, err
			}
			rtn = hash
		} else {
			err = transform.Map2Struct(data, rtn)
			if err != nil {
				return nil, err
			}
		}
		return rtn, nil
	} else {
		data, err := redisCli.Get(ctx, key).Result()
		if err != nil {
			return nil, err
		}
		if data == "" {
			return nil, Empty
		}

		err = json.Unmarshal([]byte(data), rtn)
		if err != nil {
			return nil, err
		}
	}
	return rtn, nil
}

// 删除缓存
func (c *CSDataMgr) delCache(ctx context.Context, data ICSData) error {
	redisCli := c.GetRedisCli()
	if redisCli == nil {
		return RedisNotOpen
	}
	key := data.GetRedisKey()
	return redisCli.Del(ctx, key).Err()
}

// 更新缓存
func (c *CSDataMgr) updateCache(ctx context.Context, data ICSData) error {
	redisCli := c.GetRedisCli()
	if redisCli == nil {
		return RedisNotOpen
	}
	key := data.GetRedisKey()
	dataMap := make(map[string]any)
	var err error

	if c.IsHash {
		// 检查是否有自定义Hash方法
		if f, ok := data.(IHashFunc); ok {
			dataMap, err = f.SaveRdsHash(data)
			if err != nil {
				return err
			}
		} else {
			err = transform.Struct2Map(data, dataMap)
			if err != nil {
				return err
			}
		}

		// 处理成散列数据存储
		pipe := redisCli.Pipeline()
		pipe.HSet(ctx, key, dataMap)
		pipe.Expire(ctx, key, data.GetExpire())
		_, err = pipe.Exec(ctx)
		if err != nil {
			return err
		}
	} else {
		// 处理成普通数据存储
		strData, err := json.Marshal(data)
		if err != nil {
			return err
		}
		err = redisCli.Set(ctx, key, strData, data.GetExpire()).Err()
		if err != nil {
			return err
		}
	}

	return nil
}

// 更新空缓存
func (c *CSDataMgr) updateCacheNil(ctx context.Context, data ICSData) error {
	redisCli := c.GetRedisCli()
	if redisCli == nil {
		return RedisNotOpen
	}
	key := data.GetRedisKey()
	if c.IsHash {
		pipe := redisCli.Pipeline()
		pipe.HSet(ctx, key, "nil", "nil")
		pipe.Expire(ctx, key, data.GetExpire())
		_, err := pipe.Exec(ctx)
		if err != nil {
			return err
		}
		return nil
	} else {
		return redisCli.Set(ctx, key, nil, data.GetExpire()).Err()
	}
}

// 持久化删除
func (c *CSDataMgr) deletePersistence(ctx context.Context, data ICSData) error {
	session, err := c.GetSession()
	if err != nil {
		return err
	}
	session.Table(data.GetTableName()).Delete(data.GetBean())
	return nil
}

// 持久化更新
func (c *CSDataMgr) updatePersistence(ctx context.Context, data ICSData) error {
	session, err := c.GetSession()
	if err != nil {
		return err
	}
	session = session.Table(data.GetTableName())
	exist, err := session.Exist(data.GetBean())
	if err != nil {
		return err
	}
	var effect int64
	if !exist {
		effect, err = session.Table(data.GetTableName()).Insert(data)
	} else {
		effect, err = session.Table(data.GetTableName()).Update(data, data.GetBean())
	}
	if err != nil {
		return err
	}
	_ = effect

	return nil
}

// 持久化查询
func (c *CSDataMgr) queryPersistence(ctx context.Context, data ICSData) (ICSData, error) {
	session, err := c.GetSession()
	if err != nil {
		return nil, err
	}
	session = session.Table(data.GetTableName())
	bean := data.GetBean()
	exist, err := session.Get(bean)
	if err != nil {
		return nil, err
	}
	if !exist {
		return nil, Empty
	}

	return bean.(ICSData), nil
}
