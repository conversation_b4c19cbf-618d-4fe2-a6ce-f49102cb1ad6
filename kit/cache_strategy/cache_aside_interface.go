package cache_strategy

import (
	"context"
	"time"
)

// 单结构据管理器
type ISingleDataMgr interface {
	IDataMgr
	QuerySingle(ctx context.Context, bean ICSData) (any, error)
	UpdateSingle(ctx context.Context, data ICSData) error
	DeleteSingle(ctx context.Context, bean ICSData) error
}

// 多结构据管理器
type IMultiDataMgr interface {
	QueryMulti(ctx context.Context, beans []ICSMutliData) (any, error)
	UpdateMulti(ctx context.Context, data []ICSMutliData) error
	DeleteMulti(ctx context.Context, beans []ICSMutliData) error
}

// Cache-Aside
type ICSData interface {
	GetTableName() string
	GetRedisKey() string
	GetBean() any // 获取数据检查对象 mysql where 条件
	GetExpire() time.Duration
}

type ICSMutliData interface {
	GetTableName() string
	GetRedisKey() string
	GetBean() any // 获取数据检查对象 mysql where 条件
	GetExpire() time.Duration
	GetHashkey() (feild string, value any) //  获取散列方法
}

type IHashFunc interface {
	// 序列化redis Hash内容
	SaveRdsHash(data any) (map[string]any, error)
	// 反序列化redis Hash内容
	QueryRdsHash(redisHash map[string]string) (any, error)
}
