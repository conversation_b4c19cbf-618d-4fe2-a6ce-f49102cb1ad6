package version

import "testing"

func TestVersionOrdinal(t *testing.T) {
	versions := []struct{ v string }{
		{"1.05.00.0156"},
		// Go versions
		{"1"},
		{"1.0.1"},
		{"1.0.2"},
	}
	for _, version := range versions {
		va := Ordinal(version.v)
		t.Log(va)
	}
}

func TestVersion(t *testing.T) {
	versions := []struct{ a, b string }{
		{"1.05.00.0156", "1.0.221.9289"},
		// Go versions
		{"1", "1.0.1"},
		{"1.0.1", "1.0.2"},
		{"1.0.2", "1.0.3"},
		{"1.0.3", "1.1"},
		{"1.1", "1.1.1"},
		{"1.1.1", "1.1.2"},
		{"1.1.2", "1.2"},
		{"2.1.2", "10.2"},
	}
	for _, version := range versions {
		a, b := Ordinal(version.a), Ordinal(version.b)
		switch {
		case a > b:
			t.Log(version.a, ">", version.b)
		case a < b:
			t.Log(version.a, "<", version.b)
		case a == b:
			t.Log(version.a, "=", version.b)
		}
	}
}
