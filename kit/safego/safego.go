package safego

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"runtime/debug"
	"time"
)

// Go safe function call
func Go(f func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logrus.Printf("[FATAL] panic occured> %v, stack:\n%s", r, string(debug.Stack()))
				logrus.Panicf("go coroutine fatal on Go %v", r)
				logrus.Panicf("go coroutine fatal on Go %v", string(debug.Stack()))
			}
		}()
		f()
	}()
}

func Do(f func()) {
	func() {
		defer func() {
			if r := recover(); r != nil {
				logrus.Printf("[FATAL] panic occured> %v, stack:\n%s", r, string(debug.Stack()))
				logrus.Panicf("go coroutine fatal on Do %v", r)
				logrus.Panicf("go coroutine fatal on Do %v", string(debug.Stack()))
			}
		}()
		f()
	}()
}

// SafeFunc safe function call
func SafeFunc(f func()) {
	defer func() {
		if r := recover(); r != nil {
			stack := string(debug.Stack())
			fmt.Println(time.Now().String())
			fmt.Println(r)
			fmt.Println(stack)
			logrus.Printf("[FATAL] panic occured> %v, stack:\n%s", r, string(debug.Stack()))
		}
	}()
	f()
}
