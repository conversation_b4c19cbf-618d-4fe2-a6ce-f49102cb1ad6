package env

import (
	"flag"
	"os"
)

// deploy env.
const (
	DeployEnvDev  = "dev"
	DeployEnvTest = "test"
	DeployEnvPre  = "pre"
	DeployEnvProd = "prod"
)

const (
	_deployEnv = DeployEnvDev
)

var (
	// DeployEnv deploy env where app at.
	DeployEnv string

	// Cluster 指定相同集群名为同一个环境，同一个服务。共享相同的服务注册，相同的分布式锁，共享数据
	Cluster string
)

func init() {
	Cluster = os.Getenv("CLUSTER")
	if Cluster == "" {
		Cluster = _deployEnv
	}
	addFlag(flag.CommandLine)
}

func addFlag(fs *flag.FlagSet) {
	fs.StringVar(&DeployEnv, "deploy.env", defaultString("DEPLOY_ENV", _deployEnv), "deploy env. or use DEPLOY_ENV env variable, value: dev/test/pre/prod or customized etc.")
}

func defaultString(env, value string) string {
	v := os.Getenv(env)
	if v == "" {
		return value
	}
	return v
}
