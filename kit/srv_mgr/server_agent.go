package srv_mgr

import (
	"fmt"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	consulApi "github.com/hashicorp/consul/api"
	"github.com/spf13/viper"
)

// GetAllSrvIpByName 获取某服务所有的地址
func GetAllSrvIpByName(srvName string) []string {
	return GetAllTagSrvIpByName(srvName, "")
}

// GetAllTagSrvIpByName 根据Tag获取某服务所有的地址
func GetAllTagSrvIpByName(srvName, tag string) []string {
	defaultConfig := consulApi.DefaultConfig()
	defaultConfig.Address = viper.GetString(dict.ConfigConsulAddr)

	cli, _ := consulApi.NewClient(defaultConfig)
	s := cli.Health()

	ss, _, _ := s.Service(
		srvName,
		tag,
		true,
		&consulApi.QueryOptions{},
	)

	adds := make([]string, 0, len(ss))
	for _, s := range ss {
		address := s.Service.Address
		if s.Service.Address == "" {
			address = s.Node.Address
		}
		Address := fmt.Sprintf("%s:%d", address, s.Service.Port)
		adds = append(adds, Address)
	}
	return adds
}

func HadDebugSrvBySrvName(srvName string) bool {
	return HadSrvByNameTag(srvName, dict.SysWordGrayDebug)
}


// HadGraySrvBySrvName 获取灰度服务信息 频繁调用的地方这个接口慎用
func HadGraySrvBySrvName(srvName string) bool {
	return HadSrvByNameTag(srvName, dict.SysWordGrayGray)
}

// HadSrvByNameTag 是否存在指定标签的服务 频繁调用的地方这个接口慎用
func HadSrvByNameTag(srvName, tag string) bool {
	defaultConfig := consulApi.DefaultConfig()
	defaultConfig.Address = viper.GetString(dict.ConfigConsulAddr)

	cli, _ := consulApi.NewClient(defaultConfig)
	s := cli.Health()

	ss, _, _ := s.Service( 
		srvName,
		"",
		true,
		&consulApi.QueryOptions{},
	)

	hadSrv := false
	for _, s := range ss {
		service := s.Service
		for _, tagNow := range service.Tags {
			if tagNow == tag {
				hadSrv = true
				break
			}
		}
	}
	return hadSrv
}
