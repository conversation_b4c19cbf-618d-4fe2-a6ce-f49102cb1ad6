package srv_mgr

import (
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
	"testing"
)

func init() {
	viper.Set(dict.ConfigConsulAddr, "************:8500")
}

func TestGetAllSrvIpByName(t *testing.T) {
	srvArray := GetAllTagSrvIpByName("webapi", "")
	t.Log(srvArray)
}

func TestGetTagsBySrvName(t *testing.T) {
	t.Log(HadDebugSrvBySrvName("webapi"))
	t.Log(HadDebugSrvBySrvName("login"))
}
