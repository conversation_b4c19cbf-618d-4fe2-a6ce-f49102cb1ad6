// Package dlm redis timer redis定时器
package dlm

import (
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/rds"
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"
	"github.com/go-redis/redis/v8"
	"strconv"
	"time"

	"github.com/sirupsen/logrus"
)

var lockMgr *RedisLockMgr

func init() {
	lockMgr = &RedisLockMgr{}
	lockMgr.SetIsDebug(false)
}

const (
	// exExpireTime 定时器过期时间=定时器最迟时间+exExpireTime
	exExpireTime = time.Hour * 24
)

// TimeOutFunc 到期处理函数模板
type TimeOutFunc func(body []byte)

// NewRdsTimer 新建
func NewRdsTimer(key string, limit int, count int, onTimeOut TimeOutFunc) *RdsTimer {
	return &RdsTimer{
		SetKey:    key,
		Limit:     limit,
		Count:     count,
		OnTimeOut: onTimeOut,
	}
}

// RdsTimer redis简易定时器，通过有序集合
type RdsTimer struct {
	OnTimeOut TimeOutFunc // 超时回调
	SetKey    string      // 有序集合key
	Limit     int         // 长度限制
	Count     int         // 单次处理的定时器最大数量
}

// RdsTimerData 定时器数据（包括时间）
type RdsTimerData struct {
	TimerData []byte
	TimeStamp int64
}

// 由于读取之后要remove，需要加分布式锁
func (t *RdsTimer) getLockKey() string {
	return fmt.Sprintf("%s:lock", t.SetKey)
}

// Add 新增定时器，@expireTime到期时间
func (t *RdsTimer) Add(expireTime time.Time, body []byte) error {
	redisCli := redisfactory.GetRedisClient(rds.PLAYER, 0)
	entry := logrus.WithFields(logrus.Fields{
		"expireTime": expireTime,
		"setkey":     t.SetKey,
		"limit":      t.Limit,
		"body":       string(body)})

	card, err := redisCli.ZCard(ctx, t.SetKey).Result()
	if err != nil {
		entry.WithError(err).Errorln("获取基数失败")
	}
	if card >= int64(t.Limit) {
		errMaxLimit := fmt.Errorf("现集合元素%d，大于限制数量%d", card, t.Limit)
		entry.WithError(errMaxLimit).Errorln("定时器已满")
		return errMaxLimit
	}

	if errDone := redisCli.ZAdd(ctx, t.SetKey, &redis.Z{
		Score:  float64(expireTime.Unix()),
		Member: body,
	}).Err(); errDone != nil {
		entry.WithError(errDone).Errorln("新增定时器失败")
		return errDone
	}

	entry.Debugln("添加定时任务成功")
	return nil
}

// GetClosingTask 获得临近的任务
func (t *RdsTimer) GetClosingTask(comingTime time.Time) (tasks []RdsTimerData) {
	timeStamp := comingTime.Unix()
	rCli := redisfactory.GetRedisClient(rds.PLAYER, 0)
	rangeByScores := rCli.ZRangeByScoreWithScores(ctx, t.SetKey,
		&redis.ZRangeBy{
			Min:   "-inf",
			Max:   strconv.FormatFloat(float64(timeStamp), 'f', 2, 64),
			Count: int64(t.Count),
		})

	var err error
	entry := logrus.WithFields(logrus.Fields{"setkey": t.SetKey, "rangeByScores": rangeByScores})
	if err = rangeByScores.Err(); err != nil && err != redis.Nil {
		entry.WithError(err).Errorln("派发定时任务失败，无法获取到期任务")
		return
	}

	if err == redis.Nil {
		return
	}

	rangeZ := rangeByScores.Val()
	for _, z := range rangeZ {
		score := int64(z.Score)
		task := RdsTimerData{
			TimerData: []byte(z.Member.(string)),
			TimeStamp: score,
		}
		tasks = append(tasks, task)
	}

	return
}

// Dispatch 派发到期定时任务
func (t *RdsTimer) Dispatch() {
	now := time.Now().Unix()
	redisCli := redisfactory.GetRedisClient(rds.PLAYER, 0)
	unLock := lockMgr.LockKey(t.getLockKey())

	rangeByScores := redisCli.ZRangeByScore(ctx, t.SetKey, &redis.ZRangeBy{
		Min:   "-inf",
		Max:   strconv.FormatFloat(float64(now), 'f', 2, 64),
		Count: int64(t.Count),
	})

	var err error
	entry := logrus.WithFields(logrus.Fields{
		"setKey":        t.SetKey,
		"rangeByScores": rangeByScores})

	if err = rangeByScores.Err(); err != nil && err != redis.Nil {
		entry.WithError(err).Errorln("派发定时任务失败，无法获取到期任务")
		unLock()
		return
	}

	if err == redis.Nil {
		// entry.Debugln("空列表，返回")
		unLock()
		return
	}

	values := rangeByScores.Val()
	entry = entry.WithField("vals", values)
	if len(values) == 0 {
		unLock()
		// entry.Debugln("没有值")
		return
	}

	var iValArray []interface{}
	for _, val := range values {
		iValArray = append(iValArray, []byte(val))
	}

	if errZRem := redisCli.ZRem(ctx, t.SetKey, iValArray...).Err(); errZRem != nil {
		entry.WithError(errZRem).Errorln("从定时器删除已完成定时任务失败")
		unLock()
		return
	}
	unLock()

	entry.Debugf("处理定时任务：%+v", values)
	for _, val := range values {
		t.OnTimeOut([]byte(val))
	}
}
