package dlm

import (
	"context"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"
	"github.com/go-redis/redis/v8"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
)

const (
	// 抢锁失败等待时间，单位毫秒
	grabLockWaitTime = 2

	// grabLockWarnTime 等待锁时间达到了这个阀值，才会触发统计
	grabLockWarnTime = 100

	// 和 GetLockCli()保持统一
	ConfigRedisLockDb = "lock"

	ConfigRedisLockIndex = 4
)

var (
	DefaultLockMgr *RedisLockMgr

	lockWaitTime = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "redis_lock_wait_time",
			Help: "分布式锁等待时间累计（超过grabLockWarnTime才会触发统计），单位毫秒",
		},
		[]string{"key"},
	)

	lockTimes = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "redis_lock_times",
			Help: "分布式锁加锁解锁次数",
		},
		[]string{"key", "op"},
	)
)

var ctx = context.Background()

func init() {
	DefaultLockMgr = &RedisLockMgr{}
	DefaultLockMgr.isDebug = true

	prometheus.MustRegister(lockWaitTime, lockTimes)
}

type RedisLockMgr struct {
	isDebug bool
}

func (mgr *RedisLockMgr) SetIsDebug(flag bool) {
	mgr.isDebug = flag
}

// OptimisticLock 乐观锁
func (mgr *RedisLockMgr) OptimisticLock(rdsCli *redis.Client,
	key string, lockVal int64, expireSeconds int) bool {

	cmd := rdsCli.SetNX(ctx, key, lockVal, time.Duration(expireSeconds)*time.Second)
	if cmd.Err() != nil {
		logrus.Errorf("optimisticLock failed, key=%s val=%v err=%v", key, lockVal, cmd.Err())
		return false
		// panic(cmd.Err())
	}
	return cmd.Val()
}

// DistributeLock 分布式加锁
// typ: 对象类型 id: 对象 id  lock: 锁 id
// expire: 可选参数，设定锁自动释放的时间（单位：秒），默认1分钟
func (mgr *RedisLockMgr) DistributeLock(rdsCli *redis.Client,
	key string, lockVal int64, expireSeconds int) {

	start := time.Now()
	waitTime := 0
	for {
		cmd := rdsCli.SetNX(ctx, key, lockVal, time.Duration(expireSeconds)*time.Second)
		if cmd.Err() != nil {
			panic(cmd.Err())
		}

		if cmd.Val() {
			if duration := time.Since(start); duration > 10*time.Second {
				logrus.Warnf("抢锁时间[%.2f]超过 10s", duration.Seconds())
			}

			return
		}

		// logrus.Debugf("加锁失败，2ms 后重试，id: %d", id)
		time.Sleep(time.Millisecond * grabLockWaitTime)
		waitTime += grabLockWaitTime
		if waitTime > grabLockWarnTime {
			waitTime = 0
			lockWaitTime.WithLabelValues(key).Add(float64(waitTime))
		}
	}
}

// DistributeUnlock 分布式解锁
func (mgr *RedisLockMgr) DistributeUnlock(rdsCli *redis.Client, key string, lockVal int64) {

	script := "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end"
	cmd := rdsCli.Eval(ctx, script, []string{key}, lockVal)

	if cmd.Err() != nil {
		logrus.Warnf("DistributeUnlock failed, key:%s, err:%v", key, cmd.Err())
		return
		// panic(cmd.Err())
	}

	// if mgr.isDebug {
	//	logrus.Debugf("解锁成功: %s=%d", key, lockVal)
	// }
}

func (mgr *RedisLockMgr) LockKey(key string, expire ...int) func() {
	rdsCli := redisfactory.GetRedisClient(ConfigRedisLockDb, ConfigRedisLockIndex)
	lock := time.Now().UnixNano()
	expireSeconds := 60
	if len(expire) != 0 {
		expireSeconds = expire[0]
	}
	lockTimes.WithLabelValues(key, "lock").Inc()
	mgr.DistributeLock(rdsCli, key, lock, expireSeconds)
	return func() {
		mgr.DistributeUnlock(rdsCli, key, lock)
		lockTimes.WithLabelValues(key, "unlock").Inc()
	}
}

// IsOptimisticLock 是否被乐观锁锁定
func (mgr *RedisLockMgr) IsOptimisticLock(key string) bool {
	rdsCli := redisfactory.GetRedisClient(ConfigRedisLockDb, ConfigRedisLockIndex)
	sz, err := rdsCli.Exists(ctx, key).Result()
	if err != nil {
		logrus.Warnf("IsOptimisticLock failed, key=%s, err=%v", key, err)
		// panic(err)
		// 若产生错误默认已锁定
		return true
	}
	return sz == 1
}

// OptimisticLockKey 乐观锁
func (mgr *RedisLockMgr) OptimisticLockKey(key string,
	expire ...int) (getLock bool, unLock func()) {

	redisCli := redisfactory.GetRedisClient(ConfigRedisLockDb, ConfigRedisLockIndex)
	lock := time.Now().UnixNano()
	expireSeconds := 60
	if len(expire) != 0 {
		expireSeconds = expire[0]
	}

	getLock = mgr.OptimisticLock(redisCli, key, lock, expireSeconds)
	return getLock, func() {
		mgr.DistributeUnlock(redisCli, key, lock)
	}
}

// LockKey 锁定key
func LockKey(key string, expire ...int) func() {
	return DefaultLockMgr.LockKey(key, expire...)
}

// IncrExist 只有key存在的时候才增一
func IncrExist(rdsCli *redis.Client, key string) (size int, err error) {
	script := "if redis.call('exists', KEYS[1]) == 1 then return redis.call('incr', KEYS[1]) else return 0 end"
	size, err = rdsCli.Eval(ctx, script, []string{key}).Int()
	return
}
