package confd

import (
	"git.keepfancy.xyz/back-end/frameworks/kit/ginx"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"github.com/sirupsen/logrus"
	"strings"
	"sync"
	"testing"
	"time"
)

var s *Config

const (
	prefix     = "fancy/fisher"
	consulAddr = "192.168.1.58:8500"
)

func init() {
	var err error
	s = NewConfig(WithPrefix(prefix), <PERSON><PERSON><PERSON><PERSON>(consulAddr))
	err = s.Init()
	if err != nil {
		logrus.Fatal(err)
	}
}

// test put json
func TestPut1(t *testing.T) {
	a := &struct {
		Put string `json:"put"`
	}{"put1"}

	err := s.Put("test1", a)
	if err != nil {
		t.Error(err)
		return
	}
}

// test put value
func TestPut2(t *testing.T) {
	err := s.Put("test2", "put2")
	if err != nil {
		t.Error(err)
		return
	}
}

func TestGet(t *testing.T) {
	ret := s.Get("goods")
	if ret.Err() != nil {
		t.Error(ret.Err())
		return
	}
	t.Log(ret)
}

func TestList(t *testing.T) {
	ls, err := s.list()
	if err != nil {
		t.Error(err)
		return
	}

	for _, v := range ls {
		t.Log(v)
	}
}

func TestGetKey(t *testing.T) {
	ret := s.Get("excel/temp/dummy")
	if ret.Err() != nil {
		t.Error(ret.Err())
		return
	}
}

func TestCopyFold(t *testing.T) {
	ls, err := s.listDir("fancy/excel/temp/")
	if err != nil {
		t.Error(err)
		return
	}

	timeNowStr := time.Now().Format(time.DateTime)

	for _, v := range ls {
		t.Log("modify row : ", v)
		ss := strings.Split(v, "/")
		keyStr := ss[len(ss)-1]
		newPathPrefix := ss[1] + "/" + timeNowStr + "/" + keyStr
		keyPathPrefix := ss[1] + "/temp" + "/" + keyStr
		kv := s.Get(keyPathPrefix)
		s.PutJson(newPathPrefix, kv.value)
	}
}

func TestDelete(t *testing.T) {
	err := s.Delete("excel/v1/dummy")
	if err != nil {
		t.Error(err)
		return
	}
}

func TestDeleteTree(t *testing.T) {
	err := s.DeleteTree("kvTest/excel/v1/", nil)
	if err != nil {
		t.Error(err)
		return
	}
}

func TestWatch(t *testing.T) {
	r := ginx.GetGinEngine()
	safego.Go(func() {
		key := "goods"
		// ret := s.Get()
		cfgGoods := ""
		s.Watch(key, func(kv *KV) {
			t.Logf("监听到配置%s变化后：%v", kv.key, kv.String())
			cfgGoods = kv.String()
		})
		t.Log(cfgGoods)
	})
	r.Run(":8808")
}

func Benchmark10000(b *testing.B) {
	wg := sync.WaitGroup{}
	for i := 0; i < b.N; i++ {
		wg.Add(1)
		safego.Go(func() {
			key := "goods"
			// ret := s.Get()
			cfgGoods := ""
			s.Watch(key, func(kv *KV) {
				b.Logf("监听到配置%s变化后：%v", kv.key, kv.String())
				cfgGoods = kv.String()
			})
			b.Log(cfgGoods)
		})
	}
}
