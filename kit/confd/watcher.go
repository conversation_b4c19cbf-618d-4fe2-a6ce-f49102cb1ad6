package confd

import (
	"bytes"
	"fmt"
	"strings"
	"sync"

	"github.com/hashicorp/consul/api"
	"github.com/hashicorp/consul/api/watch"
	"github.com/sirupsen/logrus"
)

type watcher struct {
	sync.RWMutex // 基于读写锁实现并发控制
	*watch.Plan
	lastValues    map[string][]byte
	hybridHandler watch.HybridHandlerFunc // 当对应的key发生变化时，调用对应的处理函数
	stopChan      chan struct{}
	err           chan error
}

type IWatcher func(path string) (*watcher, error)

var watchFactory = map[string]IWatcher{
	"":          newKeyWatcher, // 默认值
	"key":       newKeyWatcher,
	"keyprefix": newPrefixWatcher,
}

// 初始化对应的watcher ，这里设置的是监听路径的类型，也可以支持service、node等，通过更改type
// 支持的type类型有
// key - Watch a specific KV pair
// keyprefix - Watch a prefix in the KV store
// services - Watch the list of available services
// nodes - Watch the list of nodes
// service- Watch the instances of a service
// checks - Watch the value of health checks
// event - Watch for custom user events
func newPrefixWatcher(path string) (*watcher, error) {
	wp, err := watch.Parse(map[string]interface{}{"type": "keyprefix", "prefix": path})
	if err != nil {
		return nil, err
	}

	return &watcher{
		Plan:       wp,
		lastValues: make(map[string][]byte),
		err:        make(chan error, 1),
	}, nil
}

// newKeyWather 创建全匹配的wather
func newKeyWatcher(path string) (*watcher, error) {
	wp, err := watch.Parse(map[string]interface{}{"type": "key", "key": path})
	if err != nil {
		return nil, err
	}

	return &watcher{
		Plan:       wp,
		lastValues: make(map[string][]byte),
		err:        make(chan error, 1),
	}, nil
}

func newServiceWatcher(serviceName string) (*watcher, error) {
	wp, err := watch.Parse(map[string]interface{}{"type": "service", "service": serviceName})
	if err != nil {
		return nil, err
	}
	return &watcher{
		Plan:       wp,
		lastValues: make(map[string][]byte),
		err:        make(chan error, 1),
	}, nil
}

// 获取value
func (w *watcher) getValue(path string) []byte {
	w.RLock()
	defer w.RUnlock()

	return w.lastValues[path]
}

// 更新value
func (w *watcher) updateValue(path string, value []byte) {
	w.Lock()
	defer w.Unlock()

	if len(value) == 0 {
		delete(w.lastValues, path)
	} else {
		w.lastValues[path] = value
	}
}

// 用于设置对应的处理函数
func (w *watcher) setHybridHandler(prefix string, watchKey string, handler func(*KV)) {
	switch w.Type {
	case "key":
		w.setKeyHybridHandler(prefix, watchKey, handler)
	case "keyprefix":
		w.setPrefixKeyHybridHandler(prefix, watchKey, handler)
	default:
		logrus.Errorf("unkown watcher type:%s", w.Type)
		return
	}
}

func (w *watcher) setPrefixKeyHybridHandler(prefix string, watchKey string, handler func(*KV)) {
	w.hybridHandler = func(bp watch.BlockingParamVal, data interface{}) {
		kvPairs := data.(api.KVPairs)
		ret := &KV{}

		for _, k := range kvPairs {
			path := strings.TrimSuffix(strings.TrimPrefix(k.Key, prefix+"/"), "/")
			v := w.getValue(path)

			if len(k.Value) == 0 && len(v) == 0 {
				continue
			}

			if bytes.Equal(k.Value, v) {
				continue
			}

			// 非监听内容不处理
			if path != watchKey {
				continue
			}

			ret.value = k.Value
			ret.key = path
			w.updateValue(path, k.Value)
			handler(ret)
		}
	}
}

func (w *watcher) setKeyHybridHandler(prefix string, _ string, handler func(*KV)) {
	w.hybridHandler = func(bp watch.BlockingParamVal, data interface{}) {
		k := data.(*api.KVPair)
		ret := &KV{}

		path := strings.TrimSuffix(strings.TrimPrefix(k.Key, prefix+"/"), "/")
		v := w.getValue(path)

		if len(k.Value) == 0 && len(v) == 0 {
			return
		}

		if bytes.Equal(k.Value, v) {
			return
		}

		ret.value = k.Value
		ret.key = path
		w.updateValue(path, k.Value)
		handler(ret)
	}
}

func (w *watcher) run(address string, conf *api.Config) error {
	w.Lock()
	if w.stopChan == nil {
		w.stopChan = make(chan struct{})
	}
	if w.err == nil {
		w.err = make(chan error, 1)
	}
	w.Unlock()

	w.Plan.HybridHandler = w.hybridHandler

	done := make(chan struct{})
	defer close(done)

	go func() {
		select {
		case w.err <- w.RunWithConfig(address, conf):
		case <-done:
		}
	}()

	select {
	case err := <-w.err:
		if err != nil {
			return fmt.Errorf("run fail: %w", err)
		}
		return nil
	case <-w.stopChan:
		w.Stop()
		return nil
	}
}

func (w *watcher) stop() {
	close(w.stopChan)
}
