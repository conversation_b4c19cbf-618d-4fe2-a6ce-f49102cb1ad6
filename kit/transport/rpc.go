package transport

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"time"

	grpc "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
	"git.keepfancy.xyz/back-end/frameworks/lib/custom_json"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"google.golang.org/protobuf/proto"
)

var (
	SysWordMsg          = "msg"      // Word msg
	SysWordMsgID        = "msgID"    // Word msgID
	SysWordPlayerID     = "playerID" // Word playerID
	SysWordProtocolJson = "json"     // Word json 协议格式
)

// IntranetServer 内部RPC服务
type IntranetServer struct {
	localAddr string // intranet地址
}

// HandleClientMessage 实现gate_rpc客户端消息处理接口
func (s IntranetServer) HandleClientMessage(ctx context.Context,
	cMsg *intranetGrpc.ClientMessage) (*intranetGrpc.HandleResult, error) {
	defer logdog.RecoverAndLog()

	var (
		result      = &intranetGrpc.HandleResult{}
		header      = cMsg.GetHeader()
		msgID       = header.GetMsgId()
		playerID    = cMsg.GetPlayerId()
		callResults []reflect.Value
		handler     = getHandler(int(msgID))
		entry       = logrus.WithFields(logrus.Fields{
			SysWordMsg:      GetMsgName(int(msgID)),
			SysWordPlayerID: playerID,
		})
		err error
	)

	if handler == nil {
		entry.Warnf("Message handlers are not registered")
		return result, errors.New("message handlers are not registered")
	}

	if f := handler.options.routerFunc; f != nil {
		address := f(playerID)
		localAddr := s.localAddr

		if len(localAddr) == 0 {
			localAddr = viper.GetString("rpc_addr") + ":" + viper.GetString("rpc_port")
		}

		if len(address) != 0 && address != localAddr {
			cc, err := grpc.GetConnectionByAddr(address)
			if err != nil {
				entry.WithError(err).Errorf("连接 %s 失败，改为调用本地处理器", address)
			} else {
				entry.Debugf("路由此消息，目标地址: %s", address)
				return intranetGrpc.NewMessageHandlerClient(cc).HandleClientMessage(ctx, cMsg)
			}
		} else {
			logrus.Debugf("本地处理此消息，目标地址: %s", address)
		}
	}

	begin := time.Now()
	defer handleClientMessageSeconds.WithLabelValues(
		GetMsgName(int(msgID))).Observe(time.Now().Sub(begin).Seconds() * 1000)

	f := reflect.ValueOf(handler.f)
	if handler.bodyType == byteSliceType {
		callResults = f.Call([]reflect.Value{
			reflect.ValueOf(playerID),
			reflect.ValueOf(header),
			reflect.ValueOf(cMsg.GetRequestData()),
		})
	} else {
		bodyMsg := reflect.New(handler.bodyType).Interface()

		switch cMsg.GetHeader().Protocols {
		case SysWordProtocolJson:
			err = json.Unmarshal(cMsg.GetRequestData(), bodyMsg.(proto.Message))
			break
		default:
			err = proto.Unmarshal(cMsg.GetRequestData(), bodyMsg.(proto.Message))
		}

		if err != nil {
			entry.WithError(err).Errorf("反序列化失败")
			return result, errors.New("反序列化失败")
		}

		callResults = f.Call([]reflect.Value{
			reflect.ValueOf(ctx),
			reflect.ValueOf(header),
			reflect.ValueOf(bodyMsg),
		})
	}

	if len(callResults) == 0 || callResults[0].IsNil() {
		return result, nil
	}

	switch v := callResults[0].Interface().(type) {
	case *ResponseMsg:
		switch v.Protocols {
		case SysWordProtocolJson:
			result.Responses = appendJsonResponse(result.Responses, *v)
		default:
			result.Responses = appendResponse(result.Responses, *v)
		}

	case []ResponseMsg:
		for _, rsp := range v {
			switch rsp.Protocols {
			case SysWordProtocolJson:
				result.Responses = appendJsonResponse(result.Responses, v...)
			default:
				result.Responses = appendResponse(result.Responses, v...)
			}
		}

	default:
		logrus.Panicf("不正确的返回值类型: %#v", v)
	}

	return result, nil
}

func appendJsonResponse(sli []*intranetGrpc.ResponseMessage, vs ...ResponseMsg) []*intranetGrpc.ResponseMessage {

	for _, v := range vs {
		body, err := custom_json.Marshal(v.Body)
		if err != nil {
			logrus.WithError(err).Errorf("json.Marshal 失败, msg: %s body: %#v", GetMsgName(int(v.MsgID)), v.Body)
			return sli
		}

		sli = append(sli, &intranetGrpc.ResponseMessage{
			Header: &intranetGrpc.Header{
				MsgId: v.MsgID,
			},
			Body: body,
		})
	}
	return sli
}

func appendResponse(sli []*intranetGrpc.ResponseMessage, vs ...ResponseMsg) []*intranetGrpc.ResponseMessage {
	for _, v := range vs {
		body, err := proto.Marshal(v.Body)
		if err != nil {
			logrus.WithError(err).Errorf("marshal 失败, msg: %s body: %#v", GetMsgName(int(v.MsgID)), v.Body)
			return sli
		}

		sli = append(sli, &intranetGrpc.ResponseMessage{
			Header: &intranetGrpc.Header{
				MsgId: v.MsgID,
			},
			Body: body,
		})
	}
	return sli
}

// InitIntranetGrpcServer 初始化网关g
func InitIntranetGrpcServer() {
	intranetGrpc.RegisterMessageHandlerServer(grpc.Server, &IntranetServer{})
}
