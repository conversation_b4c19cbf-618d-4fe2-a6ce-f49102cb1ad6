// Package transport
package transport

import (
	"context"
	"fmt"
	"reflect"
	"sync"

	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"

	"google.golang.org/protobuf/proto"
)

// Handler 注册消息处理函数，注册失败触发 panic
// handler 形参: (playerID uint64, header *intranet_grpc.Header, body yourProtobufMsg)
// 或者 (playerID uint64, header *intranet_grpc.Header, body []byte)
// 返回值必须为 ResponseMsg 或者 []ResponseMsg
// 下面四种消息处理器的声明是合法的:
//  1. func(playerID uint64, header *intranet_grpc.Header, body yourProtobufMsg) *ResponseMsg
//  2. func(playerID uint64, header *intranet_grpc.Header, body yourProtobufMsg) []ResponseMsg
//  3. func(playerID uint64, header *intranet_grpc.Header, body []byte) *ResponseMsg
//  4. func(playerID uint64, header *intranet_grpc.Header, body []byte) []ResponseMsg
func Handler(msgID int, handler interface{}, opts ...HandleOption) {
	var options options
	for _, opt := range opts {
		opt(&options)
	}

	handlerType := reflect.TypeOf(handler)
	v := handlerFunc{
		f:       handler,
		options: options,
	}

	if handlerType.Kind() != reflect.Func {
		panic("注册消息处理对象必须是函数类型")
	}

	if handlerType.NumOut() != 1 && handlerType.NumIn() != 3 {
		panic("消息处理器返回值数量必须为1且参数数量必须为3")
	}

	v.returnType = handlerType.Out(0)
	if v.returnType != reflect.TypeOf([]ResponseMsg{}) && v.returnType != reflect.TypeOf(&ResponseMsg{}) {
		panic("返回值必须为 *ResponseMsg 或者 []ResponseMsg")
	}

	const sErrorTips = "消息处理器的参数必须为 (ctx context.context, header *intranet_grpc.Header, body *yourProtobufMsg) " + "或者 (context.context uint64, header *intranet_grpc.Header, body []byte)"
	if handlerType.In(0) != reflect.TypeOf((*context.Context)(nil)).Elem(){
		panic(sErrorTips)
	}

	if handlerType.In(1) != reflect.TypeOf(&intranetGrpc.Header{}) {
		panic(sErrorTips)
	}

	v.bodyType = handlerType.In(2)
	if v.bodyType != byteSliceType {
		if v.bodyType.Kind() != reflect.Ptr {
			panic(sErrorTips)
		}
		if _, ok := reflect.New(v.bodyType).Elem().Interface().(proto.Message); !ok {
			panic(sErrorTips)
		} else {
			// 缓存值类型
			v.bodyType = v.bodyType.Elem()
		}
	}
	_, loaded := handlerMap.LoadOrStore(msgID, v)
	if loaded {
		panic("消息重复注册")
	}
	// logrus.Infof("消息 %s 注册成功", GetMsgName(msgID))
}

type handlerFunc struct {
	f          interface{}  // 消息处理函数
	bodyType   reflect.Type // 消息体的实际类型
	returnType reflect.Type // 返回值的类型
	options    options
}

// ResponseMsg 消息处理器的回复消息
type ResponseMsg struct {
	MsgID     uint32
	Protocols string
	Body      proto.Message
}

var handlerMap sync.Map

// options 注册消息处理器的选项表
type options struct {
	routerFunc RouterFunc
}

// RouterFunc 路由函数，返回目标服务地址
type RouterFunc func(playerID uint64) string

// HandleOption 消息处理器注册选项
type HandleOption func(*options)

// WithRouterFunc 返回附带路由函数的选项
func WithRouterFunc(f RouterFunc) HandleOption {
	return func(opt *options) {
		opt.routerFunc = f
	}
}

// GetMsgName 获取消息名称，可以被外部修改
var GetMsgName = func(id int) string {
	return fmt.Sprintf("%d", id)
}

func getHandler(msgID int) *handlerFunc {
	handler, ok := handlerMap.Load(msgID)
	if !ok {
		return nil
	}
	v := handler.(handlerFunc)
	return &v
}
