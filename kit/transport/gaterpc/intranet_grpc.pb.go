// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: intranet_grpc.proto

package intranet_grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId     uint32 `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`         // msg_id 消息ID
	ClientIp  string `protobuf:"bytes,2,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"` // 客户端 ip
	Protocols string `protobuf:"bytes,3,opt,name=protocols,proto3" json:"protocols,omitempty"`               // 协议格式 json|pb
}

func (x *Header) Reset() {
	*x = Header{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{0}
}

func (x *Header) GetMsgId() uint32 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *Header) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *Header) GetProtocols() string {
	if x != nil {
		return x.Protocols
	}
	return ""
}

// 客户端消息
type ClientMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId    uint64  `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`         // 玩家 ID
	Header      *Header `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`                              // 消息头
	RequestData []byte  `protobuf:"bytes,3,opt,name=request_data,json=requestData,proto3" json:"request_data,omitempty"` // 消息内容
}

func (x *ClientMessage) Reset() {
	*x = ClientMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientMessage) ProtoMessage() {}

func (x *ClientMessage) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientMessage.ProtoReflect.Descriptor instead.
func (*ClientMessage) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{1}
}

func (x *ClientMessage) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *ClientMessage) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ClientMessage) GetRequestData() []byte {
	if x != nil {
		return x.RequestData
	}
	return nil
}

// ResponseMessage 回复消息
type ResponseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"` // 消息头
	Body   []byte  `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`     // 消息体
}

func (x *ResponseMessage) Reset() {
	*x = ResponseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseMessage) ProtoMessage() {}

func (x *ResponseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseMessage.ProtoReflect.Descriptor instead.
func (*ResponseMessage) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{2}
}

func (x *ResponseMessage) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ResponseMessage) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

// 处理结果
type HandleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Responses []*ResponseMessage `protobuf:"bytes,1,rep,name=responses,proto3" json:"responses,omitempty"` // 回复数据， 长度为空时则不需要回复
}

func (x *HandleResult) Reset() {
	*x = HandleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleResult) ProtoMessage() {}

func (x *HandleResult) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleResult.ProtoReflect.Descriptor instead.
func (*HandleResult) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{3}
}

func (x *HandleResult) GetResponses() []*ResponseMessage {
	if x != nil {
		return x.Responses
	}
	return nil
}

// 消息发送请求
type SendMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId []uint64 `protobuf:"varint,1,rep,packed,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家 ID 列表
	Header   *Header  `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`                             // 消息头
	Data     []byte   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                                 // 消息内容
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{4}
}

func (x *SendMessageRequest) GetPlayerId() []uint64 {
	if x != nil {
		return x.PlayerId
	}
	return nil
}

func (x *SendMessageRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SendMessageRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// 消息发送结果
type SendMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ok bool `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"` // 是否发送成功
}

func (x *SendMessageResult) Reset() {
	*x = SendMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResult) ProtoMessage() {}

func (x *SendMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResult.ProtoReflect.Descriptor instead.
func (*SendMessageResult) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{5}
}

func (x *SendMessageResult) GetOk() bool {
	if x != nil {
		return x.Ok
	}
	return false
}

// 广播消息
type BroadcastMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`                                 // 消息头
	Data        []byte  `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`                                     // 消息内容
	NotRpcOther bool    `protobuf:"varint,3,opt,name=not_rpc_other,json=notRpcOther,proto3" json:"not_rpc_other,omitempty"` // 不需要rpc调用其他网关广播
}

func (x *BroadcastMsgRequest) Reset() {
	*x = BroadcastMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BroadcastMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastMsgRequest) ProtoMessage() {}

func (x *BroadcastMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastMsgRequest.ProtoReflect.Descriptor instead.
func (*BroadcastMsgRequest) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{6}
}

func (x *BroadcastMsgRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *BroadcastMsgRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BroadcastMsgRequest) GetNotRpcOther() bool {
	if x != nil {
		return x.NotRpcOther
	}
	return false
}

type BroadcastMsgResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ok bool `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"` // 是否发送成功
}

func (x *BroadcastMsgResult) Reset() {
	*x = BroadcastMsgResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_intranet_grpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BroadcastMsgResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastMsgResult) ProtoMessage() {}

func (x *BroadcastMsgResult) ProtoReflect() protoreflect.Message {
	mi := &file_intranet_grpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastMsgResult.ProtoReflect.Descriptor instead.
func (*BroadcastMsgResult) Descriptor() ([]byte, []int) {
	return file_intranet_grpc_proto_rawDescGZIP(), []int{7}
}

func (x *BroadcastMsgResult) GetOk() bool {
	if x != nil {
		return x.Ok
	}
	return false
}

var File_intranet_grpc_proto protoreflect.FileDescriptor

var file_intranet_grpc_proto_rawDesc = []byte{
	0x0a, 0x13, 0x69, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x22, 0x5a, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x22, 0x7a, 0x0a, 0x0d,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x50, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0x48, 0x0a, 0x0c, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x73, 0x22, 0x70, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x23, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6b, 0x22, 0x78, 0x0a, 0x13, 0x42,
	0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x5f, 0x72, 0x70, 0x63, 0x5f, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x52, 0x70, 0x63,
	0x4f, 0x74, 0x68, 0x65, 0x72, 0x22, 0x24, 0x0a, 0x12, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6b, 0x32, 0x5c, 0x0a, 0x0e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x12, 0x4a, 0x0a,
	0x13, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x17,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x32, 0xb2, 0x01, 0x0a, 0x0d, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x0b, 0x53,
	0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x10, 0x42, 0x72, 0x6f,
	0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x12,
	0x5a, 0x10, 0x2e, 0x2f, 0x3b, 0x69, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x65, 0x74, 0x5f, 0x67, 0x72,
	0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_intranet_grpc_proto_rawDescOnce sync.Once
	file_intranet_grpc_proto_rawDescData = file_intranet_grpc_proto_rawDesc
)

func file_intranet_grpc_proto_rawDescGZIP() []byte {
	file_intranet_grpc_proto_rawDescOnce.Do(func() {
		file_intranet_grpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_intranet_grpc_proto_rawDescData)
	})
	return file_intranet_grpc_proto_rawDescData
}

var file_intranet_grpc_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_intranet_grpc_proto_goTypes = []interface{}{
	(*Header)(nil),              // 0: transport.Header
	(*ClientMessage)(nil),       // 1: transport.ClientMessage
	(*ResponseMessage)(nil),     // 2: transport.ResponseMessage
	(*HandleResult)(nil),        // 3: transport.HandleResult
	(*SendMessageRequest)(nil),  // 4: transport.SendMessageRequest
	(*SendMessageResult)(nil),   // 5: transport.SendMessageResult
	(*BroadcastMsgRequest)(nil), // 6: transport.BroadcastMsgRequest
	(*BroadcastMsgResult)(nil),  // 7: transport.BroadcastMsgResult
}
var file_intranet_grpc_proto_depIdxs = []int32{
	0, // 0: transport.ClientMessage.header:type_name -> transport.Header
	0, // 1: transport.ResponseMessage.header:type_name -> transport.Header
	2, // 2: transport.HandleResult.responses:type_name -> transport.ResponseMessage
	0, // 3: transport.SendMessageRequest.header:type_name -> transport.Header
	0, // 4: transport.BroadcastMsgRequest.header:type_name -> transport.Header
	1, // 5: transport.MessageHandler.HandleClientMessage:input_type -> transport.ClientMessage
	4, // 6: transport.MessageSender.SendMessage:input_type -> transport.SendMessageRequest
	6, // 7: transport.MessageSender.BroadcastMessage:input_type -> transport.BroadcastMsgRequest
	3, // 8: transport.MessageHandler.HandleClientMessage:output_type -> transport.HandleResult
	5, // 9: transport.MessageSender.SendMessage:output_type -> transport.SendMessageResult
	7, // 10: transport.MessageSender.BroadcastMessage:output_type -> transport.BroadcastMsgResult
	8, // [8:11] is the sub-list for method output_type
	5, // [5:8] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_intranet_grpc_proto_init() }
func file_intranet_grpc_proto_init() {
	if File_intranet_grpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_intranet_grpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_intranet_grpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_intranet_grpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResponseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_intranet_grpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_intranet_grpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_intranet_grpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_intranet_grpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BroadcastMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_intranet_grpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BroadcastMsgResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_intranet_grpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_intranet_grpc_proto_goTypes,
		DependencyIndexes: file_intranet_grpc_proto_depIdxs,
		MessageInfos:      file_intranet_grpc_proto_msgTypes,
	}.Build()
	File_intranet_grpc_proto = out.File
	file_intranet_grpc_proto_rawDesc = nil
	file_intranet_grpc_proto_goTypes = nil
	file_intranet_grpc_proto_depIdxs = nil
}
