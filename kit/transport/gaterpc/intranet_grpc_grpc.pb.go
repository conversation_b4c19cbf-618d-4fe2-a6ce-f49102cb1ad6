// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: intranet_grpc.proto

package intranet_grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessageHandler_HandleClientMessage_FullMethodName = "/transport.MessageHandler/HandleClientMessage"
)

// MessageHandlerClient is the client API for MessageHandler service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageHandlerClient interface {
	HandleClientMessage(ctx context.Context, in *ClientMessage, opts ...grpc.CallOption) (*HandleResult, error)
}

type messageHandlerClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageHandlerClient(cc grpc.ClientConnInterface) MessageHandlerClient {
	return &messageHandlerClient{cc}
}

func (c *messageHandlerClient) HandleClientMessage(ctx context.Context, in *ClientMessage, opts ...grpc.CallOption) (*HandleResult, error) {
	out := new(HandleResult)
	err := c.cc.Invoke(ctx, MessageHandler_HandleClientMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageHandlerServer is the server API for MessageHandler service.
// All implementations should embed UnimplementedMessageHandlerServer
// for forward compatibility
type MessageHandlerServer interface {
	HandleClientMessage(context.Context, *ClientMessage) (*HandleResult, error)
}

// UnimplementedMessageHandlerServer should be embedded to have forward compatible implementations.
type UnimplementedMessageHandlerServer struct {
}

func (UnimplementedMessageHandlerServer) HandleClientMessage(context.Context, *ClientMessage) (*HandleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleClientMessage not implemented")
}

// UnsafeMessageHandlerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageHandlerServer will
// result in compilation errors.
type UnsafeMessageHandlerServer interface {
	mustEmbedUnimplementedMessageHandlerServer()
}

func RegisterMessageHandlerServer(s grpc.ServiceRegistrar, srv MessageHandlerServer) {
	s.RegisterService(&MessageHandler_ServiceDesc, srv)
}

func _MessageHandler_HandleClientMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageHandlerServer).HandleClientMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageHandler_HandleClientMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageHandlerServer).HandleClientMessage(ctx, req.(*ClientMessage))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageHandler_ServiceDesc is the grpc.ServiceDesc for MessageHandler service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageHandler_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "transport.MessageHandler",
	HandlerType: (*MessageHandlerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleClientMessage",
			Handler:    _MessageHandler_HandleClientMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "intranet_grpc.proto",
}

const (
	MessageSender_SendMessage_FullMethodName      = "/transport.MessageSender/SendMessage"
	MessageSender_BroadcastMessage_FullMethodName = "/transport.MessageSender/BroadcastMessage"
)

// MessageSenderClient is the client API for MessageSender service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageSenderClient interface {
	SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResult, error)
	BroadcastMessage(ctx context.Context, in *BroadcastMsgRequest, opts ...grpc.CallOption) (*BroadcastMsgResult, error)
}

type messageSenderClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageSenderClient(cc grpc.ClientConnInterface) MessageSenderClient {
	return &messageSenderClient{cc}
}

func (c *messageSenderClient) SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResult, error) {
	out := new(SendMessageResult)
	err := c.cc.Invoke(ctx, MessageSender_SendMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageSenderClient) BroadcastMessage(ctx context.Context, in *BroadcastMsgRequest, opts ...grpc.CallOption) (*BroadcastMsgResult, error) {
	out := new(BroadcastMsgResult)
	err := c.cc.Invoke(ctx, MessageSender_BroadcastMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageSenderServer is the server API for MessageSender service.
// All implementations should embed UnimplementedMessageSenderServer
// for forward compatibility
type MessageSenderServer interface {
	SendMessage(context.Context, *SendMessageRequest) (*SendMessageResult, error)
	BroadcastMessage(context.Context, *BroadcastMsgRequest) (*BroadcastMsgResult, error)
}

// UnimplementedMessageSenderServer should be embedded to have forward compatible implementations.
type UnimplementedMessageSenderServer struct {
}

func (UnimplementedMessageSenderServer) SendMessage(context.Context, *SendMessageRequest) (*SendMessageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedMessageSenderServer) BroadcastMessage(context.Context, *BroadcastMsgRequest) (*BroadcastMsgResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BroadcastMessage not implemented")
}

// UnsafeMessageSenderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageSenderServer will
// result in compilation errors.
type UnsafeMessageSenderServer interface {
	mustEmbedUnimplementedMessageSenderServer()
}

func RegisterMessageSenderServer(s grpc.ServiceRegistrar, srv MessageSenderServer) {
	s.RegisterService(&MessageSender_ServiceDesc, srv)
}

func _MessageSender_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageSenderServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageSender_SendMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageSenderServer).SendMessage(ctx, req.(*SendMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageSender_BroadcastMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BroadcastMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageSenderServer).BroadcastMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageSender_BroadcastMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageSenderServer).BroadcastMessage(ctx, req.(*BroadcastMsgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageSender_ServiceDesc is the grpc.ServiceDesc for MessageSender service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageSender_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "transport.MessageSender",
	HandlerType: (*MessageSenderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendMessage",
			Handler:    _MessageSender_SendMessage_Handler,
		},
		{
			MethodName: "BroadcastMessage",
			Handler:    _MessageSender_BroadcastMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "intranet_grpc.proto",
}
