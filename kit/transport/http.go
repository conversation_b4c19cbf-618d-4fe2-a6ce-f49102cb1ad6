package transport

import (
	"encoding/json"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/sirupsen/logrus"
	"io"
	"net/http"
	"reflect"
)

type httpRequestBody struct {
	PlayerID uint64              `json:"playerID"`
	Header   intranetGrpc.Header `json:"header"`
	Body     json.RawMessage     `json:"body"`
}

func HandleHTTP(w http.ResponseWriter, r *http.Request) {
	defer logdog.RecoverAndLog()

	rData, err := io.ReadAll(r.Body)
	if err != nil {
		logrus.Warningf("read data failure: %s", err.Error())
		return
	}

	cMsg := httpRequestBody{}
	if err := json.Unmarshal(rData, &cMsg); err != nil {
		w.Write([]byte("request content error\n\n"))
		logrus.Warningf("deserialization failure\n\n: %s，data: %s", err.Error(), string(rData))
		return
	}

	handler := getHandler(int(cMsg.Header.MsgId))
	if handler == nil {
		w.Write([]byte("failed to recognize the message\n\n"))
		logrus.Warningf("message failure to recognize\n\n：%d", cMsg.Header.MsgId)
		return
	}

	f := reflect.ValueOf(handler.f)

	var callResults []reflect.Value
	if handler.bodyType == byteSliceType {
		callResults = f.Call([]reflect.Value{
			reflect.ValueOf(cMsg.PlayerID),
			reflect.ValueOf(cMsg.Header),
			reflect.ValueOf([]byte(cMsg.Body)),
		})
	} else {
		bodyMsg := reflect.New(handler.bodyType).Interface()
		if err := json.Unmarshal(cMsg.Body, bodyMsg); err != nil {
			w.Write([]byte("request content error\n\n"))
			logrus.Warningf("deserialization failure\n\n: %s", err.Error())
			return
		}
		callResults = f.Call([]reflect.Value{
			reflect.ValueOf(cMsg.PlayerID),
			reflect.ValueOf(&cMsg.Header),
			reflect.ValueOf(bodyMsg).Elem(),
		})
	}

	if len(callResults) == 0 || callResults[0].IsNil() {
		w.Write([]byte("processing completed, nothing returned\n\n"))
		logrus.Infof("http call success, not responce。req: %s ", string(rData))
		return
	}

	result := callResults[0].Interface()
	respBody, err := json.Marshal(result)
	if err != nil {
		logrus.Infof("failed return data serialization\n\n: %s", err.Error())
		w.Write([]byte("processing done, but something wrong with the returned data"))
		return
	}

	w.Write(respBody)
	logrus.Infof("http process success, req: %s  rsp: %s", string(rData), string(respBody))
}
