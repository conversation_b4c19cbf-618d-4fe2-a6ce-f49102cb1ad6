package transport

import (
	iGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
	"google.golang.org/protobuf/proto"
)

// AppendRsp 添加回包
func AppendRsp(old []ResponseMsg, header *iGrpc.Header, body proto.Message) (result []ResponseMsg) {
	result = append(old, ResponseMsg{
		MsgID:     header.GetMsgId(),
		Protocols: header.GetProtocols(),
		Body:      body,
	})

	return
}

func RspMsg(header *iGrpc.Header, body proto.Message) *ResponseMsg {
	return &ResponseMsg{
		MsgID:     header.GetMsgId(),
		Protocols: header.GetProtocols(),
		Body:      body,
	}
}
