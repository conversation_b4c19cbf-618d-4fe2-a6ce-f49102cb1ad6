package ginx

import (
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

var defaultGinInstance *gin.Engine

func GetGinEngine() *gin.Engine {
	if defaultGinInstance == nil {
		if viper.IsSet(dict.ConfigGinEnvMode) {
			gin.SetMode(viper.GetString(dict.ConfigGinEnvMode))
		} else {
			gin.SetMode(gin.ReleaseMode)
		}
		defaultGinInstance = gin.Default()
	}
	return defaultGinInstance
}
