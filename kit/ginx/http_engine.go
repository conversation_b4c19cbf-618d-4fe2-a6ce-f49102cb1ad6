package ginx

import (
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
	"net/http"
	"time"
)

var defaultHttpInstance *http.Server

func GetRawHttpEngine() *http.Server {
	if defaultHttpInstance == nil {
		defaultHttpInstance = &http.Server{
			Addr: "0.0.0.0:" + viper.GetString(dict.ConfigHttpPort),
			// Handler: nil, // 使用默认的 mux 区别于Gin
			Handler:      GetGinEngine(),                            // 使用默认的 mux
			ReadTimeout:  dict.SysDefaultHttpRTimeout * time.Second, // Set Read the request headers timeout 防止Slowloris攻击
			WriteTimeout: dict.SysDefaultHttpWTimeout * time.Second, // Set write the response body timeout
		}
	}
	return defaultHttpInstance
}
