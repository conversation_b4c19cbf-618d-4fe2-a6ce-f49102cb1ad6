package redisfactory

import (
	"context"
	"fmt"
	"os"
	"strings"
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/go-redis/redis/v8"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
)

var ctx = context.Background()

// RedisConf 表示 redis 配置
type RedisConf struct {
	Address string
	Passwd  string
}

// Factory redis 工厂
type Factory struct {
	configs  map[string]RedisConf
	clients  sync.Map
	initOnce sync.Once
}

// DefaultFactory 默认工厂对象
var DefaultFactory = Factory{
	configs: make(map[string]RedisConf, 16),
}

// GetRedisClient 返回 redis client
func GetRedisClient(name string, db int) *redis.Client {
	rCli, err := DefaultFactory.GetRedisClient(name, db)
	if err != nil {
		logrus.Errorf("Failed to get redis client, name: %s db: %d err: %v", name, db, err)
		//panic(fmt.Sprintf("Failed to get redis client, name: %s db: %d err: %v", name, db, err))
	}
	return rCli
}

// GetRedisConfig 获取 redis 配置
func (f *Factory) GetRedisConfig(name string) RedisConf {
	f.initOnce.Do(f.initConfigs)

	if conf, ok := f.configs[name]; ok {
		return conf
	}

	return RedisConf{
		Address: viper.GetString(name + "_redis_addr"),
		Passwd:  viper.GetString(name + "_redis_password"),
	}
}

// createClient 创建 redis.Client
func (f *Factory) createClient(conf *RedisConf, db int) (*redis.Client, error) {
	c := redis.NewClient(&redis.Options{
		Addr:       conf.Address,
		Password:   conf.Passwd,
		DB:         db,
		PoolSize:   200,
		MaxRetries: 1,
	})
	ping := c.Ping(ctx)
	if ping.Err() != nil {
		err := c.Close()
		if err != nil {
			return nil, err
		}
		logrus.Errorf("连接 redis 服务器失败:redis=%s,err=%v", conf.Address, ping.Err())
		return nil, fmt.Errorf("连接 redis 服务器失败:redis=%s,err=%v", conf.Address, ping.Err())
	}

	op := c.Options()
	logrus.Infof("Conn RedisSrv Success : ip=%s, PoolSize=%d, MaxRetries=%d, PoolTimeout=%.2fs,",
		conf.Address, op.PoolSize, op.MaxRetries, op.PoolTimeout.Seconds())

	return c, nil
}

// GetRedisClient 根据名字获取 redis 客户端
func (f *Factory) GetRedisClient(name string, db int) (*redis.Client, error) {
	if cli, ok := f.clients.Load(name); ok {

		c := cli.(*redis.Client)
		ping := c.Ping(ctx)
		if ping.Err() != nil {
			ping = c.Ping(ctx)
			if ping.Err() != nil {
				logrus.Errorf("连接 redis 服务器失败:redis=%s,err=%v", name, ping.Err())
				return nil, fmt.Errorf("连接 redis 服务器失败:redis=%s,err=%v", name, ping.Err())
			}
		}

		return c, nil
	}
	conf := f.GetRedisConfig(name)
	client, err := f.createClient(&conf, db)
	if err != nil {
		logrus.Errorf("创建 redis 连接失败:redis=%s, addr=%s, passwd=%s, err=%v", name, conf.Address, conf.Passwd, err)
		return nil, err
	}
	actual, loaded := f.clients.LoadOrStore(name, client)
	if loaded {
		err := client.Close()
		if err != nil {
			return nil, err
		}
	}
	return actual.(*redis.Client), nil
}

func (f *Factory) initConfigs() {
	configs := viper.GetStringMap("redis_list")

	// 删除默认 redis
	f.configs["default"] = RedisConf{
		Address: viper.GetString(dict.ConfigRedisAddr),
		Passwd:  viper.GetString(dict.ConfigRedisPwd),
	}

	for name, cf := range configs {
		conf := cast.ToStringMapString(cf)
		addr := cast.ToString(conf[dict.ConfigRedisDbAddr])
		passwd := cast.ToString(conf[dict.ConfigRedisDbPwd])

		f.configs[name] = RedisConf{
			Address: addr,
			Passwd:  passwd,
		}
	}

	for name, conf := range f.configs {
		if addr, ok := os.LookupEnv(strings.ToUpper(name + "_REDIS_ADDR")); ok {
			conf.Address = addr
		}
		if passwd, ok := os.LookupEnv(strings.ToUpper(name + "_REDIS_PASSWORD")); ok {
			conf.Passwd = passwd
		}
		f.configs[name] = conf
	}
	// logrus.Debugf("redis 配置加载完成: %#v", f.configs)
	logrus.Infoln("Redis 配置加载完成")
}
