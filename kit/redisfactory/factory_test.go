package redisfactory

import (
	"os"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

// TestGetDefaultRedis 获取默认 redis 未配置
func TestGetDefaultRedis(t *testing.T) {
	os.Setenv("DEFAULT_REDIS_PASSWORD", "foo.bar.baz")
	os.Setenv("DEFAULT_REDIS_ADDR", "default-redis:6379")
	conf := DefaultFactory.GetRedisConfig("default")
	if conf.Address != "default-redis:6379" || conf.Passwd != "foo.bar.baz" {
		t.Fatalf("unexpected conf: %v", conf)
	}
}

// TestGetRedisClient 测试获取 redis 连接
func TestGetRedisClient(t *testing.T) {
	conf := map[string]string{
		"addr":   "localhost:6379",
		"passwd": "",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		"test": conf,
	})

	client, err := DefaultFactory.GetRedisClient("test", 0)
	assert.Nil(t, err)
	assert.NotNil(t, client)
}
