package kafkax

import (
	"math/rand"
	"strconv"
	"time"

	"github.com/Shopify/sarama"
	"github.com/sirupsen/logrus"
)

type OnMessageCallback func(sarama.AsyncProducer)

// KafkaProducer async producer
type KafkaProducer struct {
	Producer sarama.AsyncProducer
	brokers  []string
}

func NewKafkaAsyncProducer(brokers []string) (*KafkaProducer, error) {
	kafka := &KafkaProducer{brokers: brokers}

	config := sarama.NewConfig()
	// 等待服务器所有副本都保存成功后的响应
	config.Producer.RequiredAcks = sarama.WaitForAll
	// 随机向partition发送消息
	config.Producer.Partitioner = sarama.NewRandomPartitioner

	// 是否等待成功和失败后的响应,只有上面的RequireAcks设置不是NoReponse这里才有用.
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true

	config.Producer.Timeout = 5 * time.Second

	// 这三个条件满足任何一个就会刷新到kafka
	config.Producer.Flush.Frequency = 100 * time.Millisecond // 每100毫秒刷新一次
	// config.Producer.Flush.MaxMessages = 100000            // 最多10万条消息
	// config.Producer.Flush.Bytes = 1024 * 1024             // 1MB 缓冲区大小

	// 设置使用的kafka版本,如果低于V0_10_0_0版本,消息中的timestrap没有作用.需要消费和生产同时配置
	// 注意，版本设置不对的话，kafka会返回很奇怪的错误，并且无法成功发送消息
	config.Version = sarama.V0_10_0_1

	p, err := sarama.NewAsyncProducer(brokers, config)
	kafka.Producer = p

	if err != nil {
		return nil, err
	} else {
		return kafka, nil
	}
}

// OnProduce 生产
func (k *KafkaProducer) OnProduce(topic string, data interface{}, onMessageCallback OnMessageCallback) {
	if k.Producer == nil {
		logrus.Warnf("kafka async producer is null")
		return
	}

	if onMessageCallback == nil {
	} else {
		go onMessageCallback(k.Producer)
	}

	msg := &sarama.ProducerMessage{}
	msg.Topic = topic

	// rand.Seed(time.Now().UnixMilli())
	randNum := rand.Intn(100000) // kafka partition随机
	msg.Key = sarama.StringEncoder(topic + strconv.Itoa(randNum))

	switch data.(type) {
	case string:
		msg.Value = sarama.ByteEncoder(data.(string))
	case []byte:
		msg.Value = sarama.ByteEncoder(data.([]byte))
	default:
		logrus.Errorf("record type error, topic:%s record:%+v error", topic, data)
	}

	// 防阻塞处理
	go func() {
		select {
		case k.Producer.Input() <- msg:
			if onMessageCallback != nil {
				onMessageCallback(k.Producer)
			}
		default:
			logrus.Errorf("pruducer was full, topic:%s record:%+v", topic, data)
		}
	}()

	go func() {
		for err := range k.Producer.Errors() {
			logrus.Errorf("kafka producer error: %+v, topic:%s, message:%+v", err, topic, data)
		}
	}()
}
