package kafkax

import (
	"github.com/Shopify/sarama"
	"testing"
	"time"
)

var kp *KafkaProducer
var kc *KafkaConsumer

func init() {
	var err error
	kp, err = NewKafkaAsyncProducer([]string{"192.168.1.51:9092"})
	if err != nil {
		panic(err)
	}
	kc, err = NewKafkaConsumer([]string{"192.168.1.51:9092"}, "test", []string{"test2"})
	if err != nil {
		panic(err)
	}

}

func TestPub(t *testing.T) {
	kp.OnProduce("test2", "test2", nil)
	time.Sleep(1 * time.Second)
}

func TestPubCallBack(t *testing.T) {

	callBack := func(s sarama.AsyncProducer) {
		t.Log("call back...")
	}

	kp.OnProduce("test2", "test2", callBack)
	time.Sleep(1 * time.Second)
}

func TestSub(t *testing.T) {
	kc.Consume(nil, nil, func(message *sarama.ConsumerMessage) {
		t.Log(string(message.Value))
	})
}
