package kafkax

import (
	"os"
	"os/signal"

	"github.com/Shopify/sarama"
	cluster "github.com/cnfinder/sarama-cluster"
	"github.com/sirupsen/logrus"
)

type KafkaConsumer struct {
	Consumer *cluster.Consumer
	brokers  []string
	groupId  string
	signals  chan os.Signal
	Config   *cluster.Config
}

var kafkaConfig *cluster.Config

func init() {
	kafkaConfig = cluster.NewConfig()
	kafkaConfig.Consumer.Return.Errors = true
	kafkaConfig.Group.Return.Notifications = true
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	// kafkaConfig.Consumer.Offsets.AutoCommit.Enable = false // 手动提交
}

func GetConfig() *cluster.Config {
	return kafkaConfig
}

func NewKafkaConsumer(brokers []string, groupId string, topics []string) (*KafkaConsumer, error) {
	kafka := &KafkaConsumer{brokers: brokers}
	kafka.groupId = groupId

	// init consumer
	consumer, err := cluster.NewConsumer(brokers, groupId, topics, kafkaConfig)
	if err != nil {

		return kafka, err
	}
	kafka.Consumer = consumer

	return kafka, nil

}

// Consume 启动消费
func (k *KafkaConsumer) Consume(errorFunc func(error), notificationsFunc func(*cluster.Notification), onMessage func(*sarama.ConsumerMessage)) {

	if k.Consumer == nil {
		logrus.Warnf("kafka consumer  is null")
		return
	}

	// trap SIGINT to trigger a shutdown
	k.signals = make(chan os.Signal, 1)
	signal.Notify(k.signals, os.Interrupt)

	if errorFunc != nil {
		// consume errors
		go func() {
			for err := range k.Consumer.Errors() {
				errorFunc(err)
			}

		}()
	}

	if notificationsFunc != nil {
		// consume notifications
		go func() {

			for note := range k.Consumer.Notifications() {
				notificationsFunc(note)
			}
		}()
	}

	// consume messages, watch signals
	var successes int
Loop:
	for {
		select {
		case msg, ok := <-k.Consumer.Messages():
			if ok {
				// logrus.Debug("%s:%s/%d/%d\t%s\t%s\n", k.groupId, msg.Topic, msg.Partition, msg.Offset, msg.Key, msg.Value)
				onMessage(msg)

				k.Consumer.MarkOffset(msg, "") // mark message as processed
				successes++
			}
		case <-k.signals:
			break Loop
		}
	}
}
