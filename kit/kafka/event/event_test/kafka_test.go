package event_test

import (
	"context"
	"encoding/json"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/spf13/viper"
	"log"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

const (
	numProducers   = 100
	numConsumers   = 100
	topic          = "test-topic"
	testDuration   = 10 * time.Second
	sendRatePerSec = 10
)

type TestEvent struct {
	Data string `json:"data"`
}

// 设置了100个生产者和100个消费者，每个生产者每秒发送10条消息，测试持续10秒。
func Test_kafkaSender_Send(t *testing.T) {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	viper.SetConfigFile("./config.yml")
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Sprintf("Read config file fail:%v", err))
	}

	// Create Kafka sender and receiver
	sender, err := event.NewKafkaSender(event.WithTopic(topic))
	if err != nil {
		log.Fatalf("Failed to create kafka sender: %v", err)
	}
	defer sender.Close()

	receiver, err := event.NewKafkaReceiver(event.WithTopic(topic), event.WithGroupID("heihei"))
	if err != nil {
		log.Fatalf("Failed to create kafka receiver: %v", err)
	}
	defer receiver.Close()

	// Start consumers
	var wg sync.WaitGroup
	var totalConsumed int64 = 0

	wg.Add(numConsumers)
	for i := 0; i < numConsumers; i++ {
		go func() {
			defer wg.Done()
			handler := func(ctx context.Context, evt event.Event) error {
				var testEvt TestEvent
				if err := json.Unmarshal(evt.Value(), &testEvt); err != nil {
					return err
				}
				atomic.AddInt64(&totalConsumed, 1)
				return nil
			}
			if err := receiver.Receive(context.Background(), handler); err != nil {
				log.Printf("Error in consumer: %v", err)
			}
		}()
	}

	// Start producers
	var totalProduced int64 = 0
	wg.Add(numProducers)
	for i := 0; i < numProducers; i++ {
		go func() {
			defer wg.Done()
			ticker := time.NewTicker(time.Second / time.Duration(sendRatePerSec))
			ctx, cancel := context.WithTimeout(context.Background(), testDuration)
			defer cancel()

			for {
				select {
				case <-ticker.C:
					payload := TestEvent{
						Data: generateData(),
					}
					data, _ := json.Marshal(payload)
					msg := event.NewMessage("test-key", data)
					if err := sender.Send(context.TODO(), msg); err != nil {
						log.Printf("Error in producer: %v", err)
					}
					atomic.AddInt64(&totalProduced, 1)
				case <-ctx.Done():
					return
				}
			}
		}()
	}

	wg.Wait()
	fmt.Printf("Total produced messages: %d\n", totalProduced)
	fmt.Printf("Total consumed messages: %d\n", totalConsumed)
}

func generateData() string {
	v := "{\"user_id\":\"642d3f0718a1892fe754c472\",\"currency\":\"PHP\",\"uid\":7049562666993680384,\"quantity\":\"10000\",\"amount_kind\":\"unable\",\"b_type\":\"recharge\",\"smail_type\":\"admin\",\"b_id\":\"642e28da3ebe490833d7f0dc\",\"before_quantity\":\"0.00000000\",\"after_quantity\":\"10000\",\"after_kind\":\"unable\",\"create_time\":1680746714,\"update_time\":1680746714}"
	return v
}
