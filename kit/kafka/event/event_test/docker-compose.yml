version: '2'

services:
  zookeeper:
    image: zookeeper:3.6
    ports:
      - "2181:2181"
    volumes:
      - ./zookeeper/data:/data
      - ./zookeeper/data/datalog:/data/datalog
      - ./zookeeper/datalog:/datalog
  kafka:
    image: cppla/kafka-docker:arm
    ports:
      - "9092:9092"
    environment:
      - <PERSON><PERSON><PERSON>_ADVERTISED_HOST_NAME=*************
      - KAFKA_ZOOKEEPER_CONNECT=*************:2181
      - KAFKA_ADVERTISED_PORT=9092
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://*************:9092
      - <PERSON>AFKA_LISTENERS=PLAINTEXT://0.0.0.0:9092
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./kafka:/kafka
    depends_on:
      - zookeeper
  kafka2:
    image: cppla/kafka-docker:arm
    ports:
      - "9093:9093"
    environment:
      - KAFKA_ADVERTISED_HOST_NAME=*************
      - <PERSON><PERSON><PERSON>_ZOOKEEPER_CONNECT=*************:2181
      - <PERSON><PERSON><PERSON>_ADVERTISED_PORT=9093
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://*************:9093
      - KAFKA_LISTENERS=PLAINTEXT://0.0.0.0:9093
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./kafka:/kafka
    depends_on:
      - zookeeper
  kafka3:
    image: cppla/kafka-docker:arm
    ports:
      - "9094:9094"
    environment:
      - KAFKA_ADVERTISED_HOST_NAME=*************
      - KAFKA_ZOOKEEPER_CONNECT=*************:2181
      - KAFKA_ADVERTISED_PORT=9094
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://*************:9094
      - KAFKA_LISTENERS=PLAINTEXT://0.0.0.0:9094
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./kafka:/kafka
    depends_on:
      - zookeeper
  kafka-ui:
    image: provectuslabs/kafka-ui
    container_name: kafka-ui
    ports:
      - "8082:8080"
    restart: always
    environment:
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=*************:9092
      - KAFKA_CLUSTERS_0_ZOOKEEPER=*************:2181
      - KAFKA_CLUSTERS_0_READONLY=true
    depends_on:
      - kafka
      - zookeeper
