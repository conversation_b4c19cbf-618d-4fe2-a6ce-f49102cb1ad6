package event

import (
	"context"
	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"
)

type kafkaSender struct {
	writer *kafka.Writer
	topic  string
}

func NewKafkaSender(opts ...Option) (Sender, error) {
	//读配置文件映射到kc上
	kc := &KafkaConf{}
	if err := kc.genConf("kafka-producer"); err != nil {
		return nil, err
	}

	//设置opt将覆盖配置文件
	for _, option := range opts {
		option(kc)
	}
	logrus.Infof("kafka sender topic %s initial word success config= %+v", kc.Topic, *kc)

	//tls和sasl设置
	sc, err := kc.genSafeConn()
	if err != nil {
		return nil, err
	}

	w := &kafka.Writer{
		//Topic:                  kc.Topic, // 发送时再指定以写入多个主题
		Addr:                   kafka.TCP(kc.Broker...),
		Balancer:               kc.<PERSON>lancer,
		AllowAutoTopicCreation: true,
		Transport: &kafka.Transport{
			DialTimeout: kc.Timeout,
			TLS:         sc.tlsConfig,
			SASL:        sc.mechanism,
		},
	}
	return &kafkaSender{writer: w, topic: kc.Topic}, nil
}

func (s *kafkaSender) Send(ctx context.Context, message Event) error {
	return s.writer.WriteMessages(ctx, kafka.Message{
		Topic: s.topic,
		Key:   []byte(message.Key()),
		Value: message.Value(),
	})
}

func (s *kafkaSender) SendWithTopic(ctx context.Context, topic string, message Event) error {
	return s.writer.WriteMessages(ctx, kafka.Message{
		Topic: topic,
		Key:   []byte(message.Key()),
		Value: message.Value(),
	})
}

func (s *kafkaSender) Close() error {
	return s.writer.Close()
}
