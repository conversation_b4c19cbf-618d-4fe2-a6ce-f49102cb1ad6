package event

import (
	"crypto/tls"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl"
	"github.com/segmentio/kafka-go/sasl/plain"
	"github.com/segmentio/kafka-go/sasl/scram"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"time"
)

// 接口完整性检查
var (
	_ Sender   = (*kafkaSender)(nil)
	_ Receiver = (*kafkaReceiver)(nil)
	_ Event    = (*Message)(nil)
)

// Message 包含完整的Kafka消息元数据
type Message struct {
	key       string
	value     []byte
	offset    int64
	partition int
	topic     string
}

func (m *Message) Key() string {
	return m.key
}

func (m *Message) Value() []byte {
	return m.value
}

func (m *Message) Offset() int64 {
	return m.offset
}

func (m *Message) Partition() int {
	return m.partition
}

func (m *Message) Topic() string {
	return m.topic
}

// NewMessage 创建基础消息（用于发送）
func NewMessage(key string, value []byte) Event {
	return &Message{
		key:   key,
		value: value,
	}
}

// NewMessageWithMetadata 创建包含完整元数据的消息（用于接收）
func NewMessageWithMetadata(key string, value []byte, offset int64, partition int, topic string) Event {
	return &Message{
		key:       key,
		value:     value,
		offset:    offset,
		partition: partition,
		topic:     topic,
	}
}

// KafkaConf defines kafka specific options.
type KafkaConf struct {
	Broker                []string      `mapstructure:"brokers"`
	GroupID               string        `mapstructure:"group"`
	Topic                 string        `mapstructure:"topic"`
	SSLCertFile           string        `mapstructure:"ssl_cert_file"`
	SSLKeyFile            string        `mapstructure:"ssl_key_file"`
	SASLMechanism         string        `mapstructure:"sasl_mechanism"`
	Username              string        `mapstructure:"sasl_username"`
	Password              string        `mapstructure:"sasl_password"`
	Algorithm             string        `mapstructure:"sasl_algorithm"`
	Timeout               time.Duration `mapstructure:"timeout"`
	UseSSL                bool          `mapstructure:"use_ssl"`
	SSLInsecureSkipVerify bool          `mapstructure:"ssl_insecure_skip_verify"`

	// 只能通过option指定
	Balancer       kafka.Balancer
	CommitInterval time.Duration
}

// Option 是可以修改KafkaConf的函数类型
type Option func(conf *KafkaConf)

// WithBalancer sets the balancer of the KafkaConf
func WithBalancer(balancer kafka.Balancer) Option {
	return func(c *KafkaConf) {
		c.Balancer = balancer
	}
}

func WithCommitInterval(interval time.Duration) Option {
	return func(c *KafkaConf) {
		c.CommitInterval = interval
	}
}

// WithTopic sets the topic of the KafkaConf
func WithTopic(topic string) Option {
	return func(c *KafkaConf) {
		c.Topic = topic
	}
}

// WithGroupID sets the groupID of the KafkaConf
func WithGroupID(groupID string) Option {
	return func(c *KafkaConf) {
		c.GroupID = groupID
	}
}

func (kc *KafkaConf) genConf(key string) error {
	kafkaConfig := make(map[string]interface{})
	for k, v := range viper.GetStringMap(key) {
		kafkaConfig[k] = v
	}
	err := mapstructure.Decode(kafkaConfig, kc)
	if err != nil {
		return err
	}
	if len(kc.Broker) == 0 {
		return errors.New("Failed initialize kafka len(kc.Broker) == 0")
	}
	kc.Timeout = kc.Timeout * time.Second

	return nil
}

type safeConf struct {
	tlsConfig *tls.Config
	mechanism sasl.Mechanism
}

func (kc *KafkaConf) genSafeConn() (*safeConf, error) {
	var err error
	var sc safeConf
	if kc.UseSSL {
		if kc.SSLCertFile != "" && kc.SSLKeyFile != "" {
			var cert tls.Certificate
			logrus.Info("Loading certificates for mTLS.")
			cert, err = tls.LoadX509KeyPair(kc.SSLCertFile, kc.SSLKeyFile)
			if err != nil {
				logrus.Errorf("Error loading mTLS certificates: %s", err.Error())
				return nil, errors.Wrap(err, "failed loading mTLS certificates")
			}
			sc.tlsConfig = &tls.Config{
				Certificates:       []tls.Certificate{cert},
				InsecureSkipVerify: kc.SSLInsecureSkipVerify,
			}
		} else if kc.SSLCertFile != "" || kc.SSLKeyFile != "" {
			return nil, errors.New("Only one of ssl_cert_file and ssl_cert_key configuration option is setted, you should set both to enable mTLS.")
		} else {
			sc.tlsConfig = &tls.Config{
				InsecureSkipVerify: kc.SSLInsecureSkipVerify,
			}
		}
	} else if kc.SASLMechanism != "" {
		logrus.Errorf("SASL-Mechanism is setted but use_ssl is false.SASL-Mechanism=%s", kc.SASLMechanism)
		return nil, errors.New("SASL-Mechanism is setted but use_ssl is false")
	}

	switch kc.SASLMechanism {
	case "":
		break
	case "PLAIN", "plain":
		sc.mechanism = plain.Mechanism{Username: kc.Username, Password: kc.Password}
	case "SCRAM", "scram":
		algorithm := scram.SHA256
		if kc.Algorithm == "sha-512" || kc.Algorithm == "SHA-512" {
			algorithm = scram.SHA512
		}
		var mechErr error
		sc.mechanism, mechErr = scram.Mechanism(algorithm, kc.Username, kc.Password)
		if mechErr != nil {
			logrus.Errorf("Failed initialize kafka mechanism: %s", mechErr.Error())
			return nil, mechErr
		}
	default:
		logrus.Errorf(
			"doesn't support this SASL mechanism.SASL-Mechanism=%s",
			kc.SASLMechanism,
		)
		return nil, errors.New("doesn't support this SASL mechanism")
	}
	return &sc, nil
}
