package event

import (
	"context"
	"fmt"
	"sync"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"
)

type kafkaReceiver struct {
	reader *kafka.Reader
	topic  string
}

func NewKafkaReceiver(opts ...Option) (Receiver, error) {
	//读配置文件映射到kc上
	kc := &KafkaConf{}
	if err := kc.genConf("kafka-consumer"); err != nil {
		return nil, err
	}

	//设置opt将覆盖配置文件
	for _, option := range opts {
		option(kc)
	}
	logrus.Infof("kafka receiver topic %s initial word success config= %+v", kc.Topic, *kc)

	//tls和sasl设置
	sc, err := kc.genSafeConn()
	if err != nil {
		return nil, err
	}

	dialer := &kafka.Dialer{
		Timeout:       kc.Timeout,
		TLS:           sc.tlsConfig,
		SASLMechanism: sc.mechanism,
	}
	cMapConfig := kafka.ReaderConfig{
		Dialer:         dialer,
		Brokers:        kc.Broker,
		GroupID:        kc.GroupID,
		Topic:          kc.Topic,
		MaxBytes:       10e6,              // 10MB
		CommitInterval: kc.CommitInterval, // 提交偏移量的时间间隔
	}
	r := kafka.NewReader(cMapConfig)
	return &kafkaReceiver{
		reader: r,
		topic:  kc.Topic,
	}, nil
}

func (k *kafkaReceiver) GetReceive() *kafka.Reader {
	return k.reader
}

// Receive 可能会丢数据
func (k *kafkaReceiver) Receive(ctx context.Context, handler Handler) error {
	go func() {
		for {
			m, err := k.reader.ReadMessage(ctx)
			if err != nil {
				break
			}

			// 构建包含完整元数据的消息
			event := NewMessageWithMetadata(
				string(m.Key),
				m.Value,
				m.Offset,
				m.Partition,
				m.Topic,
			)

			err = handler(ctx, event)
			if err != nil {
				logrus.Errorf("message handling exception: %s", err)
			}
		}
	}()
	return nil
}

// SafeReceive 安全地处理消息，确保不会丢失，支持优雅退出
func (k *kafkaReceiver) SafeReceive(ctx context.Context, handler Handler) error {
	var wg sync.WaitGroup
	wg.Add(1)

	go func() {
		defer wg.Done()

		for {
			select {
			case <-ctx.Done():
				logrus.Info("exit signal received, stopping message consumption")
				return
			default:
				// 先获取消息，但不自动提交
				msg, err := k.FetchMessage(ctx)
				if err != nil {
					if ctx.Err() != nil {
						// 上下文已取消，正常退出
						logrus.Info("context cancelled, stopping consumption")
						return
					}
					logrus.Errorf("fetch message failed: %v", err)
					continue
				}

				// 构建包含元数据的消息
				event := NewMessageWithMetadata(
					string(msg.Key),
					msg.Value,
					msg.Offset,
					msg.Partition,
					msg.Topic,
				)

				// 这里如果收到优雅退出信号，防止有没有处理的消息执行不完整
				newCtx := context.Background()

				if err = handler(newCtx, event); err != nil {
					logrus.Errorf("message handling failed, topic: %s, partition: %d, offset: %d, key: %s, error: %v",
						event.Topic(), event.Partition(), event.Offset(), event.Key(), err)
					// 处理失败时不提交消息，让消息重新被消费
					continue
				}

				// 只有处理成功后才提交消息
				if err = k.CommitMessages(newCtx, msg); err != nil {
					logrus.Errorf("commit message failed, topic: %s, partition: %d, offset: %d, key: %s, error: %v",
						event.Topic(), event.Partition(), event.Offset(), event.Key(), err)
					// 继续处理下一条消息，但记录错误
					continue
				}
			}
		}
	}()

	// 等待优雅关闭
	wg.Wait()
	return nil
}

func (k *kafkaReceiver) FetchMessage(ctx context.Context) (kafka.Message, error) {
	return k.reader.FetchMessage(ctx)
}

func (k *kafkaReceiver) CommitMessages(ctx context.Context, msgs ...kafka.Message) error {
	return k.reader.CommitMessages(ctx, msgs...)
}

func (k *kafkaReceiver) Close() error {
	err := k.reader.Close()
	if err != nil {
		return fmt.Errorf("close kafka reader failed: %w", err)
	}
	return nil
}
