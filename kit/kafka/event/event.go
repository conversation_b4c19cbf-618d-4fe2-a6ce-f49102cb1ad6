package event

import (
	"context"
	"github.com/segmentio/kafka-go"
)

// Event 扩展接口，包含消息的完整元数据信息
type Event interface {
	Key() string
	Value() []byte
	Offset() int64  // 消息偏移量
	Partition() int // 分区号
	Topic() string  // 主题名称
}

type Handler func(context.Context, Event) error

type Sender interface {
	Send(ctx context.Context, msg Event) error
	SendWithTopic(ctx context.Context, topic string, msg Event) error
	Close() error
}

type Receiver interface {
	GetReceive() *kafka.Reader
	Receive(ctx context.Context, handler Handler) error
	SafeReceive(ctx context.Context, handler Handler) error
	Close() error
}
