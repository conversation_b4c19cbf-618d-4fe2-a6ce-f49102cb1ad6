// Package consul_config
package consul_config

import (
	"fmt"
	"strings"
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/confd"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
)

type ConfigMgr struct {
	cache   sync.Map // key => map[string]config
	sConfig *confd.Config
	// 通常只有初始化添加
	watchHandler sync.Map // fixKey=>map[string]func(string, string)
	channels     []int32
}

var (
	once              = &sync.Once{}
	singletonInstance *ConfigMgr
)

var PrefixConfig = "fancy/GameName"

func GetInstance() *ConfigMgr {
	if singletonInstance != nil {
		return singletonInstance
	}

	once.Do(func() {
		var err error
		consulAddr := viper.GetString(dict.ConfigConsulAddr)

		singletonInstance = &ConfigMgr{
			cache: sync.Map{},
			sConfig: confd.NewConfig(
				confd.WithPrefix(PrefixConfig),
				confd.WithAdd<PERSON>(consulAddr)),
		}

		err = singletonInstance.sConfig.Init()
		if err != nil {
			logrus.Fatal(err)
		}
	})
	return singletonInstance
}

func (c *ConfigMgr) GetConfig(key string, options ...Option) (string, error) {
	opt := NewOptions(options...)

	fitKey := fmt.Sprintf("%d/%d/%s", opt.product, opt.channel, key)

	v, ok := c.cache.Load(fitKey)
	if ok {
		return fmt.Sprintf("%v", v), nil
	}

	ret := c.sConfig.Get(fitKey)
	if ret.Err() != nil {
		logrus.Warnf("consul config ret:%+v path:%+v", ret.Err(), fitKey)
		return "", ret.Err()
	}

	// 更新缓存
	errWatch := c.sConfig.Watch(fitKey, func(kv *confd.KV) {
		c.cache.Store(fitKey, kv.String())
		// 触发回调函数
		watchHandler, _ := c.watchHandler.Load(key)
		if watchHandler != nil {
			watchHandler.(func(string, string))(fitKey, kv.String())
		} else {
			logrus.Errorf("GetConfig watchHandler not found : %v", key)
		}
	})
	if errWatch != nil {
		logrus.Warnf("GetConfig watch err : %v", errWatch)
	}

	return ret.String(), nil
}

func (c *ConfigMgr) WatchCallBack(key string, handler func(*confd.KV)) any {
	v, _ := c.GetConfig(key)

	retErr := c.sConfig.Watch(key, handler)
	if retErr != nil {
		return retErr
	}

	return v
}

func (c *ConfigMgr) AddWatchHandler(key string, handler func(string, string)) {
	c.watchHandler.Store(key, handler)
}

// 获取所有渠道
func (c *ConfigMgr) GetAllChannel(options ...Option) []int32 {
	if c.channels != nil {
		return c.channels
	}
	opt := NewOptions(options...)
	channels := make([]int32, 0)
	if c == nil {
		return channels
	}
	fitKey := fmt.Sprintf("%s/%d/", PrefixConfig, opt.product)

	keys, err := c.sConfig.Keys(fitKey)
	if err != nil {
		logrus.Errorf("GetAllChannel list err : %v", err)
		return channels
	}
	for _, key := range keys {
		channelKeys := strings.Split(key, "/")
		// 最后第2组为channel
		// eg： "fancy/fisher/1/1001/"
		if len(channelKeys) > 2 {
			channel := cast.ToInt32(channelKeys[len(channelKeys)-2])
			channels = append(channels, channel)
		}
	}
	c.channels = channels
	return channels
}
