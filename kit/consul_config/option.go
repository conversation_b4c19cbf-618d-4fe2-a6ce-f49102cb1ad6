package consul_config

import (
	"context"
	"fmt"
	"reflect"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

const defaultProductID = dict.SysDefaultProductID
const defaultChannelID = dict.SysDefaultChannelID
const defaultLang = dict.SysDefaultLang

type options struct {
	jType   reflect.Type           // 对应返回值的类型，如果为 nil，则返回字符串
	product int                    // 产品
	channel int32  // 渠道
	lang    int32 // 适配语言
	version string                 // 配置版本
}

func (p *options) GetProduct() int {
	return p.product
}

func (p *options) GetChannel() int32 {
	return p.channel
}

func (p *options) GetLang() int32 {
	return p.lang
}

func (p *options) String() string {
	return fmt.Sprintf("%v", *p)
}

// Option 表示获取配置的选项
type Option func(options *options)

func NewOptions(opt ...Option) *options {
	opts := &options{
		jType:   nil,
		product: defaultProductID,
		channel: defaultChannelID,
		lang:    defaultLang,
		version: "",
	}

	for _, o := range opt {
		o(opts)
	}
	return opts
}

// WithJSON 使得 GetConfig 的返回值类型为 o 的指针类型
// 通过 json 反序列化
func WithJSON(o interface{}) Option {
	return func(options *options) {
		options.jType = reflect.TypeOf(o)
	}
}

// WithProduct 指定产品 id 为  product
func WithProduct(product int) Option {
	return func(options *options) {
		options.product = product
	}
}

// WithChannel 指定渠道 id 为 channel
func WithChannel(channel int32) Option {
	return func(options *options) {
		options.channel = channel
	}
}

// withGrpcCtx 从gpcctx 提取数据
func WithGrpcCtx(ctx context.Context) Option {
	opt := interceptor.GetRPCOptions(ctx)
	return func(options *options) {
		productId := int(opt.ProductId)
		channel := opt.ChannelType
		if opt.ProductId <= 0 {
			productId = defaultProductID
		}

		if opt.ChannelType <= 0 {
			channel = defaultChannelID
		}
		options.product = productId
		options.channel = channel
	}
}
