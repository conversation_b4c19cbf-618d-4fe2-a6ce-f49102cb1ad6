// Package singleflight 对singleflight的封装
// 适用于减少对外部资源的并发访问，如数据库查询，远程服务调用等
// 更加专注于解决特定并发重复问题，实现简单，针对性强
package singleflight

import (
	"context"
	"hash/crc32"

	"golang.org/x/sync/singleflight"
)

type multiSF struct {
	bucket    []*singleflight.Group
	bucketNum uint32
}

var msf *multiSF

func init() {
	bucketNum := uint32(64)
	msf = &multiSF{
		bucketNum: bucketNum,
		bucket:    make([]*singleflight.Group, 64),
	}
	for i := range msf.bucket {
		msf.bucket[i] = &singleflight.Group{}
	}
}

func (msf *multiSF) getBucket(key string) *singleflight.Group {
	hashN := crc32.ChecksumIEEE([]byte(key))
	sfg := msf.bucket[hashN%msf.bucketNum]
	return sfg
}

func Do(key string, fn func() (interface{}, error)) (v interface{}, err error, shared bool) {
	sfg := msf.getBucket(key)
	return sfg.Do(key, fn)
}

// DoCtx 基于ctx做超时和退出控制
func DoCtx(ctx context.Context, key string, fn func() (interface{}, error)) (val interface{}, err error) {
	sfg := msf.getBucket(key)
	ch := sfg.DoChan(key, fn)
	var result singleflight.Result
	select {
	case result = <-ch:
		return result.Val, result.Err
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}
