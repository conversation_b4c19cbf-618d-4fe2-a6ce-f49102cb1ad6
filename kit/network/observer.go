package network

import (
	"git.keepfancy.xyz/back-end/frameworks/kit/network/wpb"
	"net"
)

// IMessageObserver 消息观察者
type IMessageObserver interface {
	OnRecv(clientID uint64, header *wpb.Header, body []byte)               // OnRecv 收到消息回调
	AfterSend(clientID uint64, header *wpb.Header, body []byte, err error) // AfterSend 消息发送完成之后的回调
	OnPlayerOffLine(playerID uint64, onlineTimeSec int)                    // 玩家离线
}

// IConnectObserver 连接观察者
type IConnectObserver interface {
	OnClientConnect(clientID uint64, remoteAddr net.Addr)
	OnClientDisconnect(clientID uint64)
}
