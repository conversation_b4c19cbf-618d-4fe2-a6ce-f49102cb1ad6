package network

import (
	"encoding/binary"
	"fmt"
	socket2 "git.keepfancy.xyz/back-end/frameworks/lib/socket"
	"io"
	stdnet "net"

	"github.com/sirupsen/logrus"
)

type tcpUnPacker struct {
}

const MaxRecvLen = 30 * 1024 // 最大包长
const MaxSendLen = 60 * 1024

var _ socket2.UnPacker = new(tcpUnPacker)

func NewTCPUnPacker() socket2.UnPacker {
	return new(tcpUnPacker)
}

func (unpacked *tcpUnPacker) Unpack(r io.Reader) ([]byte, error) {
	pkgTotalSz := make([]byte, 2)
	ret, err := io.ReadFull(r, pkgTotalSz)
	if err != nil {
		if err == io.EOF {
			return nil, err
		}
		return nil, fmt.Errorf("read len err:ret=%d,err=%v", ret, err)
	}
	sz := binary.BigEndian.Uint16(pkgTotalSz)

	if sz <= 2 {
		return nil, fmt.Errorf("包长错误， %v", sz)
	}
	if sz > MaxRecvLen {
		logrus.Infof("recv pakcet len too long： len=%v", sz)
		// return nil, fmt.Errorf("包长错误， %v", sz)

	} else {
		// logrus.Tracef("recv pakcet： len=%v", sz)
	}

	// 读取排除包总长的剩余数据
	data := make([]byte, sz-2)
	if ret, err := io.ReadFull(r, data); err != nil {
		return nil, fmt.Errorf("read body err:len=%d,ret=%d,err=%v", sz, ret, err)
	}

	recvTCPBytesTotal.WithLabelValues("raw").Add(float64(sz))
	recvTCPPkgTotal.WithLabelValues("raw").Inc()

	return data, nil
}

type tcpExchanger struct {
	sock socket2.Socket
}

var _ iExchanger = new(tcpExchanger)

func (e *tcpExchanger) Close() error {
	return e.sock.Close()
}

func (e *tcpExchanger) Recv() ([]byte, error) {
	return e.sock.RecvPackage()
}

func (e *tcpExchanger) Send(data []byte) error {
	dataSz := len(data) + 2
	if dataSz > MaxSendLen {
		return fmt.Errorf("发包太长(>60K):len = %d", len(data))
	}
	pkgLen := make([]byte, 2)
	binary.BigEndian.PutUint16(pkgLen, uint16(dataSz))

	wholeData := make([]byte, dataSz)
	copy(wholeData[:2], pkgLen)
	copy(wholeData[2:], data)

	// logrus.Debugf("send pakcet： len=%v", dataSz)

	sendTCPBytesTotal.WithLabelValues("raw").Add(float64(dataSz))
	sendTCPPkgTotal.WithLabelValues("raw").Inc()

	return e.sock.SendPackage(wholeData)
}

func (e *tcpExchanger) GetRemoteAddr() stdnet.Addr {
	return e.sock.GetRemoteAddr()
}

type tcpServer struct {
	worker       workerFunc
	socketServer socket2.Server
	addr         string
}

var _ iServer = new(tcpServer)

// 每建立一条连接，都会启动一个新的goroutine执行workOnSocket
func (s *tcpServer) workOnSocket(sock socket2.Socket) {
	tcpConnectionCount.WithLabelValues("raw").Inc()
	defer tcpConnectionCount.WithLabelValues("raw").Dec()

	s.worker(&tcpExchanger{
		sock: sock,
	})
	sock.Close()
}

func (s *tcpServer) Serve(addr string, worker workerFunc) error {
	s.worker = worker
	socketServer, err := socket2.NewServer(&tcpUnPacker{}, nil)
	if err != nil {
		return err
	}
	if err := socketServer.Listen(addr); err != nil {
		return err
	}
	s.socketServer = socketServer
	for {
		sock, err := socketServer.Accept()
		if err != nil {
			return err
		}
		go s.workOnSocket(sock)
	}
}

func (s *tcpServer) Close() {
	s.socketServer.Close()
}
