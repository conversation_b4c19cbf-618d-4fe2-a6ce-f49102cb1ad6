package network

import (
	"git.keepfancy.xyz/back-end/frameworks/dict"
	wpb2 "git.keepfancy.xyz/back-end/frameworks/kit/network/wpb"
	"net"

	"google.golang.org/grpc"
	"google.golang.org/grpc/peer"
)

type rpcServer struct {
	work       workerFunc
	grpcServer *grpc.Server
	wpb2.UnimplementedExchangerServer
}

var _ iServer = new(rpcServer)
var _ wpb2.ExchangerServer = new(rpcServer)

type rpcExchanger struct {
	e wpb2.Exchanger_ExchangeServer
}

var _ iExchanger = new(rpcExchanger)

func (e *rpcExchanger) Recv() ([]byte, error) {
	var ctx *wpb2.ExchangeContext
	var err error
	if ctx, err = e.e.Recv(); err != nil {
		return []byte{}, err
	}
	return ctx.Data, err
}

func (e *rpcExchanger) Send(data []byte) error {
	return e.e.Send(&wpb2.ExchangeContext{
		Data: data,
	})
}

func (e *rpcExchanger) Close() error {
	return nil
}

func (e *rpcExchanger) GetRemoteAddr() net.Addr {
	if peerCtx, ok := peer.FromContext(e.e.Context()); ok {
		return peerCtx.Addr
	}
	return nil
}

func (s *rpcServer) Exchange(e wpb2.Exchanger_ExchangeServer) error {
	return s.work(&rpcExchanger{
		e: e,
	})
}

func (s *rpcServer) Serve(addr string, worker workerFunc) error {
	lis, err := net.Listen(dict.SysWordTcp, addr)
	if err != nil {
		return err
	}
	s.work = worker

	server := grpc.NewServer()
	wpb2.RegisterExchangerServer(server, s)

	s.grpcServer = server
	return server.Serve(lis)
}

func (s *rpcServer) Close() {
	if s.grpcServer == nil {
		return
	}
	s.grpcServer.Stop()
}
