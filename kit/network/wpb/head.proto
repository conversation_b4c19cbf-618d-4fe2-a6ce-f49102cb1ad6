syntax = "proto3";

package base;
option go_package = "./;wpb";

// MsgEncode 协议编码
enum MsgEncode {
    ME_PLAIN   = 0x0;    // 明文编码
    ME_GZIP    = 0x1;    // GZIP压缩编码
    ME_ENCRYPT = 0x2;    // 加密
}

message Header {
    optional uint32 msg_id = 1;                 // 消息ID
    optional uint64 send_seq = 2;               // 发送序号
    optional uint64 recv_seq = 3;               // 最后一次收到的消息序号
    optional uint64 stamp_time = 4;             // 最后一次收到消息的时间戳
    optional uint32 body_length = 5;            // 消息体长度
    optional uint64 rsp_seq = 6;                // 回复序号
    optional string version = 7;                // 发送方的版本号
    optional uint32 routine = 8;                // 路由
    optional MsgEncode encode_type = 9;         // 协议编码

    optional string client_ip = 10;             // 客户端IP地址
    optional string path = 11;                  // 请求的url
    optional string platform = 12;              // 操作终端平台  ios ipad android itv等
    optional string client_type = 13;           // 客户类型 1 app  2 小游戏
}