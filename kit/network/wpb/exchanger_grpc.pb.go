// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: exchanger.proto

package wpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Exchanger_Exchange_FullMethodName = "/fancy.proto.base.Exchanger/Exchange"
)

// ExchangerClient is the client API for Exchanger service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExchangerClient interface {
	Exchange(ctx context.Context, opts ...grpc.CallOption) (Exchanger_ExchangeClient, error)
}

type exchangerClient struct {
	cc grpc.ClientConnInterface
}

func NewExchangerClient(cc grpc.ClientConnInterface) ExchangerClient {
	return &exchangerClient{cc}
}

func (c *exchangerClient) Exchange(ctx context.Context, opts ...grpc.CallOption) (Exchanger_ExchangeClient, error) {
	stream, err := c.cc.NewStream(ctx, &Exchanger_ServiceDesc.Streams[0], Exchanger_Exchange_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &exchangerExchangeClient{stream}
	return x, nil
}

type Exchanger_ExchangeClient interface {
	Send(*ExchangeContext) error
	Recv() (*ExchangeContext, error)
	grpc.ClientStream
}

type exchangerExchangeClient struct {
	grpc.ClientStream
}

func (x *exchangerExchangeClient) Send(m *ExchangeContext) error {
	return x.ClientStream.SendMsg(m)
}

func (x *exchangerExchangeClient) Recv() (*ExchangeContext, error) {
	m := new(ExchangeContext)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ExchangerServer is the server API for Exchanger service.
// All implementations must embed UnimplementedExchangerServer
// for forward compatibility
type ExchangerServer interface {
	Exchange(Exchanger_ExchangeServer) error
	mustEmbedUnimplementedExchangerServer()
}

// UnimplementedExchangerServer must be embedded to have forward compatible implementations.
type UnimplementedExchangerServer struct {
}

func (UnimplementedExchangerServer) Exchange(Exchanger_ExchangeServer) error {
	return status.Errorf(codes.Unimplemented, "method Exchange not implemented")
}
func (UnimplementedExchangerServer) mustEmbedUnimplementedExchangerServer() {}

// UnsafeExchangerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExchangerServer will
// result in compilation errors.
type UnsafeExchangerServer interface {
	mustEmbedUnimplementedExchangerServer()
}

func RegisterExchangerServer(s grpc.ServiceRegistrar, srv ExchangerServer) {
	s.RegisterService(&Exchanger_ServiceDesc, srv)
}

func _Exchanger_Exchange_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ExchangerServer).Exchange(&exchangerExchangeServer{stream})
}

type Exchanger_ExchangeServer interface {
	Send(*ExchangeContext) error
	Recv() (*ExchangeContext, error)
	grpc.ServerStream
}

type exchangerExchangeServer struct {
	grpc.ServerStream
}

func (x *exchangerExchangeServer) Send(m *ExchangeContext) error {
	return x.ServerStream.SendMsg(m)
}

func (x *exchangerExchangeServer) Recv() (*ExchangeContext, error) {
	m := new(ExchangeContext)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// Exchanger_ServiceDesc is the grpc.ServiceDesc for Exchanger service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Exchanger_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "fancy.proto.base.Exchanger",
	HandlerType: (*ExchangerServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Exchange",
			Handler:       _Exchanger_Exchange_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "exchanger.proto",
}
