// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.25.3
// source: head.proto

package wpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MsgEncode 协议编码
type MsgEncode int32

const (
	MsgEncode_ME_PLAIN   MsgEncode = 0 // 明文编码
	MsgEncode_ME_GZIP    MsgEncode = 1 // GZIP压缩编码
	MsgEncode_ME_ENCRYPT MsgEncode = 2 // 加密
)

// Enum value maps for MsgEncode.
var (
	MsgEncode_name = map[int32]string{
		0: "ME_PLAIN",
		1: "ME_GZIP",
		2: "ME_ENCRYPT",
	}
	MsgEncode_value = map[string]int32{
		"ME_PLAIN":   0,
		"ME_GZIP":    1,
		"ME_ENCRYPT": 2,
	}
)

func (x MsgEncode) Enum() *MsgEncode {
	p := new(MsgEncode)
	*p = x
	return p
}

func (x MsgEncode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgEncode) Descriptor() protoreflect.EnumDescriptor {
	return file_head_proto_enumTypes[0].Descriptor()
}

func (MsgEncode) Type() protoreflect.EnumType {
	return &file_head_proto_enumTypes[0]
}

func (x MsgEncode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgEncode.Descriptor instead.
func (MsgEncode) EnumDescriptor() ([]byte, []int) {
	return file_head_proto_rawDescGZIP(), []int{0}
}

type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId      *uint32    `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3,oneof" json:"msg_id,omitempty"`                                    // 消息ID
	SendSeq    *uint64    `protobuf:"varint,2,opt,name=send_seq,json=sendSeq,proto3,oneof" json:"send_seq,omitempty"`                              // 发送序号
	RecvSeq    *uint64    `protobuf:"varint,3,opt,name=recv_seq,json=recvSeq,proto3,oneof" json:"recv_seq,omitempty"`                              // 最后一次收到的消息序号
	StampTime  *uint64    `protobuf:"varint,4,opt,name=stamp_time,json=stampTime,proto3,oneof" json:"stamp_time,omitempty"`                        // 最后一次收到消息的时间戳
	BodyLength *uint32    `protobuf:"varint,5,opt,name=body_length,json=bodyLength,proto3,oneof" json:"body_length,omitempty"`                     // 消息体长度
	RspSeq     *uint64    `protobuf:"varint,6,opt,name=rsp_seq,json=rspSeq,proto3,oneof" json:"rsp_seq,omitempty"`                                 // 回复序号
	Version    *string    `protobuf:"bytes,7,opt,name=version,proto3,oneof" json:"version,omitempty"`                                              // 发送方的版本号
	Routine    *uint32    `protobuf:"varint,8,opt,name=routine,proto3,oneof" json:"routine,omitempty"`                                             // 路由
	EncodeType *MsgEncode `protobuf:"varint,9,opt,name=encode_type,json=encodeType,proto3,enum=base.MsgEncode,oneof" json:"encode_type,omitempty"` // 协议编码
	ClientIp   *string    `protobuf:"bytes,10,opt,name=client_ip,json=clientIp,proto3,oneof" json:"client_ip,omitempty"`                           // 客户端IP地址
	Path       *string    `protobuf:"bytes,11,opt,name=path,proto3,oneof" json:"path,omitempty"`                                                   // 请求的url
	Platform   *string    `protobuf:"bytes,12,opt,name=platform,proto3,oneof" json:"platform,omitempty"`                                           // 操作终端平台  ios ipad android itv等
	ClientType *string    `protobuf:"bytes,13,opt,name=client_type,json=clientType,proto3,oneof" json:"client_type,omitempty"`                     // 客户类型 1 app  2 小游戏
}

func (x *Header) Reset() {
	*x = Header{}
	if protoimpl.UnsafeEnabled {
		mi := &file_head_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_head_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_head_proto_rawDescGZIP(), []int{0}
}

func (x *Header) GetMsgId() uint32 {
	if x != nil && x.MsgId != nil {
		return *x.MsgId
	}
	return 0
}

func (x *Header) GetSendSeq() uint64 {
	if x != nil && x.SendSeq != nil {
		return *x.SendSeq
	}
	return 0
}

func (x *Header) GetRecvSeq() uint64 {
	if x != nil && x.RecvSeq != nil {
		return *x.RecvSeq
	}
	return 0
}

func (x *Header) GetStampTime() uint64 {
	if x != nil && x.StampTime != nil {
		return *x.StampTime
	}
	return 0
}

func (x *Header) GetBodyLength() uint32 {
	if x != nil && x.BodyLength != nil {
		return *x.BodyLength
	}
	return 0
}

func (x *Header) GetRspSeq() uint64 {
	if x != nil && x.RspSeq != nil {
		return *x.RspSeq
	}
	return 0
}

func (x *Header) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *Header) GetRoutine() uint32 {
	if x != nil && x.Routine != nil {
		return *x.Routine
	}
	return 0
}

func (x *Header) GetEncodeType() MsgEncode {
	if x != nil && x.EncodeType != nil {
		return *x.EncodeType
	}
	return MsgEncode_ME_PLAIN
}

func (x *Header) GetClientIp() string {
	if x != nil && x.ClientIp != nil {
		return *x.ClientIp
	}
	return ""
}

func (x *Header) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *Header) GetPlatform() string {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return ""
}

func (x *Header) GetClientType() string {
	if x != nil && x.ClientType != nil {
		return *x.ClientType
	}
	return ""
}

var File_head_proto protoreflect.FileDescriptor

var file_head_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x22, 0xef, 0x04, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a,
	0x06, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52,
	0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x01, 0x52, 0x07, 0x73,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x71, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x72, 0x65, 0x63,
	0x76, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x48, 0x02, 0x52, 0x07, 0x72,
	0x65, 0x63, 0x76, 0x53, 0x65, 0x71, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x48, 0x03, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a,
	0x0b, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x48, 0x04, 0x52, 0x0a, 0x62, 0x6f, 0x64, 0x79, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x72, 0x73, 0x70, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x48, 0x05, 0x52, 0x06, 0x72, 0x73, 0x70, 0x53, 0x65, 0x71, 0x88, 0x01,
	0x01, 0x12, 0x1d, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x06, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x1d, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x48, 0x07, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x35, 0x0a, 0x0b, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x73, 0x67, 0x45,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x48, 0x08, 0x52, 0x0a, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x70, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x88, 0x01,
	0x01, 0x12, 0x1f, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x88,
	0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6d, 0x73, 0x67,
	0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x65, 0x71,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x72, 0x65, 0x63, 0x76, 0x5f, 0x73, 0x65, 0x71, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x72, 0x73, 0x70, 0x5f, 0x73, 0x65, 0x71, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x65,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2a, 0x36, 0x0a, 0x09, 0x4d, 0x73, 0x67, 0x45, 0x6e, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x4d, 0x45, 0x5f, 0x47, 0x5a, 0x49, 0x50, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x4d, 0x45, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x10, 0x02, 0x42, 0x08, 0x5a, 0x06,
	0x2e, 0x2f, 0x3b, 0x77, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_head_proto_rawDescOnce sync.Once
	file_head_proto_rawDescData = file_head_proto_rawDesc
)

func file_head_proto_rawDescGZIP() []byte {
	file_head_proto_rawDescOnce.Do(func() {
		file_head_proto_rawDescData = protoimpl.X.CompressGZIP(file_head_proto_rawDescData)
	})
	return file_head_proto_rawDescData
}

var file_head_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_head_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_head_proto_goTypes = []interface{}{
	(MsgEncode)(0), // 0: base.MsgEncode
	(*Header)(nil), // 1: base.Header
}
var file_head_proto_depIdxs = []int32{
	0, // 0: base.Header.encode_type:type_name -> base.MsgEncode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_head_proto_init() }
func file_head_proto_init() {
	if File_head_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_head_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_head_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_head_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_head_proto_goTypes,
		DependencyIndexes: file_head_proto_depIdxs,
		EnumInfos:         file_head_proto_enumTypes,
		MessageInfos:      file_head_proto_msgTypes,
	}.Build()
	File_head_proto = out.File
	file_head_proto_rawDesc = nil
	file_head_proto_goTypes = nil
	file_head_proto_depIdxs = nil
}
