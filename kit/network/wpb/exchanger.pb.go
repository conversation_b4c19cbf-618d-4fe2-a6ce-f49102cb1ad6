// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.25.3
// source: exchanger.proto

package wpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExchangeContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ExchangeContext) Reset() {
	*x = ExchangeContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_exchanger_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeContext) ProtoMessage() {}

func (x *ExchangeContext) ProtoReflect() protoreflect.Message {
	mi := &file_exchanger_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeContext.ProtoReflect.Descriptor instead.
func (*ExchangeContext) Descriptor() ([]byte, []int) {
	return file_exchanger_proto_rawDescGZIP(), []int{0}
}

func (x *ExchangeContext) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_exchanger_proto protoreflect.FileDescriptor

var file_exchanger_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x10, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x22, 0x25, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x63, 0x0a, 0x09, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x08, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x21, 0x2e, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x21, 0x2e, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x42,
	0x08, 0x5a, 0x06, 0x2e, 0x2f, 0x3b, 0x77, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_exchanger_proto_rawDescOnce sync.Once
	file_exchanger_proto_rawDescData = file_exchanger_proto_rawDesc
)

func file_exchanger_proto_rawDescGZIP() []byte {
	file_exchanger_proto_rawDescOnce.Do(func() {
		file_exchanger_proto_rawDescData = protoimpl.X.CompressGZIP(file_exchanger_proto_rawDescData)
	})
	return file_exchanger_proto_rawDescData
}

var file_exchanger_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_exchanger_proto_goTypes = []interface{}{
	(*ExchangeContext)(nil), // 0: fancy.proto.base.ExchangeContext
}
var file_exchanger_proto_depIdxs = []int32{
	0, // 0: fancy.proto.base.Exchanger.Exchange:input_type -> fancy.proto.base.ExchangeContext
	0, // 1: fancy.proto.base.Exchanger.Exchange:output_type -> fancy.proto.base.ExchangeContext
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_exchanger_proto_init() }
func file_exchanger_proto_init() {
	if File_exchanger_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_exchanger_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_exchanger_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_exchanger_proto_goTypes,
		DependencyIndexes: file_exchanger_proto_depIdxs,
		MessageInfos:      file_exchanger_proto_msgTypes,
	}.Build()
	File_exchanger_proto = out.File
	file_exchanger_proto_rawDesc = nil
	file_exchanger_proto_goTypes = nil
	file_exchanger_proto_depIdxs = nil
}
