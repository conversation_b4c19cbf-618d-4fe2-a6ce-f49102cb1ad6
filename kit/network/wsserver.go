package network

import (
	"encoding/binary"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/ginx"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	stdnet "net"
	"net/http"
	"reflect"
)

type wsExchanger struct {
	conn *websocket.Conn
}

var _ iExchanger = new(wsExchanger)

func (e *wsExchanger) Recv() ([]byte, error) {
	_, message, err := e.conn.ReadMessage()
	if err != nil {
		if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
			logrus.Infof("error: %v", err)
		}
		return nil, err
	}

	if len(message) > MaxRecvLen {
		return nil, fmt.Errorf("接收包超过上限错误: %d > %d", len(message), MaxRecvLen)
	}
	return e.unpack(message)
}

func (e *wsExchanger) unpack(buf []byte) ([]byte, error) {
	ret := len(buf)
	if ret < 2 {
		return nil, fmt.Errorf("read len err:ret=%d", ret)
	}
	byteSz := buf[:2]

	sz := binary.BigEndian.Uint16(byteSz)

	if sz <= 2 {
		return nil, fmt.Errorf("包长错误， %v", sz)
	}
	if sz > MaxRecvLen {
		logrus.Infof("read len too long： %v", sz)
		// return nil, fmt.Errorf("包长错误， %v", sz)
	}

	if int(sz) != len(buf) {
		return nil, fmt.Errorf("read body err[len: %d, ret: %d]", sz, ret)
	}

	recvTCPBytesTotal.WithLabelValues("wss").Add(float64(sz))
	recvTCPPkgTotal.WithLabelValues("wss").Inc()

	return buf[2:], nil
}

func (e *wsExchanger) Send(data []byte) error {
	dataSz := len(data) + 2
	if dataSz > MaxSendLen {
		return fmt.Errorf("发包太长(>60K):len = %d", len(data))
	}

	header := make([]byte, 2)
	binary.BigEndian.PutUint16(header, uint16(dataSz))

	wholeData := make([]byte, dataSz)
	copy(wholeData[:2], header)
	copy(wholeData[2:], data)

	err := e.conn.WriteMessage(websocket.BinaryMessage, wholeData)
	if err != nil {
		logrus.Errorf("send buf ret error: len=%d,err=%v", dataSz, err)
		return err
	}

	// logrus.Debugf("Send buf ret succeed: len=%d", dataSz)

	sendTCPBytesTotal.WithLabelValues("wss").Add(float64(dataSz))
	sendTCPPkgTotal.WithLabelValues("wss").Inc()

	return err
}

func (e *wsExchanger) Close() error {
	return e.conn.Close()
}

func (e *wsExchanger) GetRemoteAddr() stdnet.Addr {
	return e.conn.RemoteAddr()
}

type wsServer struct {
	worker      workerFunc
	enableHTTPS bool
}

var _ iServer = new(wsServer)

func (s *wsServer) workOnSocket(conn *websocket.Conn) {
	tcpConnectionCount.WithLabelValues("ws").Inc()
	defer tcpConnectionCount.WithLabelValues("ws").Dec()

	s.worker(&wsExchanger{
		conn: conn,
	})

	conn.Close()
}

var upgrade = websocket.Upgrader{
	// Subprotocols: []string{"network"},
	Subprotocols: []string{"network"},
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func (s *wsServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	if !reflect.DeepEqual(websocket.Subprotocols(r), upgrade.Subprotocols) {
		logrus.Errorf("bad Handshake, sub protocol must be [%v], not [%v]", upgrade.Subprotocols, websocket.Subprotocols(r))
		return
	}

	ws, err := upgrade.Upgrade(w, r, nil)
	if err != nil {
		logrus.Errorln("The origin:", err)
		return
	}

	logrus.Infoln("SubProtocol: ", ws.Subprotocol())
	s.workOnSocket(ws)
}

func (s *wsServer) Serve(addr string, worker workerFunc) error {
	s.worker = worker
	// http.Handle("/ws", s)
	ginx.GetGinEngine().GET("/ws", func(c *gin.Context) {
		s.ServeHTTP(c.Writer, c.Request)
	})
	return nil
}

func (s *wsServer) Close() {
}
