package network

import "github.com/prometheus/client_golang/prometheus"

var (
	recvTCPPkgTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "recv_tcp_pkg_total",
			Help: "tcp 消息包接收数量",
		},
		[]string{"type"}, // raw or wss
	)

	recvTCPBytesTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "recv_tcp_bytes_total",
			Help: "tcp 消息接收字节数量",
		},
		[]string{"type"}, // raw or wss
	)

	sendTCPPkgTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "send_tcp_pkg_total",
			Help: "tcp 消息包发送数量",
		},
		[]string{"type"}, // raw or wss
	)

	sendTCPBytesTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "send_tcp_bytes_total",
			Help: "tcp 消息发送字节数量",
		},
		[]string{"type"}, // raw or wss
	)

	tcpConnectionCount = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "tcp_connection_count",
			Help: "tcp 连接数",
		},
		[]string{"type"}, // raw or wss
	)
)

func init() {
	prometheus.MustRegister(
		recvTCPPkgTotal,
		recvTCPBytesTotal,
		sendTCPPkgTotal,
		sendTCPBytesTotal,
		tcpConnectionCount)
}
