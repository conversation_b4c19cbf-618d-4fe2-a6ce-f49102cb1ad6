package network

import (
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/network/wpb"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/sirupsen/logrus"
	"sync"
	"sync/atomic"
)

// ServerType 服务类型
type ServerType int

const (
	// RPC 使用 gRPC 作为基础通信框架
	RPC ServerType = iota
	// TCP 使用 TCP 协议作为基础通信框架
	TCP
	// WS 使用 http websocket 协议作为基础通信框架
	WS
	// WSS 使用 https websocket 协议作为基础通信框架
	WSS
)

// NetServicesImpl 用于管理与客户端的连接
type NetServicesImpl struct {
	msgObserver  IMessageObserver
	connObserver IConnectObserver

	clientMap   sync.Map // 客户端连接句柄 <clientId，clientV2>
	serverMap   map[ServerType]iServer
	maxClientID uint64
}

// Start 启动指定类型的网络服务
func (netImpl *NetServicesImpl) Start(addr string, serverType ServerType) error {
	if _, ok := netImpl.serverMap[serverType]; ok {
		return fmt.Errorf("该类型的服务已经启动了")
	}

	var server iServer
	switch serverType {
	case RPC:
		server = &rpcServer{}
	case TCP:
		server = &tcpServer{}
	case WS:
		server = &wsServer{}
	case WSS:
		server = &wsServer{enableHTTPS: true}
	default:
		logrus.Panicf("未知服务类型: %d", serverType)
	}

	netImpl.serverMap[serverType] = server
	err := server.Serve(addr, netImpl.workOnExchanger)
	if err != nil {
		logrus.Warnf("Serve Error:%v", err.Error())
	}
	return nil
}

// Stop 停止所有类型的服务
func (netImpl *NetServicesImpl) Stop() error { // stop 不需要关注连接类型
	for _, v := range netImpl.serverMap {
		v.Close()
	}
	return nil
}

// Client 数据交互作业
func (netImpl *NetServicesImpl) workOnExchanger(e iExchanger) error {
	defer logdog.RecoverAndLog()

	// 原子操作(atomic来保证并发情况下原子操作绝对安全)
	clientID := atomic.AddUint64(&netImpl.maxClientID, 1)

	client := &clientV2{
		exchanger:   e,
		msgObserver: netImpl.msgObserver,
		clientID:    clientID,
	}

	// 存储连接句柄
	netImpl.clientMap.Store(clientID, client)

	// 将客户端委托给连接管理器管理,管理连接和断开
	netImpl.connObserver.OnClientConnect(clientID, e.GetRemoteAddr())

	defer netImpl.clientMap.Delete(clientID)
	defer netImpl.connObserver.OnClientDisconnect(clientID)

	logrus.Infof("[客户端 %d 开启连接]", clientID)
	err := client.run() // 开启客户端发送,接受作业
	if err != nil {
		return err
	}
	logrus.Infof("客户端 %d 关闭连接", clientID)

	return nil
}

// SendPackage 向客户端 clientID 发送消息包
func (netImpl *NetServicesImpl) SendPackage(clientID uint64, header *wpb.Header, body []byte) error {
	tmp, ok := netImpl.clientMap.Load(clientID)
	if !ok {
		logrus.Warnf("客户端 %d 不存在, msgId: %d", clientID, header.GetMsgId())
		return errors.New("客户端不存在")
	}

	if err := tmp.(*clientV2).pushMessage(header, body); err != nil {
		logrus.WithError(err).Errorf("推送消息失败，clientID: %d msg: %d", clientID, header.GetMsgId())
		return err
	}
	return nil
}

// BroadPackage 向 clientIDs 中的所有客户端发送消息
func (netImpl *NetServicesImpl) BroadPackage(clientIDs []uint64, header *wpb.Header, body []byte) error {
	for _, clientID := range clientIDs {
		err := netImpl.SendPackage(clientID, header, body)
		if err != nil {
			continue
		}
	}
	return nil
}

// OnPlayerOffLine 玩家离线
func (netImpl *NetServicesImpl) OnPlayerOffLine(playerID uint64, onlineTimeSec int) {
	netImpl.msgObserver.OnPlayerOffLine(playerID, onlineTimeSec)
}

// Disconnect 断开与客户端 clientID 的连接
func (netImpl *NetServicesImpl) Disconnect(clientID uint64) error {
	var c *clientV2
	if tmp, ok := netImpl.clientMap.Load(clientID); ok {
		c = tmp.(*clientV2)
	} else {
		return fmt.Errorf("clientID %v not exists", clientID)
	}
	netImpl.clientMap.Delete(clientID)
	c.close()
	return nil
}

// NewNetService 创建并返回 watchDog
func NewNetService(msgObserver IMessageObserver, connObserver IConnectObserver) *NetServicesImpl {
	return &NetServicesImpl{
		msgObserver:  msgObserver,
		connObserver: connObserver,
		serverMap:    make(map[ServerType]iServer),
	}
}
