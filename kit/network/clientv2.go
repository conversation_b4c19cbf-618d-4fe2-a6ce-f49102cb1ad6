package network

import (
	"bytes"
	"compress/gzip"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/kit/network/wpb"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"

	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

// enableGZipMessage 是否压缩消息
var enableGZipMessage = false

// gzipMsgMin 需要进行gzip压缩消息长度
var gzipMsgMin = 512

// sendChanLen 发送队列长度
var sendChanLen = 20

type receiveData struct {
	header *wpb.Header
	data   []byte
}

type sendData struct {
	header *wpb.Header
	body   []byte
}

// SetSendChanLen 设置发送队列长度
func SetSendChanLen(chanLen int) {
	if chanLen >= 10 {
		sendChanLen = chanLen
	}
}

// EnableGZipMessage 启用gzip压缩
func EnableGZipMessage(enable bool) {
	enableGZipMessage = enable
}

// SetGZipMessageMin 设置压缩最小长度
func SetGZipMessageMin(min int) {
	if min < 32 {
		return
	}
	gzipMsgMin = min
}

type clientV2 struct {
	exchanger   iExchanger       // 数据交互接口
	clientID    uint64           // 客户端连接ID
	msgObserver IMessageObserver // 消息Observe

	recvSeq       uint64 // 接收的消息序号
	sendSeq       uint64 // 发送序号
	recvTimestamp int64  // 接收到消息的时间

	cSend  chan *sendData // 数据发送通道
	mu     sync.RWMutex
	closed bool
}

// encodeMessage 压缩或加密消息
func (c *clientV2) encodeMessage(head *wpb.Header, body []byte) ([]byte, error) {
	if len(body) < gzipMsgMin {
		return body, nil
	}

	var b bytes.Buffer
	w := gzip.NewWriter(&b)

	defer func(w *gzip.Writer) {
		err := w.Close()
		if err != nil {

		}
	}(w)

	_, err := w.Write(body)
	if err != nil {
		return body, err
	}

	errFlush := w.Flush()
	if errFlush != nil {
		return nil, errFlush
	}

	me := wpb.MsgEncode_ME_GZIP
	head.EncodeType = &me

	return b.Bytes(), nil
}

func (c *clientV2) pushMessage(head *wpb.Header, body []byte) (err error) {
	if c.closed {
		return errors.New("socket is closed")
	}
	defer logdog.RecoverAndLog("network.push")

	if body == nil {
		body = []byte{}
	}

	if body == nil || head == nil {
		return errors.New("Send data is empty")
	}

	// 压缩消息
	if enableGZipMessage {
		body, _ = c.encodeMessage(head, body)
	}

	c.mu.RLock()

	// slow path
	if c.closed {
		c.mu.RUnlock()
		return errors.New("socket is closed")
	}

	data := &sendData{
		header: proto.Clone(head).(*wpb.Header),
		body:   body,
	}

	// 为保证消息有序，不使用 goroutine.用带缓冲区的通道
	select {
	case c.cSend <- data:
		// logrus.Debugf("send_queue size : (%v)", len(c.cSend))
		c.mu.RUnlock()
		break
	default:
		logrus.Errorf("send chan was full, disconnect")
		c.mu.RUnlock()
		c.close()
	}

	return
}

// 关闭
func (c *clientV2) close() {
	// fast path
	if c.closed {
		return
	}

	c.mu.Lock()

	// slow path
	if c.closed {
		c.mu.Unlock()
		return
	}
	c.closed = true

	close(c.cSend)

	c.mu.Unlock()

	c.handleClientClose()
}

// run 返回后表示 client 的生命周期结束
func (c *clientV2) run() error {
	defer logdog.RecoverAndLog()

	c.cSend = make(chan *sendData, sendChanLen)
	wg := new(sync.WaitGroup)

	// 接收数据 goroutine
	c.recvLoop(wg)

	// 发送数据 goroutine
	c.sendLoop(wg)

	wg.Wait()

	logrus.Debugln("run loop routine end")
	return nil
}

// recvLoop 启动接收数据 goroutine
func (c *clientV2) recvLoop(wg *sync.WaitGroup) {
	wg.Add(1)
	go func() {
		defer logdog.RecoverAndLog("network.recv")
		defer wg.Done()
		defer c.close()
		defer func() {
			// logrus.Debugln("recv loop routine end")
		}()

		for {
			if c.closed {
				return
			}
			data := c.recv()

			// 读到的数据为 nil，说明连接被对方断开了
			if data == nil {
				return
			}
			c.handleRecv(data)
		}
	}()

}

func (c *clientV2) sendLoop(wg *sync.WaitGroup) {
	wg.Add(1)
	go func() {
		defer logdog.RecoverAndLog("network.send")
		defer wg.Done()
		defer func() {
			logrus.Debugln("send loop routine end")
		}()
		// 数据全部发送完了再关闭 iExchanger
		// 在 iExchanger 关闭之前仍能发送数据
		defer func(exchanger iExchanger) {
			err := exchanger.Close()
			if err != nil {

			}
		}(c.exchanger)

		for {
			data, ok := <-c.cSend
			if !ok {
				return
			}
			err := c.send(data)

			c.msgObserver.AfterSend(c.clientID, data.header, data.body, err)
			if err != nil {
				logrus.WithError(err).Errorf("数据发送失败，clientID: %d", c.clientID)
			}
		}
	}()

}

func (c *clientV2) send(data *sendData) error {
	bodySz := uint32(len(data.body))

	recvSeq, recvTimeStamp := c.getRecvState()

	timeStamp := uint64(recvTimeStamp)
	sendSeq := c.sendSeq + 1

	head := data.header

	head.BodyLength = &bodySz
	head.RecvSeq = &recvSeq
	head.StampTime = &timeStamp
	head.SendSeq = &sendSeq

	var headBytes []byte
	var err error
	if headBytes, err = proto.Marshal(head); err != nil {
		return fmt.Errorf("pb消息头序列化失败")
	}

	if len(headBytes) > 0xff {
		return fmt.Errorf("消息头过长")
	}

	c.sendSeq++
	wholeData := make([]byte, 1+len(headBytes)+int(bodySz))
	wholeData[0] = byte(len(headBytes))
	copy(wholeData[1:len(headBytes)+1], headBytes)
	copy(wholeData[len(headBytes)+1:], data.body)
	return c.exchanger.Send(wholeData)
}

// recv 从 iExchanger 中接收数据。 接收过程中不会中断
func (c *clientV2) recv() *receiveData {
	d, err := c.exchanger.Recv()
	if err != nil {
		if err != io.EOF {
			logrus.WithError(err).Warnf("接受数据失败，clientID: %d", c.clientID)
		}
		return nil
	}

	if len(d) == 0 {
		logrus.Warnf("0字节消息包，clientID:%d", c.clientID)
		return nil
	}

	headsz := d[0]
	if uint32(headsz) > uint32(len(d))-uint32(1) {
		logrus.Warnf("消息头大小超过消息包数据大小，clientID:%d", c.clientID)
		return nil
	}

	header := new(wpb.Header)
	if err := proto.Unmarshal(d[1:1+headsz], header); err != nil {
		logrus.Warnf("消息头反序列化失败，clientID:%d", c.clientID)
		return nil
	}

	left := uint32(len(d)) - uint32(1) - uint32(headsz)

	if header.GetBodyLength() != left {
		logrus.Warnf("消息体大小错误， 剩余:%v 需要:%v，clientID:%d", left, header.GetBodyLength(), c.clientID)
		return nil
	}

	return &receiveData{
		header: header,
		data:   d[1+headsz:],
	}
}

// decodeMessage 解压缩或解密消息
func (c *clientV2) decodeMessage(data *receiveData) error {

	b := bytes.NewBuffer(data.data)
	r, err := gzip.NewReader(b)
	if err != nil {
		return err
	}

	defer func(r *gzip.Reader) {
		err := r.Close()
		if err != nil {

		}
	}(r)
	data.data, _ = ioutil.ReadAll(r)
	return nil
}

// handleRecv 处理返回消息
func (c *clientV2) handleRecv(data *receiveData) {
	now := time.Now().Unix()
	c.recvSeq = data.header.GetSendSeq()
	c.recvTimestamp = now

	if data.header.GetEncodeType()&wpb.MsgEncode_ME_GZIP == wpb.MsgEncode_ME_GZIP {
		err := c.decodeMessage(data)
		if err != nil {
			logrus.Warn(err)
			return
		}
	}

	c.msgObserver.OnRecv(c.clientID, data.header, data.data)
}

// handleClientClose 客户端断开连接
func (c *clientV2) handleClientClose() {
	c.msgObserver.OnRecv(c.clientID, nil, []byte{})
}

func (c *clientV2) getRecvState() (uint64, int64) {
	return c.recvSeq, c.recvTimestamp
}
