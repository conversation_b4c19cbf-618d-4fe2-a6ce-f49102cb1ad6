package location

import (
	"net"
	"os"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/oschwald/geoip2-golang"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// IpLocation 地理位置信息结构体
type IpLocation struct {
	Country   string  `json:"country"`   // 国家 china
	City      string  `json:"city"`      // 城市 beijing
	TimeZone  string  `json:"time_zone"` // 时区 Asia/Shanghai
	Latitude  float64 `json:"latitude"`  // 纬度 23.1181
	Longitude float64 `json:"longitude"` // 经度 113.2539
}

// GetLocationByIP 通过ip获取地理位置
func GetLocationByIP(ipAddress string) *IpLocation {
	// 验证IP地址的有效性
	if net.ParseIP(ipAddress) == nil {
		return nil
	}

	geoPath := dict.SysDefaultGeoDbPath
	cfgPath := viper.GetString(dict.SysWordGeoDb)
	if cfgPath != "" {
		if cfgFile := viper.GetString(dict.SysWordGeoDb); cfgFile != "" {
			geoPath = cfgFile
		}
	}

	// 打开GeoLite2-City数据库
	db, err := geoip2.Open(geoPath) // 确保路径正确
	if err != nil {
		// 打印当前绝对路径和geoPath
		curPath, _ := os.Getwd()
		logrus.Errorf("Error opening GeoLite2-City database: %+v current path: %s geoPath: %s", err, curPath, geoPath)
		return nil
	}
	defer db.Close()

	// 查询数据库获取记录
	record, err := db.City(net.ParseIP(ipAddress))
	if err != nil {
		return nil
	}

	// 构建并返回IpLocation结构体
	locationInfo := &IpLocation{
		Country:   transform.TrimAndLower(record.Country.Names["en"]),
		City:      transform.TrimAndLower(record.City.Names["en"]),
		TimeZone:  record.Location.TimeZone,
		Latitude:  record.Location.Latitude,
		Longitude: record.Location.Longitude,
	}

	logrus.Infof("IP:%s, location:%+v", ipAddress, locationInfo)

	return locationInfo
}
