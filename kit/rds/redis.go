package rds

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/go-redis/redis/v8"

	"github.com/sirupsen/logrus"
)

const DEFAULT = "default"
const PLAYER = "player"

var GetRedisClient = func() *redis.Client {
	return GetRedisCli(DEFAULT)
}

// GetRedisCli 获取 redis 客户端
var GetRedisCli = func(redisName string) *redis.Client {
	return redisfactory.GetRedisClient(redisName, 0)
}

var ctx = context.Background()

func Del(redisName string, keys ...string) int {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("删除%v失败:%v", keys, "redis客户端为空")
		// panic(err)
		return 0
	}

	result, err := cli.Del(ctx, keys...).Result()
	if err != nil {
		logrus.Errorf("删除%v失败:%v", keys, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func Get(redisName string, key string) string {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("读取%v失败:%v", key, "redis客户端为空")
		// panic(err)
		return ""
	}

	result, err := cli.Get(ctx, key).Result()
	if err != nil {
		if err != redis.Nil {
			logrus.Errorf("读取%v失败:%v", key, err)
			// panic(err)
		}
		return ""
	}
	return result
}

func Set(redisName string, key string, value interface{}, expiration time.Duration) string {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("设置%v失败:%v", key, "redis客户端为空")
		return ""
	}

	result, err := cli.Set(ctx, key, value, expiration).Result()
	if err != nil {
		logrus.Errorf("设置%v失败:%v", key, err)
		return ""
	}
	return result
}

func Expire(redisName string, key string, expiration time.Duration) bool {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("设置%v超时%v失败:%v", key, expiration, "redis客户端为空")
		return false
	}

	result, err := cli.Expire(ctx, key, expiration).Result()
	if err != nil {
		logrus.Errorf("设置%v超时%v失败:%v", key, expiration, err)
		return false
	}
	return result
}

// ZAdd 添加member到ZSet，返回成功添加的个数
func ZAdd(redisName string, key string, member interface{}, score int) int {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("ZAdd %v失败:%v", key, "redis客户端为空")
		return 0
	}

	result, err := cli.ZAdd(ctx, key, &redis.Z{
		Score:  float64(score),
		Member: member,
	}).Result()
	if err != nil {
		logrus.Errorf("ZAdd %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func ZRemRangeByRank(redisName string, key string, start, stop int64) int {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("ZRemRangeByRank %v失败:%v", key, "redis客户端为空")
		return 0
	}

	result, err := cli.ZRemRangeByRank(ctx, key, start, stop).Result()
	if err != nil {
		logrus.Errorf("ZRemRangeByRank %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

// SAdd 添加member到Set，返回成功添加的个数
func SAdd(redisName string, key string, members ...interface{}) int {
	if len(members) == 0 {
		return 0
	}
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("SAdd %v失败:%v", key, "redis客户端为空")
		return 0
	}

	result, err := cli.SAdd(ctx, key, members...).Result()
	if err != nil {
		logrus.Errorf("SAdd %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func SIsMember(redisName string, key string, member interface{}) bool {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("SIsMember %v失败:%v", key, "redis客户端为空")
		return false
	}

	result, err := cli.SIsMember(ctx, key, member).Result()
	if err != nil {
		logrus.Errorf("SIsMember %v失败:%v", key, err)
		// panic(err)
		return false
	}
	return result
}

func SMembers(redisName string, key string) []string {
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("SMembers %v失败:%v", key, "redis客户端为空")
		return nil
	}

	result, err := cli.SMembers(ctx, key).Result()
	if err != nil {
		logrus.Errorf("SMembers %v失败:%v", key, err)
		// panic(err)
		return nil
	}
	return result
}

// ZRem 删除ZSet的member，返回成功删除的个数
func ZRem(redisName string, key string, members ...interface{}) int {
	if len(members) == 0 {
		return 0
	}
	cli := GetRedisCli(redisName)

	if cli == nil {
		logrus.Errorf("ZRem %v失败:%v", key, "redis客户端为空")
		return 0
	}

	result, err := cli.ZRem(ctx, key, members...).Result()
	if err != nil {
		logrus.Errorf("ZRem %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

// SRem 删除Set的member，返回成功删除的个数
func SRem(redisName string, key string, members ...interface{}) int {
	if len(members) == 0 {
		return 0
	}
	cli := GetRedisCli(redisName)

	result, err := cli.SRem(ctx, key, members...).Result()
	if err != nil {
		logrus.Errorf("SRem %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func ZRange(redisName string, key string, start int64, end int64) []string {
	cli := GetRedisCli(redisName)

	result, err := cli.ZRange(ctx, key, start, end).Result()
	if err != nil {
		logrus.Errorf("ZRange %v失败:%v", key, err)
		// panic(err)
		return nil
	}
	return result
}

func ZCount(redisName string, key string, min, max string) int {
	cli := GetRedisCli(redisName)

	result, err := cli.ZCount(ctx, key, min, max).Result()
	if err != nil {
		logrus.Errorf("ZCount %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func ZCard(redisName string, key string) int {
	cli := GetRedisCli(redisName)

	result, err := cli.ZCard(ctx, key).Result()
	if err != nil {
		logrus.Errorf("ZCard %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func HSet(redisName string, key string, field string, value interface{}) {
	cli := GetRedisCli(redisName)

	_, err := cli.HSet(ctx, key, field, value).Result()
	if err != nil {
		logrus.Errorf("HSet %v失败:%v", key, err)
		// panic(err)
		return
	}
}

func HIncrBy(redisName string, key string, field string, incr int64) int {
	cli := GetRedisCli(redisName)

	result, err := cli.HIncrBy(ctx, key, field, incr).Result()
	if err != nil {
		logrus.Errorf("HIncrBy %v, %v失败:%v", key, field, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func HGet(redisName string, key string, field string) string {
	cli := GetRedisCli(redisName)

	result, err := cli.HGet(ctx, key, field).Result()
	if err != nil {
		if err != redis.Nil {
			logrus.Errorf("HGet %v失败:%v", key, err)
			// panic(err)
		}
		return ""
	}
	return result
}

func HMGet(redisName string, key string, fields ...string) []interface{} {
	if len(fields) == 0 {
		return nil
	}
	cli := GetRedisCli(redisName)

	result, err := cli.HMGet(ctx, key, fields...).Result()
	if err != nil {
		if err != redis.Nil {
			logrus.Errorf("HMGet %v失败:%v", key, err)
			// panic(err)
		}
		return nil
	}
	return result
}

func HGetAll(redisName string, key string) map[string]string {
	cli := GetRedisCli(redisName)

	result, err := cli.HGetAll(ctx, key).Result()
	if err != nil {
		if err != redis.Nil {
			logrus.Errorf("HGetAll %v失败:%v", key, err)
			// panic(err)
		}
		return nil
	}
	return result
}

// HDel 删除Hash的member，返回成功删除的个数
func HDel(redisName string, key string, field ...string) int {
	if len(field) == 0 {
		return 0
	}
	cli := GetRedisCli(redisName)

	result, err := cli.HDel(ctx, key, field...).Result()
	if err != nil {
		logrus.Errorf("HDel %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

// LPush 添加member到List，返回成功添加的个数
func LPush(redisName string, key string, member ...interface{}) int {
	cli := GetRedisCli(redisName)

	result, err := cli.LPush(ctx, key, member...).Result()
	if err != nil {
		logrus.Errorf("LPush %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

// RPush 添加member到List，返回成功添加的个数
func RPush(redisName string, key string, member ...interface{}) int {
	cli := GetRedisCli(redisName)

	result, err := cli.RPush(ctx, key, member...).Result()
	if err != nil {
		logrus.Errorf("RPush %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func LLen(redisName string, key string) int {
	cli := GetRedisCli(redisName)

	result, err := cli.LLen(ctx, key).Result()
	if err != nil {
		logrus.Errorf("LLen %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

func LPop(redisName string, key string) string {
	cli := GetRedisCli(redisName)

	result, err := cli.LPop(ctx, key).Result()
	if err != nil {
		logrus.Errorf("LPop %v失败:%v", key, err)
		// panic(err)
		return ""
	}
	return result
}

func RPop(redisName string, key string) string {
	cli := GetRedisCli(redisName)

	result, err := cli.RPop(ctx, key).Result()
	if err != nil {
		logrus.Errorf("RPop %v失败:%v", key, err)
		// panic(err)
		return ""
	}
	return result
}

func Subscribe(redisName string, channel string, onNotify func(message *redis.Message)) {
	defer logdog.RecoverAndLog(fmt.Sprintf("频道%v消息处理出错", channel))
	cli := GetRedisCli(redisName)
	ch := cli.Subscribe(ctx, channel).Channel()
	for {
		select {
		case msg := <-ch:
			onNotify(msg)
		}
	}
}

// SCard 返回set中元素个数
func SCard(redisName string, key string) int {
	cli := GetRedisCli(redisName)

	result, err := cli.SCard(ctx, key).Result()
	if err != nil {
		logrus.Errorf("SCard %v失败:%v", key, err)
		// panic(err)
		return 0
	}
	return int(result)
}

// SPop 弹set中元素个数
func SPop(redisName string, key string) int {
	cli := GetRedisCli(redisName)

	result, err := cli.SPop(ctx, key).Result()
	if err != nil {
		logrus.Errorf("SPop %v失败:%v", key, err)

		return 0
	}

	showID, _ := strconv.Atoi(result)
	return showID
}

// Exists key是否存在
func Exists(redisName string, key string) int {
	cli := GetRedisCli(redisName)

	result, err := cli.Exists(ctx, key).Result()
	if err != nil {
		logrus.Errorf("Exists %v失败:%v", key, err)

		return 0
	}

	// exist, _ := strconv.Atoi(result)
	return int(result)
}
