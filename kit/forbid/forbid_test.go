package forbid

import (
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
	"math/rand"
	"os"
	"regexp"
	"strings"
	"testing"
)

const filePath = "../../../fancy-common/tools/migration/asset/forbid.txt"

func Benchmark_WordsFilter(b *testing.B) {
	file, err := os.ReadFile(filePath)
	if err != nil {
		b.<PERSON><PERSON>("读取屏蔽字文件失败")
		return
	}
	fbWords := strings.Split(string(file), " ")
	for i := 0; i < len(fbWords); i++ {
		if len(fbWords[i]) == 0 {
			fbWords = append(fbWords[:i], fbWords[i+1:]...)
			i--
		}
	}
	wf := New()
	root := wf.Generate(fbWords)
	fbLen := len(fbWords)
	invalid := 0

	for i := 0; i < b.N; i++ {
		wIndex := rand.Int() % fbLen
		word := fbWords[wIndex]
		if len(word) == 0 {
			invalid++
			continue
		}
		if !wf.Contains(word, root) {
			b.<PERSON>("找不到屏蔽词%s index:%d 总长度：%d", word, wIndex, fbLen)
		}
	}
	b.Logf("总长度%d 总次数%d，不可用次数%d", fbLen, b.N, invalid)
}

func TestForbidText(t *testing.T) {
	file, err := os.ReadFile(filePath)
	if err != nil {
		t.Errorf("读取屏蔽词文件失败:%v", err)
		return
	}
	words := regexp.MustCompile("\\s{1,}").Split(string(file), -1)
	fbWords := strings.Split(string(file), " ")
	for i := 0; i < len(fbWords); i++ {
		if len(fbWords[i]) == 0 {
			fbWords = append(fbWords[:i], fbWords[i+1:]...)
			i--
		}
	}
	t.Logf("words:%d fbWords:%d", len(words), len(fbWords))
}

func Benchmark_FilterReplace(b *testing.B) {
	viper.Set(dict.SysWordForbid, filePath)
	InitForbid()
	text := "蛤蟆www"
	for i := 0; i < b.N; i++ {
		FilterForbid(text)
	}
}
