package forbid

import (
	"bufio"
	"bytes"
	"io"
	"os"
	"strings"
	"sync"

	"github.com/sirupsen/logrus"
)

var DefaultPlaceholder = "*"

type WordsFilter struct {
	Placeholder string
	StripSpace  bool
	node        *Node
	mutex       sync.RWMutex
}

func New() *WordsFilter {
	return &WordsFilter{
		Placeholder: DefaultPlaceholder,
		StripSpace:  true,
		node:        NewNode(make(map[string]*Node), ""),
	}
}

func (wf *WordsFilter) Generate(texts []string) map[string]*Node {
	root := make(map[string]*Node)
	for _, text := range texts {
		if len(text) == 0 {
			continue
		}
		wf.Add(text, root)
	}
	return root
}

func (wf *WordsFilter) GenerateWithFile(path string) (map[string]*Node, error) {
	fd, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer func(fd *os.File) {
		errClose := fd.Close()
		if errClose != nil {
			logrus.Errorf("Close fd fail : %v", errClose)
		}
	}(fd)

	buf := bufio.NewReader(fd)
	var texts []string
	for {
		line, _, errBuf := buf.ReadLine()
		if errBuf != nil {
			if errBuf == io.EOF {
				break
			} else {
				return nil, err
			}
		}

		text := strings.TrimSpace(string(line))
		if text == "" {
			continue
		}
		texts = append(texts, text)
	}

	rootGen := wf.Generate(texts)
	return rootGen, nil
}

func (wf *WordsFilter) Add(text string, root map[string]*Node) {
	if wf.StripSpace {
		text = stripSpace(text)
	}
	wf.mutex.Lock()
	defer wf.mutex.Unlock()
	wf.node.add(text, root, wf.Placeholder)
}

func (wf *WordsFilter) Replace(text string, root map[string]*Node) string {
	if wf.StripSpace {
		text = stripSpace(text)
	}
	wf.mutex.RLock()
	defer wf.mutex.RUnlock()
	return wf.node.replace(text, root)
}

func (wf *WordsFilter) Contains(text string, root map[string]*Node) bool {
	if wf.StripSpace {
		text = stripSpace(text)
	}
	wf.mutex.RLock()
	defer wf.mutex.RUnlock()
	return wf.node.contains(text, root)
}

func (wf *WordsFilter) Remove(text string, root map[string]*Node) {
	if wf.StripSpace {
		text = stripSpace(text)
	}
	wf.mutex.Lock()
	defer wf.mutex.Unlock()
	wf.node.remove(text, root)
}

func stripSpace(str string) string {
	fields := strings.Fields(str)
	var bf bytes.Buffer
	for _, field := range fields {
		bf.WriteString(field)
	}
	return bf.String()
}
