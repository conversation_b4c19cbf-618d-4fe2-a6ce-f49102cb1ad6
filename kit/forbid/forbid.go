package forbid

import (
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"os"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// 字典树根节点
var root map[string]*Node

// 过滤器
var filter *WordsFilter

// InitForbid 初始化屏蔽字库
func InitForbid() bool {

	// 敏感字初始化
	forbidPath := dict.SysDefaultForbidPath
	cfgPath := viper.GetString(dict.SysWordForbid)
	if cfgPath != "" {
		if cfgFile := viper.GetString(dict.SysWordForbid); cfgFile != "" {
			forbidPath = cfgFile
		}
	}

	logrus.Infof("[forbid] load forbid start path : %s", forbidPath)
	fil, err := os.ReadFile(forbidPath)
	if err != nil {
		logrus.Errorln("[forbid] 读取屏蔽字文件失败")
		return false
	}

	var fbWords []string
	fbWords = regexp.MustCompile("\\s{1,}").Split(string(fil), -1)
	logrus.Debugf("[forbid] 屏蔽词共:%v", len(fbWords))
	logrus.Debugf("[forbid] Ok 首个:%v, 尾个:%v", fbWords[0], fbWords[len(fbWords)-1])

	filter = New()
	root = filter.Generate(fbWords)

	return true
}

// ExistForbid 检测指定的字符串中是否有屏蔽字
func ExistForbid(data string) bool {
	if filter == nil {
		logrus.Warnln("未初始化屏蔽字")
		return false
	}
	if filter.Contains(data, root) {
		return true
	}
	words := []rune(data)
	text := data
	for _, word := range words {
		text = strings.Replace(text, string(word), "", 1)
		if filter.Contains(text, root) {
			return true
		}
	}
	return false
}

// FilterForbid 过滤屏蔽词
func FilterForbid(data string) string {
	if filter == nil {
		logrus.Debugln("未初始化屏蔽字")
		return data
	}
	return filter.Replace(data, root)
}
