package gray_release

import "strings"

// IsGrayByUserIDTail 根据用户ID计算哈希值
func IsGrayByUserIDTail(userID uint64, tailNumber int) bool {
	tailOfUserID := userID % 10.00
	return uint64(tailNumber) == tailOfUserID
}

func InIPList(ip string, whiteList []string) bool {
	if ip == "" && len(whiteList) == 0 {
		return false
	}
	return strings.Contains(strings.Join(whiteList, ","), ip)
}

func InTagsList(tags []string, whiteList []string) bool {
	if len(tags) == 0 && len(whiteList) == 0 {
		return false
	}

	isGray := false
	for _, tag := range tags {
		if strings.Contains(strings.Join(whiteList, ","), tag) {
			isGray = true
			break
		}
	}

	return isGray
}

func InGrayVersion(version string, grayVersion string) bool {
	if version == "" || grayVersion == "" {
		return false
	}
	return version == grayVersion
}
