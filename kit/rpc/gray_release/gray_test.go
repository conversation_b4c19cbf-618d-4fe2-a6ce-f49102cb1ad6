package gray_release

import "testing"

func TestHashUidTail(t *testing.T) {
	userID := 126711
	grayRatio := 1

	if IsGrayByUserIDTail(uint64(userID), grayRatio) {
		t.Log("用户进入灰度")
	} else {
		t.Log("用户进入正常")
	}
}

func TestInIpList(t *testing.T) {
	ip := "***********"
	var ipList = []string{"***********", "***********", "***********"}
	t.Log(InIPList(ip, ipList))
}

func TestTags(t *testing.T) {
	t.Log(InTagsList([]string{"tester", "payed"}, []string{"tester", "tester2"}))
}
