package rpc

import (
	"context"
	"errors"
	"net/http"
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	rpc "git.keepfancy.xyz/back-end/frameworks/kit/rpc/utility"
	discovery "git.keepfancy.xyz/back-end/frameworks/lib/ha/consul"
	"git.keepfancy.xyz/back-end/frameworks/lib/ip"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	grpcMiddleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpcRecovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	grpcCtxTags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
	grpcValidator "github.com/grpc-ecosystem/go-grpc-middleware/validator"
	grpcPrometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"google.golang.org/grpc/keepalive"

	// "github.com/hashicorp/consul/api"
	"net"
	"runtime/debug"
	"time"

	"google.golang.org/grpc/health/grpc_health_v1"

	consulApi "github.com/hashicorp/consul/api"
	"github.com/sirupsen/logrus"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"

	"github.com/grpc-ecosystem/grpc-gateway/runtime"

	"google.golang.org/grpc"

	// "go.uber.org/zap"
	// "go.uber.org/zap/zapcore"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// HealthImpl health
type HealthImpl struct{}

// Check 健康检查接口，直接返回健康状态，这里也可以有更复杂的健康检查策略，比如根据服务器负载来返回
func (h *HealthImpl) Check(ctx context.Context, req *grpc_health_v1.HealthCheckRequest) (*grpc_health_v1.HealthCheckResponse, error) {
	// logrus.Tracef("Service Check rsp : %s", req.Service)
	return &grpc_health_v1.HealthCheckResponse{
		Status: grpc_health_v1.HealthCheckResponse_SERVING,
	}, nil
}

func (h *HealthImpl) Watch(req *grpc_health_v1.HealthCheckRequest, w grpc_health_v1.Health_WatchServer) error {
	logrus.Tracef("Service Watch rsp : %s ", req.Service)
	return nil
}

var (
	Server *grpc.Server // Server grpc services

	// config文件变化逻辑用
	configChangedFlagIng bool
	mutex                sync.Mutex
)

var (
	// Mux 用于 grpc-gateway 注册和 http 消息路由
	Mux = runtime.NewServeMux()

	// RunningChan 反注册时停止健壮性检查
	RunningChan = make(chan bool, 1)

	// zapLogger   *zap.Logger
	// grpcZapOpts []grpcZap.Option
)

func init() {
	logrus.Trace("初始化Rpc Server")

	// grpc_recovery
	customFunc := func(p interface{}) (err error) {
		logrus.Errorf("%+v %s", p, debug.Stack())
		return status.Errorf(codes.Unknown, "panic triggered: %v", p)
	}

	// Shared options for the logger, with a custom gRPC code to log level function.
	grpcRecoveryOption := []grpcRecovery.Option{
		grpcRecovery.WithRecoveryHandler(customFunc),
	}

	// New a grpc services
	Server = grpc.NewServer(
		grpc.StreamInterceptor(
			grpcMiddleware.ChainStreamServer(
				grpcCtxTags.StreamServerInterceptor(),
				// grpc_opentracing.StreamServerInterceptor(),
				grpcPrometheus.StreamServerInterceptor,
				// grpcZap.StreamServerInterceptor(zapLogger, grpcZapOpts...), // TODO zap
				grpcRecovery.StreamServerInterceptor(grpcRecoveryOption...),
				// StreamServerInterceptor(limiter),
				grpcValidator.StreamServerInterceptor(),
			)),
		grpc.UnaryInterceptor(grpcMiddleware.ChainUnaryServer(
			grpcCtxTags.UnaryServerInterceptor(),
			// grpc_opentracing.UnaryServerInterceptor(),
			grpcPrometheus.UnaryServerInterceptor,
			// grpc_zap.UnaryServerInterceptor(zapLogger, grpcZapOpts...),
			grpcRecovery.UnaryServerInterceptor(grpcRecoveryOption...),
			// UnaryServerInterceptor(limiter),
			grpcValidator.UnaryServerInterceptor(),
		)),
		grpc.KeepaliveParams(keepalive.ServerParameters{
			Time:              rpc.KeepAliveTimeServer,
			Timeout:           rpc.KeepAliveTimeoutServer,
			MaxConnectionIdle: rpc.MaxConnectionIdle,
		}),
		grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
			MinTime:             rpc.KeepAliveMinTime, // 最小Ping时间间隔
			PermitWithoutStream: true,
		}),
	)

	// 开启监控检查
	grpc_health_v1.RegisterHealthServer(Server, &HealthImpl{})

	// // grpc prometheus 注册服务
	// grpcPrometheus.Register(Server)
	// grpcPrometheus.EnableHandlingTimeHistogram()

	// 服务开启后，注册http路由监听
	http.Handle("/grpc/", http.StripPrefix("/grpc", Mux))
}

// GetServerAddr 返回全局grpc服务的监听地址
func GetServerAddr() string {
	return viper.GetString(dict.ConfigRpcAddr) + ":" + viper.GetString(dict.ConfigRpcPort)
}

// StartServer 启动全局 grpc 服务。
// 启动服务成功后，将向 consul 注册服务地址，
func StartServer() error {

	// grpc日志设置
	// zapLogger = logzap.InitLogger("grpc_server.log")
	// grpcZap.ReplaceGrpcLoggerV2(zapLogger)

	serverAddr := GetServerAddr()

	name := viper.GetString(dict.ConfigRpcServerName)
	if len(name) == 0 {
		logrus.Errorf("not config key : rpc_server_name")
		return errors.New("not config key : rpc_server_name")
	}

	// 地址和端口
	rpcIP := dict.SysDefaultConsulAddr
	if viper.IsSet(dict.ConfigRpcAddr) {
		rpcIP = viper.GetString(dict.ConfigRpcAddr)
	} else {
		rpcIP = ip.LocalIP()
	}
	rpcPort := viper.GetInt(dict.ConfigRpcPort)

	lis, err := net.Listen(dict.SysWordTcp, serverAddr)
	if err != nil {
		logrus.WithError(err).Errorf("监听地址 %s 失败", serverAddr)
		return errors.New("监听端口失败" + serverAddr)
	}

	// 服务标签
	tags := viper.GetString(dict.ConfigRpcServerTags)
	tagsSlice := transform.StrSplit2Slice(tags, dict.SysSymbolVerticalLine)

	// 权重设置
	weight := 0
	if viper.IsSet(dict.ConfigRpcServerWeights) {
		weight = viper.GetInt(dict.ConfigRpcServerWeights)
	}

	// Notice：Consul服务注册, 这里如果框架采用etcd或其它,可以修改这部分
	cs := &discovery.ConsulService{
		Name: name,
		Tag:  tagsSlice,
		IP:   rpcIP,
		Port: rpcPort,
	}

	if weight > 0 {
		cs.Weights = weight
	}

	client, err := registerToConsul(cs)
	if err != nil {
		logrus.WithError(err).Errorf("注册失败")
		return errors.New("注册服务失败")
	}
	logrus.Tracef("Srv-Consul Status : %v", client.Status())

	done := make(chan struct{})
	// yaml发生了变更
	fileModify := make(chan bool)
	go func() {
		viper.WatchConfig()
		viper.OnConfigChange(func(event fsnotify.Event) {
			mutex.Lock()
			defer mutex.Unlock()

			if configChangedFlagIng {
				return
			}

			tagsNew := viper.GetString(dict.ConfigRpcServerTags)
			if tagsNew != tags {
				logrus.Infof("Detect config context change: %s ", event.String())
				fileModify <- true
			}
		})
	}()

	go func() {

		// viper.WatchConfig()
		ticker := time.NewTicker(discovery.TTLHealthCheck / 2)
		defer ticker.Stop()

		// retryTimeout := 1
		for {
			select {
			case <-done:
				logrus.Infof("服务结束")
				return
			case <-RunningChan:
				logrus.Infof("服务已被反注册，退出健康检查")
				return
			case <-fileModify:
				tagsModify := viper.GetString(dict.ConfigRpcServerTags)
				if tagsModify != tags {
					logrus.Infof("配置变更后Tags：%s", tagsModify)
					tagsSlice = transform.StrSplit2Slice(tagsModify, dict.SysSymbolVerticalLine)
					cs.Tag = tagsSlice
					client, err = registerToConsul(cs)
					if err != nil {
						logrus.WithError(err).Errorf("注册失败")
						configChangedFlagIng = false
						return
					}
					tags = tagsModify
				}

				configChangedFlagIng = false

			case <-ticker.C:
				// id := discovery.FormatServerID(cs)
				// errUpdate := client.Agent().UpdateTTL(id, "一切正常", "passing")
				// if errUpdate != nil {
				// 	logrus.Errorf("健康检查失败 : %v", errUpdate)
				// 	retryTimeout++
				// 	if retryTimeout > 3 {
				// 		// 每3 * TTL之后尝试注册服务，
				// 		// 尝试注册服务
				// 		_, err = registerToConsul(cs)
				// 		if err != nil {
				// 			logrus.WithError(err).Errorln("尝试注册服务失败")
				// 		}
				// 		retryTimeout = 0
				// 	} else if retryTimeout >= 9 {
				// 		ticker.Stop()
				// 		logrus.Errorf("RPC服务(%s)服务检查重连失败", name)
				// 	}
				// }
			}
		}
	}()
	defer close(done)

	logrus.Infof("RPC服务(%s)注册完成", name)

	return Server.Serve(lis)
}

// StopServer 停止 grpc 服务
func StopServer() {
	// DeregisterServer()
	Server.GracefulStop()
}

// handle 注册的时候 加上tag标签  用于流量染色
func registerToConsul(cs *discovery.ConsulService) (*consulApi.Client, error) {

	consulAddr := viper.GetString(dict.ConfigConsulAddr)
	client, err := discovery.RegisterService(consulAddr, cs)

	logrus.Infof("registerToConsul : %s", consulAddr)

	if err != nil {
		logrus.WithError(err).Errorln("registerToConsul : 注册失败")
		return nil, errors.New("registerToConsul : 注册失败")
	}

	return client, nil
}

// DeregisterServer 反注册
func DeregisterServer() {

	consulAddr := viper.GetString(dict.ConfigConsulAddr)
	name := viper.GetString(dict.ConfigRpcServerName)
	rpcIP := viper.GetString(dict.ConfigRpcAddr)
	rpcPort := viper.GetInt(dict.ConfigRpcPort)

	bSuccess := discovery.DeregisterService(consulAddr, &discovery.ConsulService{
		Name: name,
		IP:   rpcIP,
		Port: rpcPort,
	})

	if bSuccess {
		RunningChan <- true
		logrus.Infof("反注册服务成功")
	} else {
		logrus.Errorf("反注册服务失败")
	}
}
