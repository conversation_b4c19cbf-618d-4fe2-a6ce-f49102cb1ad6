package main

import (
	"context"
	"flag"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	grpc "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/demo/api"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/ip"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"strconv"
	"sync"
)

var SerName = "tCloudSrv"

type ApiService struct{}

var nIndex = 0
var instanceFlg = ""

func (a ApiService) ApiTest(ctx context.Context, request *api.Request) (*api.Response, error) {
	nIndex++
	opt := interceptor.GetRPCOptions(ctx)
	fmt.Println("Listener Port(" + instanceFlg + " Count>>>:" + strconv.Itoa(nIndex) + " uuid" + transform.Uint642Str(opt.PlayerId) + ")  Req:" + request.String())
	return &api.Response{Output: instanceFlg + " uuid:" + transform.Uint642Str(opt.PlayerId)}, nil
}

func main() {
	var tPort, weight, color, consulAddr string
	flag.StringVar(&tPort, "p", "8088", "port")
	flag.StringVar(&weight, "w", "1", "weight")
	flag.StringVar(&color, "c", "normal", "color")
	flag.StringVar(&consulAddr, "consul", "************:8500", "consul")
	flag.Parse()

	viper.Set("rpc_server_name", SerName)
	viper.Set("rpc_addr", ip.LocalIP()) // 假设将RPC名称学到环境变量，实际读取配置
	viper.Set("rpc_port", tPort)
	viper.Set("weight", weight)
	viper.Set("rpc_server_tags", color)
	viper.Set(dict.ConfigConsulAddr, consulAddr)

	viper.AutomaticEnv() // 匹配环境变量

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := grpc.StartServer(); err != nil {
			panic(err)
		}
		logrus.Infof("关闭 intranet 服务完成")
	}()

	// 注册服务
	var srv = &ApiService{}
	api.RegisterApiServer(grpc.Server, srv)

	wg.Wait()
}
