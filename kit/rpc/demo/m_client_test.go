package main

import (
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	grpc "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/demo/api"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"github.com/spf13/viper"
	"strconv"
	"sync"
	"testing"
	"time"
)

var wg = sync.WaitGroup{}

func TestDiscoveryParallel(t *testing.T) {
	viper.Set(dict.ConfigConsulAddr, "************:8500")

	beginTime := time.Now()
	// maxTimes := 1000 * 100
	maxTimes := 1
	var times int32 = 0
	infoMap := make(map[string]int32, maxTimes)
	wg.Add(1)
	safego.Go(func() {
		for {
			if times >= int32(maxTimes) {
				return
			}

			conn, errGetConn := grpc.GetConnection(SerName)
			if errGetConn != nil {
				panic(errGetConn)
			}

			rpcCtx := interceptor.NewRpcClientCtx(
				interceptor.WithAppVersion("0.0.1"),
				interceptor.WithPlayerId(100001),
				interceptor.WithTag("gray"))

			apiClient := api.NewApiClient(conn)
			res, err := apiClient.ApiTest(rpcCtx, &api.Request{Input: "req index : " + strconv.Itoa(int(times))})
			if err != nil {
				t.Log(err)
				// return
			}
			t.Log("rsp：", res)

			infoMap[res.Output] = infoMap[res.Output] + 1

			if times%10 == 0 {
				fmt.Println(infoMap)
			}
			times++
		}
	})

	t.Logf("over: times - %d, rt - %d", times, time.Since(beginTime))
	wg.Wait()
	time.Sleep(time.Second * 2)
}

func TestConn(t *testing.T) {
	viper.Set(dict.ConfigConsulAddr, "************:8500")

	cc, err := grpc.GetConnection("webapi")
	if err != nil {
		t.Error(err)
	}
	t.Log(cc)
}
