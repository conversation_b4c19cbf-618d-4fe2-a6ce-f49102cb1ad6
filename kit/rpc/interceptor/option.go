package interceptor

import (
	"fmt"

	"git.keepfancy.xyz/back-end/frameworks/dict"
)

type options struct {
	TraceId     string // 链路追踪ID
	Tag         string // 染色标签
	PlayerId    uint64 // 请求Player
	ProductId   int32  // 产品线id
	ChannelType int32  // 渠道类型
	Platform    int32  // 平台
	AppLanguage int32  // 用户语言
	Country     string // 国家信息
	AccType     int32  // 账号类型
	AppVersion  string // 客户端版本
	ClientID    uint64 // 客户端id
}

func (p *options) String() string {
	return fmt.Sprintf("%v", *p)
}

// Option 表示获取配置的选项
type Option func(options *options)

func newOptions(opt ...Option) *options {
	opts := &options{
		Tag: dict.SysWordGrayNormal,
	}

	for _, o := range opt {
		o(opts)
	}
	return opts
}

// WithTraceId 指定 链路追踪Id
func WithTraceId(traceId string) Option {
	return func(options *options) {
		options.TraceId = traceId
	}
}

// WithTag 指定灰度标签
func WithTag(tag string) Option {
	return func(options *options) {
		options.Tag = tag
	}
}

// WithPlayerId 指定用户id
func WithPlayerId(playerId uint64) Option {
	return func(options *options) {
		options.PlayerId = playerId
	}
}

// WithProductId 指定产品
func WithProductId(productId int32) Option {
	return func(options *options) {
		options.ProductId = productId
	}
}

// WithChannelType 指定渠道
func WithChannelType(channel int32) Option {
	return func(options *options) {
		options.ChannelType = channel
	}
}

// WithPlatform 指定平台
func WithPlatform(platform int32) Option {
	return func(options *options) {
		options.Platform = platform
	}
}

// WithAppLanguage 指定用户语言
func WithAppLanguage(op int32) Option {
	return func(options *options) {
		options.AppLanguage = op
	}
}

// WithCountry 指定国家
func WithCountry(op string) Option {
	return func(options *options) {
		options.Country = op
	}
}

// WithAccType 指定用户语言
func WithAccType(op int32) Option {
	return func(options *options) {
		options.AccType = op
	}
}

// WithAppVersion 指定AppVersion
func WithAppVersion(version string) Option {
	return func(options *options) {
		options.AppVersion = version
	}
}

func WithClientID(op uint64) Option {
	return func(options *options) {
		options.ClientID = op
	}
}
