package interceptor

import (
	"context"
	"google.golang.org/grpc"
)

// GrpcClientHeaderCarrierInterceptor 处理客户端携带信息
func GrpcClientHeaderCarrierInterceptor() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {

		ctxNew := GetCtxFromMetadata(ctx)
		return invoker(ctxNew, method, req, reply, cc, opts...)
	}
}
