package interceptor

import (
	"context"
	grpcPrometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/status"
	"strings"
)

// 直接采用系统 go-grpc-prometheus
// grpcPrometheus.UnaryClientInterceptor
// go-grpc-prometheus@v1.2.0
// https://github.com/grpc-ecosystem/go-grpc-prometheus

/**
xxx_started_total：x端启动的rpc总数
xxx_client_handled_total：GRPCx端处理总数
xxx_client_msg_received_total： x端接收到的RPC流消息总数。
xxx_client_msg_sent_total：x端发送的gRPC流消息总数。
xxx_client_handling_seconds：gRPC在应用程序完成之前的响应延迟(秒)的直方图。

客户端指标：
	return &ClientMetrics{
		clientStartedCounter: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_client_started_total",
				Help: "Total number of RPCs started on the client.",
			}), []string{"grpc_type", "grpc_service", "grpc_method"}),

		clientHandledCounter: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_client_handled_total",
				Help: "Total number of RPCs completed by the client, regardless of success or failure.",
			}), []string{"grpc_type", "grpc_service", "grpc_method", "grpc_code"}),

		clientStreamMsgReceived: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_client_msg_received_total",
				Help: "Total number of RPC stream messages received by the client.",
			}), []string{"grpc_type", "grpc_service", "grpc_method"}),

		clientStreamMsgSent: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_client_msg_sent_total",
				Help: "Total number of gRPC stream messages sent by the client.",
			}), []string{"grpc_type", "grpc_service", "grpc_method"}),

		clientHandledHistogramEnabled: false,
		clientHandledHistogramOpts: prom.HistogramOpts{
			Name:    "grpc_client_handling_seconds",
			Help:    "Histogram of response latency (seconds) of the gRPC until it is finished by the application.",
			Buckets: prom.DefBuckets,
		},
		clientHandledHistogram: nil,
	}

服务端指标：
	return &ServerMetrics{
		serverStartedCounter: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_server_started_total",
				Help: "Total number of RPCs started on the server.",
			}), []string{"grpc_type", "grpc_service", "grpc_method"}),
		serverHandledCounter: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_server_handled_total",
				Help: "Total number of RPCs completed on the server, regardless of success or failure.",
			}), []string{"grpc_type", "grpc_service", "grpc_method", "grpc_code"}),
		serverStreamMsgReceived: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_server_msg_received_total",
				Help: "Total number of RPC stream messages received on the server.",
			}), []string{"grpc_type", "grpc_service", "grpc_method"}),
		serverStreamMsgSent: prom.NewCounterVec(
			opts.apply(prom.CounterOpts{
				Name: "grpc_server_msg_sent_total",
				Help: "Total number of gRPC stream messages sent by the server.",
			}), []string{"grpc_type", "grpc_service", "grpc_method"}),
		serverHandledHistogramEnabled: false,
		serverHandledHistogramOpts: prom.HistogramOpts{
			Name:    "grpc_server_handling_seconds",
			Help:    "Histogram of response latency (seconds) of gRPC that had been application-level handled by the server.",
			Buckets: prom.DefBuckets,
		},
		serverHandledHistogram: nil,
	}
*/

var (
	ClientWithBizHandledCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "monitor_grpc_client_accept_result_total",
			Help: "Total number of RPCs accept on the client, regardless of success or failure.",
		}, []string{"grpc_type", "grpc_service", "grpc_method", "grpc_code", "result_code"})
)

const unknown = "unknown"

// SplitGrpcMethodName split grpc full method into service and method.
func SplitGrpcMethodName(fullMethodName string) (service string, method string) {
	fullMethodName = strings.TrimPrefix(fullMethodName, "/") // remove leading slash
	if i := strings.Index(fullMethodName, "/"); i >= 0 {
		return fullMethodName[:i], fullMethodName[i+1:]
	}
	return unknown, unknown
}

func MetricUnaryClientInterceptor(successCode string) grpc.UnaryClientInterceptor {
	grpcPrometheus.EnableClientHandlingTimeHistogram()
	originMw := grpcPrometheus.UnaryClientInterceptor

	return func(ctx context.Context, method string,
		req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {

		err := originMw(ctx, method, req, reply, cc, invoker, opts...)
		st, _ := status.FromError(err)

		service, methodName := SplitGrpcMethodName(method)

		ClientWithBizHandledCounter.WithLabelValues("unary",
			service, methodName, st.Code().String(), successCode).Inc()

		return err
	}
}
