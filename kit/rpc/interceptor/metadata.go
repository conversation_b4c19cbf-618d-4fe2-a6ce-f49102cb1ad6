package interceptor

import (
	"context"
	"strings"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/google/uuid"
	"google.golang.org/grpc/metadata"
)

type mp map[string]string

func (m mp) Get(key string) string {
	k := strings.ToLower(key)
	return m[k]
}

func parseMeta(md metadata.MD) mp {
	parseMap := make(mp)
	for key, val := range md {
		if len(val) > 0 {
			parseMap[key] = val[0]
		}
	}
	return parseMap
}

// GetCtxFromMetadata 获取gRPC传递的元数据
func GetCtxFromMetadata(rCtx context.Context) context.Context {

	var md metadata.MD
	if m, ok := metadata.FromOutgoingContext(rCtx); ok {
		md = m.Copy()
	}else if m, ok := metadata.FromIncomingContext(rCtx); ok {
		md = m.Copy()
	} else {
		md = metadata.MD{}
	}
	parseMap := parseMeta(md)

	ctx := context.Background()
	if traceId := parseMap.Get(dict.SysWordTraceID); traceId == "" {
		parseMap[dict.SysWordTraceID] = uuid.Must(uuid.NewUUID()).String()
	}
	md = metadata.Pairs(
		dict.SysWordTraceID, parseMap.Get(dict.SysWordTraceID),
		dict.SysWordColor, parseMap.Get(dict.SysWordColor),
		dict.SysWordPlayerID, parseMap.Get(dict.SysWordPlayerID),
		dict.SysWordProductID, parseMap.Get(dict.SysWordProductID),
		dict.SysWordChannelID, parseMap.Get(dict.SysWordChannelID),
		dict.SysWordPlatform, parseMap.Get(dict.SysWordPlatform),
		dict.SysWordAppLanguage, parseMap.Get(dict.SysWordAppLanguage),
		dict.SysWordCountry, parseMap.Get(dict.SysWordCountry),
		dict.SysWordAccType, parseMap.Get(dict.SysWordAccType),
		dict.SysWordAppVersion, parseMap.Get(dict.SysWordAppVersion),
		dict.SysWordClientID, parseMap.Get(dict.SysWordClientID),
	)

	ctxNew := metadata.NewOutgoingContext(ctx, md)
	return ctxNew
}

// GetRPCOptions 获取上下文内容
func GetRPCOptions(ctx context.Context) *options {
	md, _ := metadata.FromIncomingContext(ctx)
	if md == nil { // 保底解析
		md, _ = metadata.FromOutgoingContext(ctx)
	}
	parseMap := parseMeta(md)
	opt := &options{}
	if data := parseMap.Get(dict.SysWordTraceID); data != "" {
		opt.TraceId = data
	}
	if data := parseMap.Get(dict.SysWordColor); data != "" {
		opt.Tag = data
	}
	if data := parseMap.Get(dict.SysWordPlayerID); data != "" {
		opt.PlayerId = transform.Str2Uint64(data)
	}
	if data := parseMap.Get(dict.SysWordProductID); data != "" {
		opt.ProductId = transform.Str2Int32(data)
	}
	if data := parseMap.Get(dict.SysWordChannelID); data != "" {
		opt.ChannelType = transform.Str2Int32(data)
	}
	if data := parseMap.Get(dict.SysWordPlatform); data != "" {
		opt.Platform = transform.Str2Int32(data)
	}
	if data := parseMap.Get(dict.SysWordAppLanguage); data != "" {
		opt.AppLanguage = transform.Str2Int32(data)
	}
	if data := parseMap.Get(dict.SysWordCountry); data != "" {
		opt.Country = data
	}
	if data := parseMap.Get(dict.SysWordAccType); data != "" {
		opt.AccType = transform.Str2Int32(data)
	}
	if data := parseMap.Get(dict.SysWordAppVersion); data != "" {
		opt.AppVersion = data
	}
	if data := parseMap.Get(dict.SysWordClientID); data != "" {
		opt.ClientID = transform.Str2Uint64(data)
	}

	return opt
}

// NewRpcClientCtx RPC上下文
// RPC客户端调用
func NewRpcClientCtx(opts ...Option) context.Context {
	opt := newOptions(opts...)
	traceId := uuid.Must(uuid.NewUUID()).String()
	ctx := context.Background()
	md := metadata.Pairs(
		dict.SysWordTraceID, traceId,
		dict.SysWordColor, opt.Tag,
		dict.SysWordPlayerID, transform.Uint642Str(opt.PlayerId),
		dict.SysWordProductID, transform.I32ToStr(opt.ProductId),
		dict.SysWordChannelID, transform.I32ToStr(opt.ChannelType),
		dict.SysWordPlatform, transform.I32ToStr(opt.Platform),
		dict.SysWordAppLanguage, transform.I32ToStr(opt.AppLanguage),
		dict.SysWordCountry, opt.Country,
		dict.SysWordAccType, transform.I32ToStr(opt.AccType),
		dict.SysWordAppVersion, opt.AppVersion,
		dict.SysWordClientID, transform.Uint642Str(opt.ClientID),
	)
	ctxNew := metadata.NewOutgoingContext(ctx, md)
	return ctxNew
}
