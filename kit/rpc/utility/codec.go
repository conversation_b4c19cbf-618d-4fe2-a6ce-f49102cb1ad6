package rpc

import (
	"errors"
	"fmt"
)

type RawCodec struct {
}

// Marshal 打包
func (c RawCodec) Marshal(v interface{}) ([]byte, error) {
	out, ok := v.(*[]byte)
	if !ok {
		return nil, errors.New("RawCodec Marshal error")
	}
	return *out, nil
}

// Unmarshal 解包
func (c RawCodec) Unmarshal(data []byte, v interface{}) error {
	_, ok := v.(*[]byte)
	if !ok {
		return errors.New("RawCodec Unmarshal error")
	}
	// v = data
	*(v.(*[]byte)) = data
	return nil
}

// String 打印
func (c RawCodec) String() string {
	return fmt.Sprintf("proxy>%s", "raw")
}
