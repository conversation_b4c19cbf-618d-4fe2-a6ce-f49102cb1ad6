package rpc

import "time"

const (
	LBWeighted   = "weight"      // LBWeighted is the name of weight balancer.
	LBPickFirst  = "pick_first"  // LBWeighted is the name of pick_first balancer.
	LBRoundRobin = "round_robin" // LBWeighted is the name of round_robin balancer.
)

const (
	ParamRetryTimes      = 0 // 重试次数
	ParamPerRetryTimeout = 3 // 每次Retry TimeOut时间
)

const (
	// DialTimeout the timeout of create connection
	DialTimeout = 5 * time.Second

	// // BackoffMaxDelay provided maximum delay when backing off after failed connection attempts.
	// BackoffMaxDelay = 3 * time.Second

	// KeepAliveTime is the duration of time after which if the client doesn't see
	// any activity it pings the services to see if the transport is still alive.
	// KeepAliveTime = time.Duration(20) * time.Second
	KeepAliveTime       = time.Duration(40) * time.Second // 客户端的 Time 设置为稍长一些，以减少 PING 请求的频率
	KeepAliveTimeServer = time.Duration(30) * time.Second

	// KeepAliveTimeout is the duration of time for which the client waits after having
	// pinged for keepalive check and if no activity is seen even after that the connection
	// is closed.
	KeepAliveTimeout       = time.Duration(20) * time.Second
	KeepAliveTimeoutServer = time.Duration(30) * time.Second // 考虑到客户端可能网络延迟，设置比Client长点

	// MaxConnectionIdle 连接空闲N秒后关闭
	MaxConnectionIdle = time.Duration(10) * time.Minute // 最小PING心跳时间
	KeepAliveMinTime  = 10 * time.Second
	// // InitialWindowSize we set it 1GB is to provide system's throughput.
	// InitialWindowSize = 1 << 30
	//
	// // InitialConnWindowSize we set it 1GB is to provide system's throughput.
	// InitialConnWindowSize = 1 << 30

	// // MaxSendMsgSize set max gRPC request message size sent to services.
	// // If any request message size is larger than current value, an error will be reported from gRPC.
	// MaxSendMsgSize = 4 << 30
	//
	// // MaxRecvMsgSize set max gRPC receive message size received from services.
	// // If any message size is larger than current value, an error will be reported from gRPC.
	// MaxRecvMsgSize = 4 << 30

	// Success 通用gRPC code码 自定义
	Success = "200"
)
