package rpc

import (
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	grpcMiddleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpcRetry "github.com/grpc-ecosystem/go-grpc-middleware/retry"
	grpcPrometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"time"
)

// NewGrpcConnByAddr Dial指定地址rpc服务端
func NewGrpcConnByAddr(addr string) (*grpc.ClientConn, error) {

	ctx, cancel := context.WithTimeout(context.Background(), DialTimeout)
	defer cancel()

	conn, err := grpc.DialContext(
		ctx,
		addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultServiceConfig(fmt.Sprintf(`{"LoadBalancingPolicy": "%s"}`, LBWeighted)), // 权重

		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                KeepAliveTime,
			Timeout:             KeepAliveTimeout,
			PermitWithoutStream: true,
		}),

		grpc.WithUnaryInterceptor(
			grpcMiddleware.ChainUnaryClient(
				grpcPrometheus.UnaryClientInterceptor,
				interceptor.GrpcClientHeaderCarrierInterceptor(),  // 统一拦截添加请求信息
				interceptor.MetricUnaryClientInterceptor(Success), // Prometheus统计
				grpcRetry.UnaryClientInterceptor(
					grpcRetry.WithBackoff(grpcRetry.BackoffLinear(time.Duration(1)*time.Millisecond)), // 重试间隔时间
					grpcRetry.WithMax(ParamRetryTimes),                                                // 重试次数
					grpcRetry.WithPerRetryTimeout(time.Duration(ParamPerRetryTimeout)*time.Second),    // 重试时间
					grpcRetry.WithBackoff(grpcRetry.BackoffLinearWithJitter(time.Second/2, 0.2)),
					grpcRetry.WithCodes(codes.ResourceExhausted, codes.Unavailable),
				),
			),
		))
	return conn, err
}

// NewGrpcConnByRobin 根据服务名获取Grpc拨号连接
func NewGrpcConnByRobin(target string) (*grpc.ClientConn, error) {
	ctx, cancel := context.WithTimeout(context.Background(), DialTimeout)
	defer cancel()

	conn, err := grpc.DialContext(
		ctx,
		target,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultServiceConfig(fmt.Sprintf(`{"LoadBalancingPolicy": "%s"}`, LBRoundRobin)), // 权重

		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                KeepAliveTime,    // 发送 PING 消息的时间间隔
			Timeout:             KeepAliveTimeout, // 等待 PING ACK 消息的超时时间
			PermitWithoutStream: true,             // 允许在没活动流的情况下发送PING
		}),
		grpc.WithUnaryInterceptor(
			grpcMiddleware.ChainUnaryClient(
				grpcPrometheus.UnaryClientInterceptor,

				// grpc_opentracing.UnaryClientInterceptor(),
				// grpc_zap.UnaryClientInterceptor(zapLogger, opts...),
				interceptor.GrpcClientHeaderCarrierInterceptor(), // 统一拦截添加请求信息
				interceptor.MetricUnaryClientInterceptor(Success),
				grpcRetry.UnaryClientInterceptor(
					grpcRetry.WithBackoff(grpcRetry.BackoffLinear(time.Duration(1)*time.Millisecond)), // 重试间隔时间
					grpcRetry.WithMax(ParamRetryTimes),                                                // 重试次数
					grpcRetry.WithPerRetryTimeout(time.Duration(ParamPerRetryTimeout)*time.Second),    // 重试时间
					// 返回码为如下值时重试
					grpcRetry.WithBackoff(grpcRetry.BackoffLinearWithJitter(time.Second/2, 0.2)),
					grpcRetry.WithCodes(codes.ResourceExhausted, codes.Unavailable),
				),
			),
		))

	if err != nil {
		logrus.WithError(err).Errorf("grpc dial 失败，target: %s", target)
		return nil, fmt.Errorf("dial 失败")
	}

	// logrus.Infof("NewGrpcConnByRobin 连接 %s", target)
	return conn, err
}
