package rpc

import (
	"fmt"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/env"
	rpc "git.keepfancy.xyz/back-end/frameworks/kit/rpc/utility"
	discovery "git.keepfancy.xyz/back-end/frameworks/lib/ha/consul"
	consul "git.keepfancy.xyz/back-end/frameworks/lib/ha/consul/grpc_resolver_consul"
	"github.com/sirupsen/logrus"
	"go.uber.org/zap/zapcore"

	"go.uber.org/zap"
	"google.golang.org/grpc"

	grpcZap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap"
)

var (
	connMap sync.Map
	opts    []grpcZap.Option
)

func init() {

	opts = []grpcZap.Option{
		grpcZap.WithDurationField(func(duration time.Duration) zapcore.Field {
			return zap.Int64("grpc.time_ns", duration.Nanoseconds())
		}),
	}

	// 初始化Consul服务注册发现的resolver
	discovery.InitResolver()
}

// GetConnectionByAddr 获取到目标地址的grpc连接。
func GetConnectionByAddr(addr string) (*grpc.ClientConn, error) {
	x, ok := connMap.Load(addr)
	if ok {
		return x.(*grpc.ClientConn), nil
	}

	conn, err := rpc.NewGrpcConnByAddr(addr)
	if err != nil {
		logrus.WithError(err).Errorf("grpc dial 失败，addr: %s", addr)
		return nil, fmt.Errorf("dial 失败")
	}

	connMap.Store(addr, conn)
	return conn, nil
}

// GetConnection 获取到目标服务的grpc连接。
func GetConnection(name string) (*grpc.ClientConn, error) {
	// 这里不需要判断是否有debug标签的服务
	// 在watchConsulService中默认fallback 也就是优先取debug的服务，找不到返回正常服务
	if env.DeployEnv == env.DeployEnvDev {
		return GetConnectionWithDyeTag(name, dict.SysWordGrayDebug)
	}
	return GetConnectionByRobin(name)
}

func GetConnectionByRobin(name string) (*grpc.ClientConn, error) {
	target := consul.FormatTarget(name)
	x, ok := connMap.Load(target)
	if ok {
		return x.(*grpc.ClientConn), nil
	}

	conn, err := rpc.NewGrpcConnByRobin(target)
	if err != nil {
		logrus.WithError(err).Errorf("grpc dial 失败，target: %s", target)
		return nil, fmt.Errorf("dial 失败")
	}
	logrus.Infof("GetConnection Connect : %s 成功", target)

	connMap.Store(target, conn)
	return conn, nil
}

// GetConnectionWithDyeTag 带tag染色，获取到目标服务的grpc连接。
func GetConnectionWithDyeTag(name string, tag string) (*grpc.ClientConn, error) {
	target := consul.FormatTagTarget(name, tag)

	x, ok := connMap.Load(target)
	if ok {
		return x.(*grpc.ClientConn), nil
	}

	conn, err := rpc.NewGrpcConnByAddr(target)
	if err != nil {
		logrus.WithError(err).Errorf("grpc dial 失败，target: %s", target)
		return nil, fmt.Errorf("dial 失败")
	}
	logrus.Infof("GRPC Connect With Tag : %s", target)

	connMap.Store(target, conn)
	return conn, nil
}
