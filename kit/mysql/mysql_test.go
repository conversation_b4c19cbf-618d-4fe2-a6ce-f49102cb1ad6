package mysql

import (
	"fmt"
	"github.com/ldy105cn/xorm"
	"testing"

	"github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
)

func TestParamLong(t *testing.T) {
	show("aaa", "bbb", 0, "ccc")
}

func show(param ...interface{}) {
	str := fmt.Sprintf("%v", param)
	_ = str
	showShort(param)
}

func showShort(param interface{}) {
	str := fmt.Sprintf("%v", param)
	_ = str
}

func Test__(t *testing.T) {
	conf := mysql.Config{
		User:   "root",
		Passwd: "123456",
		Net:    "tcp",
		Addr:   "localhost:3306",
		DBName: "database1",
		Params: map[string]string{"charset": "utf8"},
	}
	dsn := conf.FormatDSN()
	fmt.Println(dsn)
	engine, err := xorm.NewEngine("mysql", dsn)
	assert.Nil(t, err)
	assert.NotNil(t, engine)
	assert.Nil(t, engine.Ping())
}
