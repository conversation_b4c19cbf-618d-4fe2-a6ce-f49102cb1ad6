package mysql

import (
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/dict"

	"github.com/go-sql-driver/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"xorm.io/core"
)

type EngineMgrImp struct {
	engines  sync.Map // name -> engine
	closeCh  chan *xorm.Engine
	configs  map[string]mysql.Config
	initOnce sync.Once
}

// defaultEngineMgr 默认管理器对象
var defaultEngineMgr = &EngineMgrImp{
	configs: make(map[string]mysql.Config, 8),
}

// GetDefaultMgr 获取 mysql engine 管理器
func GetDefaultMgr() *EngineMgrImp {
	defaultEngineMgr.initOnce.Do(defaultEngineMgr.init)
	return defaultEngineMgr
}

// GetMysqlEngine 返回指定名称的 mysql 引擎
func GetMysqlEngine(name string) *xorm.Engine {
	engine, err := GetDefaultMgr().GetEngine(name)
	if err != nil {
		panic(fmt.Sprintf("failed to GetEngine, name: %s err: %v", name, err))
	}
	return engine
}

func (mg *EngineMgrImp) runCloseDB(ch chan *xorm.Engine) {
	for one := range ch {
		time.Sleep(time.Minute)
		err := one.Close()
		if err != nil {
			return
		}
	}
}

// init 初始化
func (mg *EngineMgrImp) init() {
	mg.closeCh = make(chan *xorm.Engine, 1000)
	go mg.runCloseDB(mg.closeCh)

	allConfigs := viper.GetStringMap(dict.ConfigMysqlList)
	for name, cf := range allConfigs {
		configs := cast.ToStringMap(cf)
		conf := mysql.Config{
			User:                 cast.ToString(configs[dict.ConfigMysqlUser]),
			Passwd:               cast.ToString(configs[dict.ConfigMysqlPwd]),
			Net:                  dict.SysWordTcp,
			Addr:                 cast.ToString(configs[dict.ConfigMysqlAddr]),
			DBName:               cast.ToString(configs[dict.ConfigMysqlDb]),
			AllowNativePasswords: true,
			Params:               map[string]string{dict.ConfigMysqlCharset: "utf8mb4"},
			Loc:                  time.Local,
		}

		if v, ok := os.LookupEnv(strings.ToUpper(name + "_MYSQL_USER")); ok {
			conf.User = v
		}

		if v, ok := os.LookupEnv(strings.ToUpper(name + "_MYSQL_PASSWORD")); ok {
			conf.Passwd = v
		}

		if v, ok := os.LookupEnv(strings.ToUpper(name + "_MYSQL_ADDR")); ok {
			conf.Addr = v
		}

		if v, ok := os.LookupEnv(strings.ToUpper(name + "_MYSQL_DATABASE_NAME")); ok {
			conf.DBName = v
		}
		mg.configs[name] = conf
	}

	// logrus.Debugf("mysql 配置列表加载完成[%+v]", mg.configs)
	logrus.Infoln("Mysql 配置列表加载完成")
}

// GetMysqlConfig 获取 mysql 配置
func (mg *EngineMgrImp) GetMysqlConfig(name string) mysql.Config {
	conf, ok := mg.configs[name]
	if ok {
		return conf
	}
	return mysql.Config{
		User:                 viper.GetString(name + "_mysql_user"),
		Passwd:               viper.GetString(name + "_mysql_password"),
		Net:                  dict.SysWordTcp,
		Addr:                 viper.GetString(name + "_mysql_addr"),
		DBName:               viper.GetString(name + "_mysql_database_name"),
		AllowNativePasswords: true,
		Params:               map[string]string{"charset": "utf8mb4"},
		Loc:                  time.Local,
	}
}

// GetEngine 根据名字获取 *xorm.Engine
func (mg *EngineMgrImp) GetEngine(name string) (*xorm.Engine, error) {
	eng, err := mg.GetMyEngine(name)
	if err != nil {
		return nil, err
	}
	return eng, nil
}

// GetMyEngine 根据名字获取 *xorm.Engine
func (mg *EngineMgrImp) GetMyEngine(name string) (*xorm.Engine, error) {
	v, ok := mg.engines.Load(name)
	if ok {
		eng := v.(*xorm.Engine)
		err := eng.Ping()
		if err != nil {
			logrus.Warnf("数据库Ping失败:db=%s,err=%v", name, err)
			mg.engines.Delete(name)
			mg.closeCh <- eng
			eng = nil
		}
		if eng != nil {
			return eng, nil
		}
	}
	conf := mg.GetMysqlConfig(name)

	engine, err := mg.createEngine(&conf)
	if err != nil {
		logrus.Errorf("数据库创建失败:db=%s,err=%v", name, err)
		return nil, err
	}
	actual, loaded := mg.engines.LoadOrStore(name, engine)
	if loaded {
		engine.Close()
	}
	eng := actual.(*xorm.Engine)
	if eng == nil {
		logrus.Errorf("数据库生成失败:db=%s", name)
		return nil, fmt.Errorf("数据库生成失败:db=%s", name)
	}

	if err = eng.Ping(); err != nil {
		logrus.Errorf("数据库Ping失败:db=%s,err=%v", name, err)
		return nil, fmt.Errorf("数据库Ping失败:db=%s,err=%v", name, err)
	}
	return eng, nil
}

func (mg *EngineMgrImp) createEngine(conf *mysql.Config) (*xorm.Engine, error) {
	dns := conf.FormatDSN()
	engine, err := xorm.NewEngine("mysql", dns)
	if err != nil {
		logrus.Panicf("新建Mysql引擎失败:db=%s,err=%v", conf.DBName, err)
		return nil, fmt.Errorf("新建Mysql引擎失败:db=%s,err=%v", conf.DBName, err)
	}

	if err := engine.Ping(); err != nil {
		engine.Close()
		logrus.Errorf("数据库新建连接失败:db=%s,err=%v", conf.DBName, err)
		return nil, fmt.Errorf("数据库新建连接失败:db=%s,err=%v", conf.DBName, err)
	}

	// 是目前比较合适的值，详见../benchmarks/main_test.go的结果
	engine.SetMaxIdleConns(5)
	engine.SetMaxOpenConns(10)
	engine.SetConnMaxLifetime(0)

	engine.SetLogLevel(core.LOG_WARNING)

	writer := errWriter{}
	writer.Init()
	engine.SetErrLoggerOutPut(&writer)

	return engine, nil
}

// GetMyisamEngineSession 返回MYISAM的session
func GetMyisamEngineSession(name string) (engine *xorm.Engine, session *xorm.Session, err error) {
	engine, err = GetDefaultMgr().GetEngine(name)
	if err != nil {
		logrus.Errorf("failed to get myisam engine session err = %v", err.Error())
		return
	}
	session = engine.StoreEngine("myisam")
	return
}

// GetMysqlTableEngine 根据dbName和tableName获取数据库Session
func GetMysqlTableEngine(DbName string, tableName string) *xorm.Session {
	return GetMysqlEngine(DbName).Table(tableName)
}